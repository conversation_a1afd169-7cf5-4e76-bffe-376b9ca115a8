<theme-wrap vue-id="eab4ae3c-1" class="data-v-290d40f7" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content"><u-sticky vue-id="{{('eab4ae3c-2')+','+('eab4ae3c-1')}}" offset-top="0" class="data-v-290d40f7" bind:__l="__l" vue-slots="{{['default']}}"><view class="bg-fff data-v-290d40f7"><view class="u-flex u-row-between u-p-b-10 u-border-bottom data-v-290d40f7"><view class="w-100 u-flex-1 u-border-right data-v-290d40f7"><picker class="w-100 data-v-290d40f7" mode="date" end="{{maxDate}}" value="{{maxDate}}" data-event-opts="{{[['change',[['changeDate',['$event']]]]]}}" bindchange="__e"><view class="u-tips-color u-flex w-100 u-row-center u-p-10 data-v-290d40f7"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-line-1 u-p-r-10 data-v-290d40f7" bindtap="__e">{{''+(queryForm.classTime||"开始时间")}}</view><block wx:if="{{!queryForm.classTime}}"><u-icon vue-id="{{('eab4ae3c-3')+','+('eab4ae3c-2')}}" name="calendar" color="#999" size="18" class="data-v-290d40f7" bind:__l="__l"></u-icon></block><block wx:else><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" catchtap="__e" class="data-v-290d40f7"><u-icon vue-id="{{('eab4ae3c-4')+','+('eab4ae3c-2')}}" name="close-circle-fill" color="#999" size="18" class="data-v-290d40f7" bind:__l="__l"></u-icon></view></block></view></picker></view><view class="u-flex-1 u-p-r-10 data-v-290d40f7"><picker class="w-100 data-v-290d40f7" range="{{coachList}}" range-key="nickName" data-event-opts="{{[['change',[['changeCoach',['$event']]]]]}}" bindchange="__e"><view class="u-tips-color u-flex w-100 u-row-center u-p-10 no-wrap data-v-290d40f7"><view class="u-line-1 u-p-r-10 data-v-290d40f7">{{''+(nickName||"教练选择")}}</view><block wx:if="{{!nickName}}"><u-icon vue-id="{{('eab4ae3c-5')+','+('eab4ae3c-2')}}" name="list" color="#999" size="18" class="data-v-290d40f7" bind:__l="__l"></u-icon></block><block wx:else><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" catchtap="__e" class="data-v-290d40f7"><u-icon vue-id="{{('eab4ae3c-6')+','+('eab4ae3c-2')}}" name="close-circle-fill" color="#999" size="18" class="data-v-290d40f7" bind:__l="__l"></u-icon></view></block></view></picker></view></view></view></u-sticky><view class="container u-p-t-40 u-p-b-40 data-v-290d40f7"><block wx:if="{{$root.g0}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-290d40f7"><view class="u-flex u-col-center u-row-start data-v-290d40f7" style="flex-wrap:no-wrap;overflow:hidden;"><view class="overflow-hidden flex-0 border-16 data-v-290d40f7" style="width:140rpx;height:140rpx;line-height:0;"><image class="h-100 data-v-290d40f7" src="{{item.banner}}" mode="heightFix"></image></view><view class="w-100 u-p-l-20 data-v-290d40f7"><view class="u-line-1 w-100 data-v-290d40f7">{{''+item.title+''}}</view><view class="u-flex u-tips-color u-font-26 u-p-t-10 u-p-b-10 text-no-wrap data-v-290d40f7"><view class="u-p-r-20 data-v-290d40f7">{{"总人数："+(item.attendance||0)}}</view><view class="data-v-290d40f7">{{"剩余："+(item.remainder||0)}}</view></view><view class="u-tips-color u-font-26 data-v-290d40f7">{{'开课时间：'+(item.classTime||"")+''}}</view></view></view><view class="btn-wrap data-v-290d40f7"><block wx:if="{{!isAdmin}}"><navigator class="u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 border-8 btc text-no-wrap lbc u-font-26 font-bold data-v-290d40f7" url="{{'/pages-admin/tuanKeGuanLi/check?courseId='+item.groupCourseId}}">查看</navigator></block></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-290d40f7"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-290d40f7"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-290d40f7">暂无课程</view></view></block></view></view></theme-wrap>