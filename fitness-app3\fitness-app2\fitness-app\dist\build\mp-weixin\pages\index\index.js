(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/index/index"],{25038:function(){},51711:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return c.__esModule},default:function(){return d}});var o,r={uToast:function(){return n.e("node-modules/uview-ui/components/u-toast/u-toast").then(n.bind(n,67559))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(n,78278))},uLine:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-line/u-line")]).then(n.bind(n,84916))},uReadMore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-read-more/u-read-more")]).then(n.bind(n,32789))},uSwiper:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-swiper/u-swiper")]).then(n.bind(n,84578))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("8dd740cc-1")),o=n&&t.isAdmin?t.$getSSP("8dd740cc-1","content"):null,r=n&&t.isAdmin?t.$getSSP("8dd740cc-1","content"):null,i=n?t._f("Img")(t.detail.logo):null,a=n?t.infoImgList.length:null,c=n&&a?t.infoImgList.length:null,u=!n||!a||c>1?null:t._f("Img")(t.infoImgList[0]),s=n?t.$getSSP("8dd740cc-1","content"):null,l=n?t.$getSSP("8dd740cc-1","content"):null,h=n?t.__map(t.jiaoLianList,(function(e,n){var o=t.__get_orig(e),r=e.coachAvatar?t._f("Img")(e.coachAvatar):null;return{$orig:o,f2:r}})):null,f=n?t.__map(t.huiYuanKaList,(function(e,n){var o=t.__get_orig(e),r=t.__map(e.list,(function(e,n){var o=t.__get_orig(e),r=t._f("Img")(e.cover);return{$orig:o,f3:r}}));return{$orig:o,l1:r}})):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:o,m2:r,f0:i,g0:a,g1:c,f1:u,m3:s,m4:l,l0:h,l2:f}})},a=[],c=n(90011),u=c["default"],s=n(25038),l=n.n(s),h=(l(),n(18535)),f=(0,h["default"])(u,i,a,!1,null,null,null,!1,r,o),d=f.exports},81456:function(t,e,n){"use strict";var o=n(51372)["default"],r=n(81715)["createPage"];n(96910);a(n(923));var i=a(n(51711));function a(t){return t&&t.__esModule?t:{default:t}}o.__webpack_require_UNI_MP_PLUGIN__=n,r(i.default)},90011:function(t,e,n){"use strict";var o=n(81715)["default"],r=n(51372)["default"];function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;n(77020);var a=c(n(68466));function c(t){return t&&t.__esModule?t:{default:t}}function u(t,e,n){return(e=s(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function s(t){var e=l(t,"string");return"symbol"==i(e)?e:e+""}function l(t,e){if("object"!=i(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=i(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},n=Object.prototype,o=n.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function f(t,e,n,o){var i=e&&e.prototype instanceof w?e:w,a=Object.create(i.prototype),c=new N(o||[]);return r(a,"_invoke",{value:E(t,n,c)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var g="suspendedStart",p="suspendedYield",m="executing",v="completed",y={};function w(){}function L(){}function b(){}var S={};l(S,c,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(O([])));x&&x!==n&&o.call(x,c)&&(S=x);var I=b.prototype=w.prototype=Object.create(S);function P(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function T(t,e){function n(r,a,c,u){var s=d(t[r],t,a);if("throw"!==s.type){var l=s.arg,h=l.value;return h&&"object"==i(h)&&o.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,c,u)}),(function(t){n("throw",t,c,u)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return n("throw",t,c,u)}))}u(s.arg)}var a;r(this,"_invoke",{value:function(t,o){function r(){return new e((function(e,r){n(t,o,e,r)}))}return a=a?a.then(r,r):r()}})}function E(e,n,o){var r=g;return function(i,a){if(r===m)throw Error("Generator is already running");if(r===v){if("throw"===i)throw a;return{value:t,done:!0}}for(o.method=i,o.arg=a;;){var c=o.delegate;if(c){var u=j(c,o);if(u){if(u===y)continue;return u}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===g)throw r=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=m;var s=d(e,n,o);if("normal"===s.type){if(r=o.done?v:p,s.arg===y)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(r=v,o.method="throw",o.arg=s.arg)}}}function j(e,n){var o=n.method,r=e.iterator[o];if(r===t)return n.delegate=null,"throw"===o&&e.iterator.return&&(n.method="return",n.arg=t,j(e,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var i=d(r,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function G(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function O(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function n(){for(;++r<e.length;)if(o.call(e,r))return n.value=e[r],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return L.prototype=b,r(I,"constructor",{value:b,configurable:!0}),r(b,"constructor",{value:L,configurable:!0}),L.displayName=l(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,l(t,s,"GeneratorFunction")),t.prototype=Object.create(I),t},e.awrap=function(t){return{__await:t}},P(T.prototype),l(T.prototype,u,(function(){return this})),e.AsyncIterator=T,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new T(f(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},P(I),l(I,s,"Generator"),l(I,c,(function(){return this})),l(I,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=O,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(G),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(o,r){return c.type="throw",c.arg=e,n.next=o,r&&(n.method="next",n.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var u=o.call(a,"catchLoc"),s=o.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),G(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;G(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,o){return this.delegate={iterator:O(e),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function f(t,e,n,o,r,i,a){try{var c=t[i](a),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(o,r)}function d(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){f(i,o,r,a,c,"next",t)}function c(t){f(i,o,r,a,c,"throw",t)}a(void 0)}))}}var g=function(){n.e("components/privacy-popup/privacy-popup").then(function(){return resolve(n(57596))}.bind(null,n))["catch"](n.oe)},p=function(){n.e("components/changGuan/index").then(function(){return resolve(n(11985))}.bind(null,n))["catch"](n.oe)},m=function(){n.e("components/zw-tabbar/zw-tabbar").then(function(){return resolve(n(74220))}.bind(null,n))["catch"](n.oe)};e["default"]={name:"home",components:{privacy:g,changGuan:p,zwTabBar:m},data:function(){return{huodonglist:[],shopList:[],shopId:"",x:"",y:"",infoImgList:[],huiYuanKaList:[],jiaoLianList:[],isAdmin:!1,detail:{shopIntroduce:""},startY:0,scrollHeight:0}},onLoad:function(){var t=this;a.default.getRouter({data:{},methods:"POST"}).then((function(e){0==e.data.length?t.isAdmin=!1:(o.setStorageSync("userRouter",e.data),t.isAdmin=!0)})),o.showLoading({mask:!0,title:"正在加载中……"}),this.getLocation()},onPageScroll:function(t){this.scrollHeight=t.scrollTop},onShow:function(){var t=this;return d(h().mark((function e(){var n;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log(1231231),e.next=3,a.default["getActivity"]({data:{pageNum:1,pageSize:999}});case 3:n=e.sent,t.huodonglist=n.rows;case 5:case"end":return e.stop()}}),e)})))()},methods:u(u({showChangGuanSelector:function(){this.$refs.changGuan&&this.$refs.changGuan.clickshow()},onTouchStart:function(t){this.startY=t.changedTouches[0].clientY},onTouchMove:function(t){var e=t.changedTouches[0].clientY;e-this.startY>200&&0==this.scrollHeight&&this.$refs.changGuan.clickshow()},onTouchEnd:function(t){},jump:function(t){o.navigateTo({url:"/pages-admin/huodongGuanli/huodongindex?id=".concat(t.id)})},returnCardName:function(t){switch(t){case"1":return"会员卡";case"2":return"私教课";default:return"其他类型"}},showRefuseLocationPermission:function(){var t=this;r.showModal({title:"提示",content:"需要打开定位设置，否则影响体验",confirmText:"前往设置",showCancel:!1,success:function(e){e.confirm&&o.openSetting({success:function(e){console.log("打开设置成功",e),e.authSetting["scope.userLocation"]?(console.log("成功授权userLocation"),t.positioning()):(console.log("用户未授权userLocation"),t.showRefuseLocationPermission())},fail:function(t){console.log("打开设置失败",t)}})}})},getLocation:function(){var t=this;o.getLocation({type:"gcj02",geocode:!0,success:function(e){console.log(e.longitude,e.latitude,"resLocation",""),t.positioning()},fail:function(e){console.log(e),t.showRefuseLocationPermission()}})},positioning:function(){var t=this;o.getLocation({type:"wgs84",success:function(e){t.x=e.longitude,t.y=e.latitude,t.getShopList()},fail:function(t){console.log("失败",t)},complete:function(){o.hideLoading()}})},getShopList:function(){var t=this;o.setStorageSync("longitude",this.x),o.setStorageSync("latitude",this.y),a.default.getShopListForDistance({data:{distance:1e5,companyId:1,longitude:this.x,latitude:this.y}}).then((function(e){console.log("获取场馆列表"),200==e.code&&e.rows.length>0&&(t.shopList=e.rows,t.shopId=e.rows[0].shopId,o.setStorageSync("nowShopId",e.rows[0].shopId),o.setStorageSync("nowShopName",e.rows[0].shopName),t.getShopDetail(),e.rows.length>1&&(console.log("获取到多个场馆,打开选择场馆"),t.$refs.changGuan.clickshow()))}))},getShopDetail:function(){var t=this;a.default.getShopDetail({shopId:this.shopId}).then((function(e){if(200==e.code){if(console.log("场馆详情：",e.data),e.data.shopImages){for(var n,r=null===(n=e.data.shopImages)||void 0===n?void 0:n.split(","),i=[],a=0;a<r.length;a++)i.push({src:t.$serverUrl+r[a]});t.infoImgList=i,console.log(t.infoImgList,"轮播图")}t.detail=e.data,o.setStorageSync("companyId",t.detail.companyId),t.getCardList(),t.getcoachList()}})).catch((function(t){}))},getCardList:function(){var t=this;a.default.getUserCardList({shopId:this.shopId}).then((function(e){if(200==e.code){var n=e.rows.reduce((function(e,n,o,r){var i=t.returnCardName(n.cardType);return e[i]?e[i].push(n):e[i]=[n],e}),[]);console.log(n);var o=[];for(var r in n)o.push({list:n[r],type_text:r});console.log(o),t.huiYuanKaList=o}}))},getcoachList:function(){var t=this;a.default.getcoachList({data:{shopId:this.shopId,companyId:1}}).then((function(e){200==e.code&&(t.jiaoLianList=e.rows)}))},toQrcodePage:function(){o.navigateTo({url:"/pages/ruChang/qrcode"})},gotoAdmin:function(){o.navigateTo({url:"/pages-admin/admin/index/index"})},toHuiYuanKa:function(t){o.navigateTo({url:"/pages/huiYuanKa/detail?id=".concat(t)})},rawPrice:function(t){if(t>0){var e=Math.floor(t/.8);return e<t?t:e}return t},toChangGuanPage:function(){o.showLoading({mask:!0,title:"加载中……"});var t=this;o.getSetting({success:function(t){console.log(t),t.authSetting["scope.userLocation"]?(o.hideLoading(),o.navigateTo({url:"/pages/changGuan/index"})):o.authorize({scope:"scope.userLocation",success:function(){o.hideLoading(),o.navigateTo({url:"/pages/changGuan/index"})}})},fail:function(){o.hideLoading(),t.$u.toast("请先给予定位权限！")}})},toJiaoLianDetail:function(t){o.navigateTo({url:"/pages/jiaoLian/detail?id=".concat(t.memberId,"&name=").concat(t.nickName)})},openMap:function(){r.openLocation({latitude:this.detail.latitude,longitude:this.detail.longitude,scale:18})},callPhone:function(){r.makePhoneCall({phoneNumber:this.detail.phone})},previewImage:function(t){console.log(t),o.previewImage({urls:this.infoImgList,current:t})},changeChangGuan:function(t){this.$refs.changGuan.clickhide(),this.$refs.uToast.show({message:"切换场馆成功",type:"success",duration:1e3});var e=this.shopList[t];o.setStorageSync("nowShopId",e.shopId),o.setStorageSync("nowShopName",e.shopName),this.shopId=e.shopId,this.getShopDetail()}},"onTouchStart",(function(t){this.startY=t.changedTouches[0].clientY})),"onTouchMove",(function(t){var e=t.changedTouches[0].clientY;e-this.startY>200&&0==this.scrollHeight&&this.$refs.changGuan.clickshow()}))}}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(81456)}));t.O()}]);