require("../../common/vendor.js"),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages-baobiao/huiYuanGuanLi/huiYuanBaoBiao/index"],{13484:function(e,t,n){"use strict";n.r(t),n.d(t,{__esModule:function(){return s.__esModule},default:function(){return d}});var o,a={uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(n,43399))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(n,78278))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.$hasSSP("2b1cb2b0-1")),o=n?e.$getSSP("2b1cb2b0-1","content"):null;e._isMounted||(e.e0=function(t){e.pickStartShow=!0},e.e1=function(t){e.pickEndShow=!0}),e.$mp.data=Object.assign({},{$root:{m0:n,m1:o}})},r=[],s=n(78533),u=s["default"],c=n(52349),l=n.n(c),p=(l(),n(18535)),m=(0,p["default"])(u,i,r,!1,null,"232913c3",null,!1,a,o),d=m.exports},52349:function(){},78533:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var o=a(n(68466));function a(e){return e&&e.__esModule?e:{default:e}}var i=function(){Promise.all([n.e("pages-baobiao/common/vendor"),n.e("pages-baobiao/ui-echarts/components/ui-echarts/ui-echarts")]).then(function(){return resolve(n(90072))}.bind(null,n))["catch"](n.oe)};t["default"]={components:{echart:i},data:function(){return{pickStartShow:!1,pickEndShow:!1,range:[],shopName:"",form:{reportType:"1",companyId:1,startDate:"",endDate:"",reportDate:"",pageNum:1,pageSize:10},maxDate:"",minDate:"",current:0,companyList:[],chartType:"line",tabList:[{name:"日结",value:"1",type:"line"},{name:"周结",value:"2",type:"bar"},{name:"月结",value:"3",type:"bar"},{name:"年结",value:"4",type:"bar"}],image:null,option:{}}},onLoad:function(){var e=this;this.maxDate=new Date(+Date.now()+288e5).toISOString().split("T")[0],o.default.getShopList({data:{companyId:1}}).then((function(t){console.log(t,"获取场馆列表"),200==t.code&&(e.companyList=t.rows||[],e.companyList.unshift({shopName:"全部",companyId:1}),e.getReport())}))},methods:{toImage:function(){var e,t=this;null===(e=this.$refs)||void 0===e||e.chart.toImageFile({success:function(e){e.tempFilePath;var n=e.base64;t.image=n}})},changeDate:function(e,t){this.form[t]=e.detail.value,this.getReport()},changeCompany:function(e){console.log(e)},pickerConfirmHandler:function(e){console.log(e),this.form.startDate=new Date(e.value+288e5).toISOString().split("T")[0],this.pickStartShow=!1},getReport:function(){var e=this;o.default.getReportList({data:this.form}).then((function(t){console.log(t,"获取报表"),e.allData=t.rows;for(var n=[],o=[],a=[],i=0;i<t.rows.length;i++)t.rows.forEach((function(e){n.push(e.startDate),o.push(e.registerCount),a.push([e.startDate.substr(0,10),e.registerCount])}));e.option={tooltip:{trigger:"item"},legend:{orient:"vertical",bottom:"bottom",itemWidth:8,itemHeight:8,itemGap:4},grid:{right:"5%"},dataset:[{dimensions:["name","score"],source:a},{transform:{type:"sort",config:{dimension:"score",order:"desc"}}}],xAxis:{type:"category",axisLabel:{interval:0,rotate:"line"==e.chartType?30:""}},yAxis:{},series:{type:e.chartType,encode:{x:"name",y:"score"},datasetIndex:1}}}))},changeTabs:function(e){console.log(e),this.form.reportType=e.value,this.chartType=e.type,this.getReport()}}}},98550:function(e,t,n){"use strict";var o=n(51372)["default"],a=n(81715)["createPage"];n(96910);r(n(923));var i=r(n(13484));function r(e){return e&&e.__esModule?e:{default:e}}o.__webpack_require_UNI_MP_PLUGIN__=n,a(i.default)}},function(e){var t=function(t){return e(e.s=t)};e.O(0,["common/vendor"],(function(){return t(98550)}));e.O()}]);