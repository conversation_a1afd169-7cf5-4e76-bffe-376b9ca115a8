<view class="page"><u-no-network vue-id="83a6bf28-1" bind:__l="__l"></u-no-network><u-toast class="vue-ref" vue-id="83a6bf28-2" data-ref="uToast" bind:__l="__l"></u-toast><u-notify class="vue-ref" vue-id="83a6bf28-3" data-ref="uNotify" bind:__l="__l"></u-notify><block wx:if="{{loading}}"><view class="loading"><u-loading-page vue-id="83a6bf28-4" loading="{{loading}}" bind:__l="__l"></u-loading-page></view></block><block wx:else><view class="content"><view class="authorize-page"><view class="logo"><u--image vue-id="83a6bf28-5" src="@/static/images/logo.png" mode="widthFix" width="150rpx" fade="{{true}}" duration="1000" bind:__l="__l"></u--image></view><view class="auth-title">授权登录</view><view class="auth-info">获取您的头像、昵称、地区及性别信息</view><view class="auth-btn"><u-button vue-id="83a6bf28-6" type="warning" ripple="{{true}}" loading="{{btnLoading}}" data-event-opts="{{[['^click',[['authorize']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">授权登录</u-button></view><view class="auth-btn"><u-button vue-id="83a6bf28-7" type="default" ripple="{{true}}" plain="{{true}}" data-event-opts="{{[['^click',[['goBack']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">暂不登录</u-button></view></view></view></block></view>