/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/layout/theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-7a7df696, .theme-color.u-content-color.data-v-7a7df696 {
  color: var(--base-color);
}
.theme-bg-color.data-v-7a7df696, .bgc.data-v-7a7df696 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-7a7df696, .nbc.data-v-7a7df696 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-7a7df696, .bc.data-v-7a7df696 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-7a7df696, .lbc.data-v-7a7df696 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-7a7df696, .btc.data-v-7a7df696 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-7a7df696, .ltc.data-v-7a7df696 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-7a7df696 {
  background: var(--scroll-item-bg-color);
}
.theme-wrap.data-v-7a7df696 {
  min-height: 100vh;
  width: 100vw;
  background: var(--base-bg-color);
}
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages-admin/siJiaoGuanLi/keChengFenLei/details.vue?vue&type=style&index=0&id=1b526d54&scoped=true&lang=scss& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-1b526d54, .theme-color.u-content-color.data-v-1b526d54 {
  color: var(--base-color);
}
.theme-bg-color.data-v-1b526d54, .bgc.data-v-1b526d54 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-1b526d54, .nbc.data-v-1b526d54 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-1b526d54, .bc.data-v-1b526d54 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-1b526d54, .lbc.data-v-1b526d54 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-1b526d54, .btc.data-v-1b526d54 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-1b526d54, .ltc.data-v-1b526d54 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-1b526d54 {
  background: var(--scroll-item-bg-color);
}
.contView.data-v-1b526d54 {
  display: flex;
  flex-wrap: wrap;
}
.contView .user_avatar.data-v-1b526d54 {
  width: 20%;
  height: 120rpx;
  margin-top: 30rpx;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}
.contView .user_avatar .w-100.data-v-1b526d54 {
  overflow: hidden;
}
.contView.data-v-1b526d54  .u-checkbox-group--column {
  width: 100%;
}
.contView.data-v-1b526d54  .u-radio {
  width: 100%;
}
.formView .formTextarea.data-v-1b526d54 {
  border: 2rpx solid #e6e6e6;
  margin-bottom: 20rpx;
}
.formView .formTextarea .textareaTitle.data-v-1b526d54 {
  border-radius: 8rpx 8rpx 0 0;
  padding: 10rpx 30rpx;
  background-color: #e6e6e6;
}
.formView .formTextarea .formLogo.data-v-1b526d54 {
  margin: 20rpx;
  flex-wrap: wrap;
}
.formView .formTextarea .formLogo .imgView.data-v-1b526d54 {
  width: 200rpx;
  height: 120rpx;
  position: relative;
  margin-right: 10rpx;
  margin-bottom: 20rpx;
}
.formView .formTextarea .formLogo .imgView.data-v-1b526d54  .u-icon--right {
  position: absolute;
  z-index: 999;
  top: -10rpx;
  right: -10rpx;
}
.formView .formList.data-v-1b526d54 {
  background-color: #fafafa;
  display: flex;
  align-items: center;
  flex-direction: row;
  box-sizing: border-box;
  padding: 10rpx 30rpx;
  font-size: 30rpx;
  color: #303133;
  align-items: center;
  border: 2rpx solid #d6d7d9;
  margin-bottom: 20rpx;
}
.bottonBtn.data-v-1b526d54 {
  height: 160rpx;
  width: 750rpx;
  position: fixed;
  bottom: 0;
  border-top: 1px solid black;
}
.bottonBtn .moreBtn.data-v-1b526d54,
.bottonBtn .addHuiYuan.data-v-1b526d54 {
  width: 300rpx;
  height: 80rpx;
  margin: 0 auto;
  text-align: center;
  line-height: 80rpx;
  border: 1px solid;
  border-radius: 40rpx;
}
