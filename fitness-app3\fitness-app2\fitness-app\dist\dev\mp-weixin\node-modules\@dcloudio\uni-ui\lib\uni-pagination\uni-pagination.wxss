/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node_modules/@dcloudio/uni-ui/lib/uni-pagination/uni-pagination.vue?vue&type=style&index=0&id=2a2fdb66&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-2a2fdb66, .theme-color.u-content-color.data-v-2a2fdb66 {
  color: var(--base-color);
}
.theme-bg-color.data-v-2a2fdb66, .bgc.data-v-2a2fdb66 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-2a2fdb66, .nbc.data-v-2a2fdb66 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-2a2fdb66, .bc.data-v-2a2fdb66 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-2a2fdb66, .lbc.data-v-2a2fdb66 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-2a2fdb66, .btc.data-v-2a2fdb66 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-2a2fdb66, .ltc.data-v-2a2fdb66 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-2a2fdb66 {
  background: var(--scroll-item-bg-color);
}
.uni-pagination.data-v-2a2fdb66 {
  display: flex;
  position: relative;
  overflow: hidden;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.uni-pagination__total.data-v-2a2fdb66 {
  font-size: 14px;
  color: #999;
  margin-right: 15px;
}
.uni-pagination__btn.data-v-2a2fdb66 {
  display: flex;
  cursor: pointer;
  padding: 0 8px;
  line-height: 30px;
  font-size: 12px;
  position: relative;
  background-color: #F0F0F0;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 5px;
}
.uni-pagination__child-btn.data-v-2a2fdb66 {
  display: flex;
  font-size: 12px;
  position: relative;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #666;
  font-size: 12px;
}
.uni-pagination__num.data-v-2a2fdb66 {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
  color: #666;
  margin: 0 5px;
}
.uni-pagination__num-tag.data-v-2a2fdb66 {
  margin: 0 5px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  color: #999;
  border-radius: 4px;
}
.uni-pagination__num-current.data-v-2a2fdb66 {
  display: flex;
  flex-direction: row;
}
.uni-pagination__num-current-text.data-v-2a2fdb66 {
  font-size: 15px;
}
.current-index-text.data-v-2a2fdb66 {
  color: #2979ff;
}
.uni-pagination--enabled.data-v-2a2fdb66 {
  color: #333333;
  opacity: 1;
}
.uni-pagination--disabled.data-v-2a2fdb66 {
  opacity: 0.5;
}
.uni-pagination--hover.data-v-2a2fdb66 {
  color: rgba(0, 0, 0, 0.6);
  background-color: #eee;
}
.tag--active.data-v-2a2fdb66:hover {
  color: #2979ff;
}
.page--active.data-v-2a2fdb66 {
  color: #fff;
  background-color: #2979ff;
}
.page--active.data-v-2a2fdb66:hover {
  color: #fff;
}
.is-pc-hide.data-v-2a2fdb66 {
  display: block;
}
.is-phone-hide.data-v-2a2fdb66 {
  display: none;
}
@media screen and (min-width: 450px) {
.is-pc-hide.data-v-2a2fdb66 {
    display: none;
}
.is-phone-hide.data-v-2a2fdb66 {
    display: block;
}
.uni-pagination__num-flex-none.data-v-2a2fdb66 {
    flex: none;
}
}
