<theme-wrap scoped-slots-compiler="augmented" vue-id="33010177-1" class="data-v-1c2f2bae" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-1c2f2bae" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('33010177-2')+','+('33010177-1')}}" title="课程详情" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-1c2f2bae" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-1c2f2bae"><view class="formView data-v-1c2f2bae"><view class="formList data-v-1c2f2bae"><view class="data-v-1c2f2bae">教练类型名称:</view><u--input bind:input="__e" vue-id="{{('33010177-3')+','+('33010177-1')}}" border="{{false}}" value="{{formList.coachTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','coachTypeName','$event',[]],['formList']]]]]}}" class="data-v-1c2f2bae" bind:__l="__l"></u--input></view></view></view><view class="bottonBtn u-flex data-v-1c2f2bae"><view data-event-opts="{{[['tap',[['deleteTrainer']]]]}}" class="moreBtn data-v-1c2f2bae" style="{{'color:'+($root.m3['buttonLightBgColor'])+';'+('border-color:'+($root.m4['buttonLightBgColor'])+';')}}" bindtap="__e">删除类型</view><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="addHuiYuan data-v-1c2f2bae" style="{{'background:'+($root.m5['buttonLightBgColor'])+';'+('color:'+($root.m6['buttonTextColor'])+';')+('border-color:'+($root.m7['buttonLightBgColor'])+';')}}" bindtap="__e">修改类型</view></view></view></theme-wrap>