(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-navbar/u-navbar"],{2439:function(e,t,n){"use strict";var u=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var i=a(n(77952));function a(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-navbar",mixins:[u.$u.mpMixin,u.$u.mixin,i.default],data:function(){return{}},methods:{leftClick:function(){this.$emit("leftClick"),this.autoBack&&u.navigateBack()},rightClick:function(){this.$emit("rightClick")}}}},2982:function(){},66372:function(e,t,n){"use strict";n.r(t),n.d(t,{__esModule:function(){return l.__esModule},default:function(){return m}});var u,i={uStatusBar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-status-bar/u-status-bar")]).then(n.bind(n,51401))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(n,78278))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.fixed&&e.placeholder?e.$u.addUnit(e.$u.getPx(e.height)+e.$u.sys().statusBarHeight,"px"):null),u=e.$u.addUnit(e.height),i=e.__get_style([{width:e.$u.addUnit(e.titleWidth)},e.$u.addStyle(e.titleStyle)]);e.$mp.data=Object.assign({},{$root:{g0:n,g1:u,s0:i}})},o=[],l=n(2439),s=l["default"],c=n(2982),d=n.n(c),r=(d(),n(18535)),f=(0,r["default"])(s,a,o,!1,null,"91358f36",null,!1,i,u),m=f.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-navbar/u-navbar-create-component"],{},function(e){e("81715")["createComponent"](e(66372))}]);