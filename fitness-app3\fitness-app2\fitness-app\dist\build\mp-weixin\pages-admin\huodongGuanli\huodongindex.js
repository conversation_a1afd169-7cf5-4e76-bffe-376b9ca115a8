(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages-admin/huodongGuanli/huodongindex"],{1697:function(t,e,n){"use strict";var r=n(81715)["default"];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=i(n(68466));function i(t){return t&&t.__esModule?t:{default:t}}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,i=Object.create(o.prototype),c=new j(r||[]);return a(i,"_invoke",{value:k(t,n,c)}),i}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",m="suspendedYield",y="executing",g="completed",v={};function w(){}function b(){}function x(){}var L={};f(L,u,(function(){return this}));var S=Object.getPrototypeOf,_=S&&S(S(N([])));_&&_!==n&&r.call(_,u)&&(L=_);var I=x.prototype=w.prototype=Object.create(L);function P(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(a,i,c,u){var s=h(t[a],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,c,u)}),(function(t){n("throw",t,c,u)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return n("throw",t,c,u)}))}u(s.arg)}var i;a(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function k(e,n,r){var o=p;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:t,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var u=O(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=y;var s=h(e,n,r);if("normal"===s.type){if(o=r.done?g:m,s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=g,r.method="throw",r.arg=s.arg)}}}function O(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,O(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=h(o,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var i=a.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function N(e){if(e||""===e){var n=e[u];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=x,a(I,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,l,"GeneratorFunction")),t.prototype=Object.create(I),t},e.awrap=function(t){return{__await:t}},P(E.prototype),f(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new E(d(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},P(I),f(I,l,"Generator"),f(I,u,(function(){return this})),f(I,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=N,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),D(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;D(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:N(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function u(t,e,n,r,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(r,o)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(t){u(a,r,o,i,c,"next",t)}function c(t){u(a,r,o,i,c,"throw",t)}i(void 0)}))}}var l=function(){n.e("components/official-qrcode").then(function(){return resolve(n(65602))}.bind(null,n))["catch"](n.oe)};e["default"]={components:{officialQrcode:l},data:function(){return{showQrcode:!1,modelshow:!1,getActivityRecordInfos:null,EnablePtList:[],getEnablePtListlen:"",background:["color1","color2","color3"],indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,data:"",currentPage:1,total:1,list:[],limit:100,type:"",timeData:{days:0,hours:0,minutes:0,seconds:0},imgs:[],getNewOrderLists:[],timeDifference:"",wxUserInfo:"",getCommissionList:[]}},onLoad:function(t){var e=this;return s(c().mark((function n(){var o,i,u,s,l,f,d,h,p,m,y,g,v,w,b;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,a.default["getactivityPt1"]({data:{id:t.id}});case 2:return o=n.sent,e.data=o.data,e.imgs=e.data.infoPic.split(","),i={},i=t.memberId?{activityId:e.data.id,type:"share",prevMemberId:t.memberId}:{activityId:e.data.id,type:"read"},n.next=9,a.default["setActivityPtRecord"]({data:i});case 9:return n.sent,n.next=12,a.default["getEnablePtList"]({data:{activityId:e.data.id}});case 12:return u=n.sent,e.getEnablePtListlen=u.data.length,e.EnablePtList=e.chunkArray(u.data),n.next=17,a.default["getNewOrderList"]({data:{activityId:e.data.id,num:999}});case 17:return s=n.sent,l=[],l=s.data.length>6?s.data.sort((function(){return Math.random()-.5})).slice(0,Math.floor(s.data.length/2)):s.data,e.getNewOrderLists=e.chunkArray(l),n.next=23,a.default["getActivityRecordInfo"]({data:{activityId:e.data.id}});case 23:return f=n.sent,e.getActivityRecordInfos=f.data,e.timeData={days:e.calculateDaysBetween(e.data.startTime,e.data.endTime),hours:23,minutes:59,seconds:59},d=new Date(e.data.startTime),h=new Date,p=h.getFullYear(),m=h.getMonth()+1,y=h.getDate(),m=m<10?"0"+m:m,y=y<10?"0"+y:y,g="".concat(p,"-").concat(m,"-").concat(y),v=new Date(g),w=v.getTime()-d.getTime(),e.timeDifference=w,e.wxUserInfo=r.getStorageSync("wxUserInfo"),n.next=40,a.default["getCommissionList"]({data:{activityId:e.data.id}});case 40:b=n.sent,e.getCommissionList=b.data,console.log(b,"getCommissionListgetCommissionList");case 43:case"end":return n.stop()}}),n)})))()},onShow:function(){},onReachBottom:function(){},methods:{onOfficialClose:function(){this.showQrcode=!1,a.default.getInfo({data:{companyId:1},method:"GET"}).then((function(t){200==t.code&&r.setStorageSync("wxUserInfo",t.wxUser)}))},cloces:function(){this.modelshow=!1},calculateDaysBetween:function(t,e){var n=new Date(t),r=new Date(e),o=r-n,a=o/864e5;return Math.floor(a)},gow:function(t){var e=this;a.default.createOrder({data:{activityId:this.data.id,ptId:t.ptInfoId}}).then((function(t){200==t.code&&(console.log(t),r.requestPayment({provider:"weixin",nonceStr:t.data.prepay.nonceStr,package:t.data.prepay.packageVal,paySign:t.data.prepay.paySign,signType:t.data.prepay.signType,timeStamp:t.data.prepay.timeStamp+"",success:function(t){console.log(t),setTimeout((function(){e.$u.toast("支付成功！"),r.navigateBack()}),2e3)},fail:function(t){console.log(t),e.$u.toast("支付失败！"),e.disabled=!1}}))}))},chunkArray:function(t){if(t.length<=2)return[t];for(var e=[],n=0;n<t.length;n+=2)e.push(t.slice(n,n+2));return e},onShareAppMessage:function(t){var e=r.getStorageSync("wxUserInfo"),n={title:this.data.name,path:"/pages-admin/huodongGuanli/huodongindex?id=".concat(this.data.id,"&memberId=").concat(e.memberId),imageUrl:this.data.poster};return n},onShareTimeline:function(t){var e=r.getStorageSync("wxUserInfo"),n={title:this.data.name,path:"/pages-admin/huodongGuanli/huodongindex?id=".concat(this.data.id,"&memberId=").concat(e.memberId),imageUrl:this.data.poster};return n},pay:function(){var t=this;a.default.createOrder({data:{activityId:this.data.id}}).then((function(e){200==e.code&&(console.log(e),r.requestPayment({provider:"weixin",nonceStr:e.data.prepay.nonceStr,package:e.data.prepay.packageVal,paySign:e.data.prepay.paySign,signType:e.data.prepay.signType,timeStamp:e.data.prepay.timeStamp+"",success:function(e){console.log(e),setTimeout((function(){t.$u.toast("支付成功！"),r.navigateBack()}),2e3)},fail:function(e){console.log(e),t.$u.toast("支付失败！"),t.disabled=!1}}))}))},changeIndicatorDots:function(t){this.indicatorDots=!this.indicatorDots},changeAutoplay:function(t){this.autoplay=!this.autoplay},intervalChange:function(t){this.interval=t.target.value},durationChange:function(t){this.duration=t.target.value},onChange:function(t){this.timeData=t}}}},30088:function(t,e,n){"use strict";var r=n(51372)["default"],o=n(81715)["createPage"];n(96910);i(n(923));var a=i(n(39999));function i(t){return t&&t.__esModule?t:{default:t}}r.__webpack_require_UNI_MP_PLUGIN__=n,o(a.default)},39999:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return c.__esModule},default:function(){return h}});var r,o={uNavbar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(n.bind(n,66372))},uCountDown:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-count-down/u-count-down")]).then(n.bind(n,75014))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(n,78278))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(n,85432))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("73f69fd2-1")),r=n?{color:t.$getSSP("73f69fd2-1","content")["navBarTextColor"]}:null,o=n?t.$getSSP("73f69fd2-1","content"):null;t._isMounted||(t.e0=function(e){return t.uni.navigateBack()},t.e1=function(e){t.modelshow=!0},t.e2=function(e){t.showQrcode=!0}),t.$mp.data=Object.assign({},{$root:{m0:n,a0:r,m1:o}})},i=[],c=n(1697),u=c["default"],s=n(95902),l=n.n(s),f=(l(),n(18535)),d=(0,f["default"])(u,a,i,!1,null,"3f772f48",null,!1,o,r),h=d.exports},95902:function(){}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(30088)}));t.O()}]);