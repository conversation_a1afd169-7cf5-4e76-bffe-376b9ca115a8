<view class="uni-table-tr"><block wx:if="{{selection==='selection'}}"><view class="{{['checkbox',(border)?'tr-table--border':'']}}"><table-checkbox vue-id="31caf752-1" checked="{{checked}}" indeterminate="{{indeterminate}}" disabled="{{disabled}}" data-event-opts="{{[['^checkboxSelected',[['checkboxSelected']]]]}}" bind:checkboxSelected="__e" bind:__l="__l"></table-checkbox></view></block><slot></slot></view>