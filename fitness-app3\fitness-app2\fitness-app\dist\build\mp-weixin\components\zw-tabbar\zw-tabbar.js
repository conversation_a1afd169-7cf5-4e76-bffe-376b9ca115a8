(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["components/zw-tabbar/zw-tabbar"],{13740:function(){},74220:function(t,a,e){"use strict";var n;e.r(a),e.d(a,{__esModule:function(){return o.__esModule},default:function(){return h}});var i,c=function(){var t=this,a=t.$createElement;t._self._c},s=[],o=e(92475),r=o["default"],l=e(13740),u=e.n(l),p=(u(),e(18535)),b=(0,p["default"])(r,c,s,!1,null,null,null,!1,n,i),h=b.exports},92475:function(t,a,e){"use strict";var n=e(81715)["default"];Object.defineProperty(a,"__esModule",{value:!0}),a["default"]=void 0;a["default"]={props:{list:{type:Array,default:[{isTabBar:!0,pagePath:"/pages/index/index",iconPath:"/static/icon/tabBar/1.png",selectedIconPath:"/static/icon/tabBar/1_on.png",text:"场馆首页"},{isTabBar:!0,pagePath:"/pages/jiaoLian/list",iconPath:"/static/icon/tabBar/2.png",selectedIconPath:"/static/icon/tabBar/2_on.png",text:"选个教练"},{isTabBar:!0,pagePath:"/pages/yu-yue/index",iconPath:"/static/icon/tabBar/3.png",selectedIconPath:"/static/icon/tabBar/3_on.png",text:""},{pagePath:"/pages/user/yu-yue/index",iconPath:"/static/icon/tabBar/4.png",selectedIconPath:"/static/icon/tabBar/4_on.png",text:"我的预约"},{isTabBar:!0,pagePath:"/pages/user/index",iconPath:"/static/icon/tabBar/5.png",selectedIconPath:"/static/icon/tabBar/5_on.png",text:"个人中心"}]},selIdx:{type:Number,default:0},hoverColor:{type:String,default:"#000000"},color:{type:String,default:"#666666"},bigIdx:{type:Number,default:-1}},methods:{click:function(t){this.list[t].pagePath?this.list[t].isTabBar?n.switchTab({url:this.list[t].pagePath}):n.navigateTo({url:this.list[t].pagePath}):this.$emit("clickTab",t)}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["components/zw-tabbar/zw-tabbar-create-component"],{},function(t){t("81715")["createComponent"](t(74220))}]);