<theme-wrap scoped-slots-compiler="augmented" vue-id="250adff6-1" class="data-v-06ad2a9e" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-06ad2a9e"><u-navbar vue-id="{{('250adff6-2')+','+('250adff6-1')}}" title="编辑会员卡" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-06ad2a9e" bind:__l="__l"></u-navbar></view><view class="formView data-v-06ad2a9e"><view class="formList data-v-06ad2a9e"><view class="data-v-06ad2a9e">会员卡名称:</view><u--input bind:input="__e" vue-id="{{('250adff6-3')+','+('250adff6-1')}}" border="{{false}}" value="{{formList.cardName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardName','$event',[]],['formList']]]]]}}" class="data-v-06ad2a9e" bind:__l="__l"></u--input></view><view class="formList data-v-06ad2a9e"><view class="data-v-06ad2a9e">会员卡类型:</view><u--input bind:input="__e" vue-id="{{('250adff6-4')+','+('250adff6-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.cardTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardTypeName','$event',[]],['formList']]]]]}}" class="data-v-06ad2a9e" bind:__l="__l"></u--input></view><view class="formTextarea border-8 data-v-06ad2a9e"><view class="textareaTitle u-flex data-v-06ad2a9e"><view class="u-flex-1 data-v-06ad2a9e">会员卡图片:</view><u-icon vue-id="{{('250adff6-5')+','+('250adff6-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseLogo']]]]}}" bind:click="__e" class="data-v-06ad2a9e" bind:__l="__l"></u-icon></view><view class="formLogo data-v-06ad2a9e"><u--image vue-id="{{('250adff6-6')+','+('250adff6-1')}}" showLoading="{{true}}" src="{{$root.f0}}" width="240rpx" height="160rpx" radius="4" data-event-opts="{{[['^click',[['clickLogo']]]]}}" bind:click="__e" class="data-v-06ad2a9e" bind:__l="__l"></u--image></view></view><view class="formList data-v-06ad2a9e"><view class="data-v-06ad2a9e">有效天数:</view><u--input bind:input="__e" vue-id="{{('250adff6-7')+','+('250adff6-1')}}" border="{{false}}" value="{{formList.validDays}}" data-event-opts="{{[['^input',[['__set_model',['$0','validDays','$event',[]],['formList']]]]]}}" class="data-v-06ad2a9e" bind:__l="__l"></u--input></view><view class="formList data-v-06ad2a9e"><view class="data-v-06ad2a9e">有效次数:</view><u--input bind:input="__e" vue-id="{{('250adff6-8')+','+('250adff6-1')}}" border="{{false}}" value="{{formList.validTimes}}" data-event-opts="{{[['^input',[['__set_model',['$0','validTimes','$event',[]],['formList']]]]]}}" class="data-v-06ad2a9e" bind:__l="__l"></u--input></view><view class="formList data-v-06ad2a9e"><view class="data-v-06ad2a9e">会员卡价格:</view><u--input bind:input="__e" vue-id="{{('250adff6-9')+','+('250adff6-1')}}" border="{{false}}" value="{{formList.cardPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardPrice','$event',[]],['formList']]]]]}}" class="data-v-06ad2a9e" bind:__l="__l"></u--input></view><view class="formTextarea border-8 data-v-06ad2a9e"><view class="textareaTitle u-flex data-v-06ad2a9e"><view class="u-flex-1 data-v-06ad2a9e">会员卡描述:</view></view><view class="formLogo data-v-06ad2a9e"><u--textarea bind:input="__e" vue-id="{{('250adff6-10')+','+('250adff6-1')}}" placeholder="请输入内容" count="{{true}}" maxlength="{{250}}" value="{{formList.description}}" data-event-opts="{{[['^input',[['__set_model',['$0','description','$event',[]],['formList']]]]]}}" class="data-v-06ad2a9e" bind:__l="__l"></u--textarea></view></view></view><u-picker vue-id="{{('250adff6-11')+','+('250adff6-1')}}" show="{{showType}}" columns="{{actions}}" keyName="dictLabel" data-event-opts="{{[['^confirm',[['typeSelect']]],['^cancel',[['e1']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-06ad2a9e" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-06ad2a9e"><view data-event-opts="{{[['tap',[['deleteCoupon']]]]}}" class="moreBtn data-v-06ad2a9e" style="{{'color:'+($root.m3['buttonLightBgColor'])+';'+('border-color:'+($root.m4['buttonLightBgColor'])+';')}}" bindtap="__e">删除</view><view data-event-opts="{{[['tap',[['saveCoupon']]]]}}" class="addHuiYuan data-v-06ad2a9e" style="{{'background:'+($root.m5['buttonLightBgColor'])+';'+('color:'+($root.m6['buttonTextColor'])+';')+('border-color:'+($root.m7['buttonLightBgColor'])+';')}}" bindtap="__e">保存</view></view></view></theme-wrap>