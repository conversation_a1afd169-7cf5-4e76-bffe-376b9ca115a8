/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node_modules/uview-ui/components/u-form-item/u-form-item.vue?vue&type=style&index=0&id=5e7216f1&lang=scss&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-5e7216f1, .theme-color.u-content-color.data-v-5e7216f1 {
  color: var(--base-color);
}
.theme-bg-color.data-v-5e7216f1, .bgc.data-v-5e7216f1 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-5e7216f1, .nbc.data-v-5e7216f1 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-5e7216f1, .bc.data-v-5e7216f1 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-5e7216f1, .lbc.data-v-5e7216f1 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-5e7216f1, .btc.data-v-5e7216f1 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-5e7216f1, .ltc.data-v-5e7216f1 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-5e7216f1 {
  background: var(--scroll-item-bg-color);
}
view.data-v-5e7216f1, scroll-view.data-v-5e7216f1, swiper-item.data-v-5e7216f1 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-form-item.data-v-5e7216f1 {

  display: flex;

  flex-direction: column;
  font-size: 14px;
  color: #303133;
}
.u-form-item__body.data-v-5e7216f1 {

  display: flex;

  flex-direction: row;
  padding: 10px 0;
}
.u-form-item__body__left.data-v-5e7216f1 {

  display: flex;

  flex-direction: row;
  align-items: center;
}
.u-form-item__body__left__content.data-v-5e7216f1 {
  position: relative;

  display: flex;

  flex-direction: row;
  align-items: center;
  padding-right: 10rpx;
  flex: 1;
}
.u-form-item__body__left__content__icon.data-v-5e7216f1 {
  margin-right: 8rpx;
}
.u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: -9px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}
.u-form-item__body__left__content__label.data-v-5e7216f1 {

  display: flex;

  flex-direction: row;
  align-items: center;
  flex: 1;
  color: #303133;
  font-size: 15px;
}
.u-form-item__body__right.data-v-5e7216f1 {
  flex: 1;
}
.u-form-item__body__right__content.data-v-5e7216f1 {

  display: flex;

  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-form-item__body__right__content__slot.data-v-5e7216f1 {
  flex: 1;
}
.u-form-item__body__right__content__icon.data-v-5e7216f1 {
  margin-left: 10rpx;
  color: #c0c4cc;
  font-size: 30rpx;
}
.u-form-item__body__right__message.data-v-5e7216f1 {
  font-size: 12px;
  line-height: 12px;
  color: #f56c6c;
}
