/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node_modules/uview-ui/components/u-calendar/month.vue?vue&type=style&index=0&id=b6727082&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-b6727082, .theme-color.u-content-color.data-v-b6727082 {
  color: var(--base-color);
}
.theme-bg-color.data-v-b6727082, .bgc.data-v-b6727082 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-b6727082, .nbc.data-v-b6727082 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-b6727082, .bc.data-v-b6727082 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-b6727082, .lbc.data-v-b6727082 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-b6727082, .btc.data-v-b6727082 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-b6727082, .ltc.data-v-b6727082 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-b6727082 {
  background: var(--scroll-item-bg-color);
}
view.data-v-b6727082, scroll-view.data-v-b6727082, swiper-item.data-v-b6727082 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-calendar-month-wrapper.data-v-b6727082 {
  margin-top: 4px;
}
.u-calendar-month__title.data-v-b6727082 {
  font-size: 14px;
  line-height: 42px;
  height: 42px;
  color: #303133;
  text-align: center;
  font-weight: bold;
}
.u-calendar-month__days.data-v-b6727082 {
  position: relative;

  display: flex;

  flex-direction: row;
  flex-wrap: wrap;
}
.u-calendar-month__days__month-mark-wrapper.data-v-b6727082 {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.u-calendar-month__days__month-mark-wrapper__text.data-v-b6727082 {
  font-size: 155px;
  color: rgba(231, 232, 234, 0.83);
}
.u-calendar-month__days__day.data-v-b6727082 {

  display: flex;

  flex-direction: row;
  padding: 2px;
  width: 14.2857142857%;
  box-sizing: border-box;
}
.u-calendar-month__days__day__select.data-v-b6727082 {
  flex: 1;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
}
.u-calendar-month__days__day__select__dot.data-v-b6727082 {
  width: 7px;
  height: 7px;
  border-radius: 100px;
  background-color: #f56c6c;
  position: absolute;
  top: 12px;
  right: 7px;
}
.u-calendar-month__days__day__select__buttom-info.data-v-b6727082 {
  color: #606266;
  text-align: center;
  position: absolute;
  bottom: 5px;
  font-size: 10px;
  text-align: center;
  left: 0;
  right: 0;
}
.u-calendar-month__days__day__select__buttom-info--selected.data-v-b6727082 {
  color: #ffffff;
}
.u-calendar-month__days__day__select__buttom-info--disabled.data-v-b6727082 {
  color: #cacbcd;
}
.u-calendar-month__days__day__select__info.data-v-b6727082 {
  text-align: center;
  font-size: 16px;
}
.u-calendar-month__days__day__select__info--selected.data-v-b6727082 {
  color: #ffffff;
}
.u-calendar-month__days__day__select__info--disabled.data-v-b6727082 {
  color: #cacbcd;
}
.u-calendar-month__days__day__select--selected.data-v-b6727082 {
  background-color: #3c9cff;

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex: 1;
  border-radius: 3px;
}
.u-calendar-month__days__day__select--range-selected.data-v-b6727082 {
  opacity: 0.3;
  border-radius: 0;
}
.u-calendar-month__days__day__select--range-start-selected.data-v-b6727082 {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.u-calendar-month__days__day__select--range-end-selected.data-v-b6727082 {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
