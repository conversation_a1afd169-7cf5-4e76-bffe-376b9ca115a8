<theme-wrap scoped-slots-compiler="augmented" vue-id="20f621a0-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view data-event-opts="{{[['tap',[['back']]]]}}" class="backIcon" bindtap="__e"><u-icon vue-id="{{('20f621a0-2')+','+('20f621a0-1')}}" name="arrow-left" color="#2979ff" size="28" bind:__l="__l"></u-icon></view><block wx:if="{{detail.bannerList}}"><block wx:if="{{$root.g0>0}}"><u-swiper vue-id="{{('20f621a0-3')+','+('20f621a0-1')}}" height="400rpx" list="{{detail.bannerList}}" bgColor="transparent" circular="{{true}}" indicator="{{true}}" autoplay="{{true}}" keyName="src" bind:__l="__l"></u-swiper></block></block><block wx:else><image class="w-100" src="{{detail.banner}}" mode="widthFix"></image></block><view class="title">{{detail.title}}</view><view class="container bottom-placeholder dark-theme"><view class="white-box u-p-40 bg-fff u-font-30 u-m-t-40 u-m-b-40 border-16"><view class="time-box"><image class="icon" src="/static/images/icons/clock.png"></image><view class="time">{{"时间："+detail.classTime+''}}</view></view><view class="time-box u-m-t-20"><image class="icon" src="/static/images/icons/clock.png"></image><view class="time">{{"时长："+(detail.classLength||'-')+'分钟'}}</view></view><view data-event-opts="{{[['tap',[['openMap',['$event']]]]]}}" class="u-flex u-flex-1 u-m-t-20" bindtap="__e"><u-icon vue-id="{{('20f621a0-4')+','+('20f621a0-1')}}" name="map" color="rgb(255, 190, 0)" size="20" bind:__l="__l"></u-icon><view class="u-line-1 u-p-l-10">{{detail.shopAddr}}</view></view></view><view class="white-box coach-box u-p-40 bg-fff u-font-30 u-m-t-40 u-m-b-40 border-16"><view style="width:160rpx;height:160rpx;border-radius:50%;overflow:hidden;background:#ccc;"><block wx:if="{{coach.coachAvatar}}"><image class="w-100" style="height:160rpx;" src="{{$root.f0}}"></image></block><block wx:else><image class="w-100" style="height:160rpx;" src="/static/images/default/coach.jpg"></image></block></view><view class="right"><view class="name">{{coach.nickName}}</view><view class="specialty">{{coach.specialty}}</view></view></view><view data-event-opts="{{[['tap',[['handleOfficial',['$event']]]]]}}" class="u-flex u-tips-color u-font-26 u-m-l-10 u-m-t-20" bindtap="__e"><u-icon vue-id="{{('20f621a0-5')+','+('20f621a0-1')}}" name="error-circle" color="{{$root.m1['buttonLightBgColor']}}" bind:__l="__l"></u-icon><text class="u-m-l-10 ltc">关注公众号，教练确认后会发送公众号消息通知会员</text></view><view class="white-box u-p-40 bg-fff u-font-30 u-m-t-40 u-m-b-40 border-16"><view class="course-title">课程详情</view><view class="course-content">{{detail.remark}}</view><block wx:if="{{detail.classInfoPicList}}"><view class="course-img-box"><block wx:for="{{detail.classInfoPicList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="w-100" src="{{item}}" mode="widthFix"></image></block></view></block></view><block wx:if="{{!payDisabled}}"><view class="bottom-box"><view class="price">{{"￥"+detail.price}}</view><view class="u-flex" style="margin-left:auto;margin-right:30rpx;"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-flex u-row-start u-col-start" style="display:flex;align-items:center;" bindtap="__e"><view class="check-box-wrap u-m-r-20 overflow-hidden border-8" style="{{'background-color:'+(checkboxValue1?'rgb(255, 190, 0)':'#fff')+';'+('border-color:'+('rgb(255, 190, 0)')+';')}}"><u-icon vue-id="{{('20f621a0-6')+','+('20f621a0-1')}}" name="checkbox-mark" color="#fff" size="16" bind:__l="__l"></u-icon></view><view class="u-font-28" style="color:#fff;">阅读并同意<text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-m-l-10" style="display:block;color:rgb(255, 190, 0);text-decoration:underline;" catchtap="__e">合同条款</text></view></view></view><button class="btn" disabled="{{!detail.remainder}}" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">{{''+(detail.remainder?'立即预约':'剩余人数不足')+''}}</button></view></block></view><u-popup vue-id="{{('20f621a0-7')+','+('20f621a0-1')}}" show="{{showContractModal}}" mode="center" safeAreaInsetBottom="{{false}}" round="16" data-event-opts="{{[['^close',[['e2']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-p-40" style="height:75vh;width:85vw;overflow:scroll;"><u-parse class="content-text" vue-id="{{('20f621a0-8')+','+('20f621a0-7')}}" content="{{contract}}" selectable="{{true}}" tagStyle="{{parseTagStyle}}" bind:__l="__l"></u-parse></view></u-popup></view></theme-wrap>