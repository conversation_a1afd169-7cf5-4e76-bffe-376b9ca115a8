<theme-wrap scoped-slots-compiler="augmented" vue-id="19d26eec-1" class="data-v-314ef36c" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-314ef36c"><u-navbar vue-id="{{('19d26eec-2')+','+('19d26eec-1')}}" title="编辑会员卡" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-314ef36c" bind:__l="__l"></u-navbar></view><view class="formView data-v-314ef36c"><view class="formList data-v-314ef36c"><view class="data-v-314ef36c">会员卡名称:</view><u--input bind:input="__e" vue-id="{{('19d26eec-3')+','+('19d26eec-1')}}" border="{{false}}" value="{{formList.cardName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardName','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--input></view><view class="formList data-v-314ef36c"><view class="data-v-314ef36c">会员卡类型:</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-flex-1 data-v-314ef36c" bindtap="__e"><u--input bind:input="__e" vue-id="{{('19d26eec-4')+','+('19d26eec-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.cardTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardTypeName','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--input></view></view><block wx:if="{{showJL}}"><view class="formList data-v-314ef36c"><view class="data-v-314ef36c">绑定教练:</view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="u-flex-1 data-v-314ef36c" bindtap="__e"><u--input bind:input="__e" vue-id="{{('19d26eec-5')+','+('19d26eec-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.coachName}}" data-event-opts="{{[['^input',[['__set_model',['$0','coachName','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--input></view></view></block><view class="formTextarea border-8 data-v-314ef36c"><view class="textareaTitle u-flex data-v-314ef36c"><view class="u-flex-1 data-v-314ef36c">会员卡图片:</view><u-icon vue-id="{{('19d26eec-6')+','+('19d26eec-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseLogo']]]]}}" bind:click="__e" class="data-v-314ef36c" bind:__l="__l"></u-icon></view><view class="formLogo data-v-314ef36c"><u--image vue-id="{{('19d26eec-7')+','+('19d26eec-1')}}" showLoading="{{true}}" src="{{$root.f0}}" width="240rpx" height="160rpx" radius="4" data-event-opts="{{[['^click',[['clickLogo']]]]}}" bind:click="__e" class="data-v-314ef36c" bind:__l="__l"></u--image></view></view><view class="formList data-v-314ef36c"><view class="data-v-314ef36c">有效天数:</view><u--input bind:input="__e" vue-id="{{('19d26eec-8')+','+('19d26eec-1')}}" border="{{false}}" value="{{formList.validDays}}" data-event-opts="{{[['^input',[['__set_model',['$0','validDays','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--input></view><view class="formList data-v-314ef36c"><view class="data-v-314ef36c">有效次数:</view><u--input bind:input="__e" vue-id="{{('19d26eec-9')+','+('19d26eec-1')}}" border="{{false}}" value="{{formList.validTimes}}" data-event-opts="{{[['^input',[['__set_model',['$0','validTimes','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--input></view><view class="formList data-v-314ef36c"><view class="data-v-314ef36c">会员卡价格:</view><u--input bind:input="__e" vue-id="{{('19d26eec-10')+','+('19d26eec-1')}}" border="{{false}}" value="{{formList.cardPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardPrice','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--input></view><view class="formTextarea border-8 data-v-314ef36c"><view class="textareaTitle u-flex data-v-314ef36c"><view class="u-flex-1 data-v-314ef36c">会员卡描述:</view></view><view class="formLogo data-v-314ef36c"><u--textarea bind:input="__e" vue-id="{{('19d26eec-11')+','+('19d26eec-1')}}" placeholder="请输入内容" count="{{true}}" maxlength="{{250}}" value="{{formList.description}}" data-event-opts="{{[['^input',[['__set_model',['$0','description','$event',[]],['formList']]]]]}}" class="data-v-314ef36c" bind:__l="__l"></u--textarea></view></view></view><u-picker vue-id="{{('19d26eec-12')+','+('19d26eec-1')}}" show="{{showType}}" columns="{{actions}}" keyName="dictLabel" data-event-opts="{{[['^confirm',[['typeSelect']]],['^cancel',[['e3']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-314ef36c" bind:__l="__l"></u-picker><u-picker vue-id="{{('19d26eec-13')+','+('19d26eec-1')}}" show="{{showCoach}}" columns="{{coachActions}}" keyName="nickName" data-event-opts="{{[['^confirm',[['coachSelect']]],['^cancel',[['e4']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-314ef36c" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-314ef36c"><view data-event-opts="{{[['tap',[['saveCoupon']]]]}}" class="saveBtn data-v-314ef36c" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增</view></view></view></theme-wrap>