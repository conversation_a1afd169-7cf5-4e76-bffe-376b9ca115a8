<view data-event-opts="{{[['touchstart',[['onTouchStart',['$event']]]],['touchmove',[['onTouchMove',['$event']]]]]}}" class="{{['chang-guan-list',show?'show':'']}}" bindtouchstart="__e" catchtouchmove="__e"><u-navbar vue-id="fea6f944-1" title="选择场馆" titleStyle="{{({color:'#fff '})}}" bgColor="#000" placeholder="{{true}}" safeAreaInsetTop="{{true}}" fixed="{{false}}" show-back="{{false}}" bind:__l="__l"></u-navbar><view class="u-p-40"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeChangGuan',[index]]]]]}}" class="changguan-item-blk u-relative overflow-hidden u-m-b-40 border-24" bindtap="__e"><view class="w-100 placeholder overflow-hidden" style="aspect-ratio:2;line-height:0;"><image class="w-100" src="{{item.f0}}" mode="widthFix"></image></view><view class="u-absolute u-p-30 w-100" style="bottom:0;left:0;background-color:rgba(255, 255, 255, 0.6);"><view class="u-font-40 font-bold u-line u-m-b-20 w-100 u-text-center">{{''+item.$orig.shopName+''}}</view><view class="u-flex w-100 u-font-30" style="color:#555;"><view class="u-flex-5 u-p-r-20 u-line-1">{{''+item.$orig.address+''}}</view><view class="u-flex-1">{{''+item.g0+'km'}}</view></view></view><view class="u-absolute" style="top:20rpx;right:20rpx;"></view></view></block></view></view>