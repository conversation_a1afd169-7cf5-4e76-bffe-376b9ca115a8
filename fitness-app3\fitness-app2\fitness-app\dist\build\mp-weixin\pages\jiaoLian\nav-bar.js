"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/jiaoLian/nav-bar"],{1649:function(e,n,t){var a=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;n["default"]={props:{buttonBgColor:{type:String,default:"#fff"},buttonTextColor:{type:String,default:"#000"}},methods:{goBack:function(){a.navigateBack()}}}},84004:function(e,n,t){t.r(n),t.d(n,{__esModule:function(){return r.__esModule},default:function(){return s}});var a,o={uNavbar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(t.bind(t,66372))}},u=function(){var e=this,n=e.$createElement;e._self._c},l=[],r=t(1649),c=r["default"],i=t(18535),f=(0,i["default"])(c,u,l,!1,null,null,null,!1,o,a),s=f.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/jiaoLian/nav-bar-create-component"],{},function(e){e("81715")["createComponent"](e(84004))}]);