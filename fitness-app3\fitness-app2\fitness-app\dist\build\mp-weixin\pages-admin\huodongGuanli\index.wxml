<theme-wrap scoped-slots-compiler="augmented" vue-id="15767dec-1" class="data-v-f4ffb306" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-f4ffb306"><u-navbar vue-id="{{('15767dec-2')+','+('15767dec-1')}}" title="活动列表" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-f4ffb306" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-f4ffb306"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-f4ffb306" bindtap="__e"><view class="u-flex u-col-center u-row-start data-v-f4ffb306" style="flex-wrap:no-wrap;overflow:hidden;"><view class="overflow-hidden flex-0 border-16 data-v-f4ffb306" style="width:140rpx;height:140rpx;line-height:0;"><image class="h-100 data-v-f4ffb306" src="{{item.$orig.background}}" mode="heightFix"></image></view><view class="w-100 u-p-l-20 data-v-f4ffb306"><view class="u-line-1 w-100 data-v-f4ffb306">{{''+item.$orig.name+''}}</view><view class="u-tips-color u-font-26 data-v-f4ffb306" style="margin-top:30rpx;">{{'状态：'+(item.$orig.status==1?'已开启':'已关闭')+''}}</view><view class="u-tips-color u-font-26 data-v-f4ffb306" style="margin-top:10rpx;">{{'售价：￥'+item.g1+''}}</view></view></view><view class="btn-wrap data-v-f4ffb306"><view data-event-opts="{{[['tap',[['start',['$0'],[[['list','',index]]]]]]]}}" class="{{['data-v-f4ffb306',item.$orig.status==0?'u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 border-8 btc text-no-wrap lbc u-font-26 font-bold':'u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10']}}" style="border:1px solid;border-color:buttonLightBgColor;" catchtap="__e">{{item.$orig.status==0?'开启':'关闭'}}</view><view data-event-opts="{{[['tap',[['del',['$0'],[[['list','',index]]]]]]]}}" class="u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10 data-v-f4ffb306" style="border:1px solid;border-color:buttonLightBgColor;" catchtap="__e">删除</view></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-f4ffb306"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-f4ffb306"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-f4ffb306">暂无活动</view></view></block></view><view class="bottom-blk bg-fff w-100 u-p-40 data-v-f4ffb306"><u-button vue-id="{{('15767dec-3')+','+('15767dec-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['toAddCourse']]]]}}" bind:click="__e" class="data-v-f4ffb306" bind:__l="__l" vue-slots="{{['default']}}">添加活动</u-button></view></view></theme-wrap>