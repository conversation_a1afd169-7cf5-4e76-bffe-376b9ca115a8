<theme-wrap scoped-slots-compiler="augmented" vue-id="ba7d7aee-1" class="data-v-7eb1df4c" bind:__l="__l" vue-slots="{{['content']}}"><view class="con data-v-7eb1df4c" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('ba7d7aee-2')+','+('ba7d7aee-1')}}" title="选择用户" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-7eb1df4c" bind:__l="__l"></u-navbar><view class="searchView data-v-7eb1df4c" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('ba7d7aee-3')+','+('ba7d7aee-1')}}" showAction="{{false}}" placeholder="请输入会员名,微信名,手机号" animation="{{true}}" data-event-opts="{{[['^change',[['searchView']]]]}}" bind:change="__e" class="data-v-7eb1df4c" bind:__l="__l"></u-search></view><u-gap vue-id="{{('ba7d7aee-4')+','+('ba7d7aee-1')}}" height="1" bgColor="#bbb" class="data-v-7eb1df4c" bind:__l="__l"></u-gap><scroll-view class="scrollView data-v-7eb1df4c" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:for="{{showList}}" wx:for-item="list" wx:for-index="index" wx:key="*this"><view class="userList data-v-7eb1df4c"><view class="user_avatar data-v-7eb1df4c"><u-avatar vue-id="{{('ba7d7aee-5-'+index)+','+('ba7d7aee-1')}}" size="80rpx" src="{{src}}" shape="circle" class="data-v-7eb1df4c" bind:__l="__l"></u-avatar></view><view data-event-opts="{{[['tap',[['addHuiYuan',['$0'],[[['showList','n',n]]]]]]]}}" class="user_textView data-v-7eb1df4c" bindtap="__e"><text class="data-v-7eb1df4c">晴天雨绵</text><view class="user_tag data-v-7eb1df4c">15978907890</view></view><view class="user_rightView data-v-7eb1df4c"><u-icon vue-id="{{('ba7d7aee-6-'+index)+','+('ba7d7aee-1')}}" name="arrow-right" class="data-v-7eb1df4c" bind:__l="__l"></u-icon></view></view></block></scroll-view><u-popup vue-id="{{('ba7d7aee-7')+','+('ba7d7aee-1')}}" show="{{show}}" mode="center" data-event-opts="{{[['^close',[['close']]],['^open',[['open']]]]}}" bind:close="__e" bind:open="__e" class="data-v-7eb1df4c" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-7eb1df4c"><view class="w-100 u-text-center data-v-7eb1df4c">添加会员卡</view><view class="data-v-7eb1df4c"><u--form vue-id="{{('ba7d7aee-8')+','+('ba7d7aee-7')}}" labelPosition="left" model="{{model1}}" rules="{{rules}}" labelWidth="120" data-ref="uForm" class="data-v-7eb1df4c vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('ba7d7aee-9')+','+('ba7d7aee-8')}}" label="会员卡类型" prop="name" borderBottom="{{true}}" data-ref="item1" class="data-v-7eb1df4c vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('ba7d7aee-10')+','+('ba7d7aee-9')}}" border="none" placeholder="请输入会员卡名称" value="{{model1.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['model1']]]]]}}" class="data-v-7eb1df4c" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('ba7d7aee-11')+','+('ba7d7aee-8')}}" label="有效天数" prop="name" borderBottom="{{true}}" data-ref="item1" class="data-v-7eb1df4c vue-ref" bind:__l="__l" vue-slots="{{['default','right']}}"><u--input bind:input="__e" vue-id="{{('ba7d7aee-12')+','+('ba7d7aee-11')}}" border="none" value="{{model1.day}}" data-event-opts="{{[['^input',[['__set_model',['$0','day','$event',[]],['model1']]]]]}}" class="data-v-7eb1df4c" bind:__l="__l"></u--input><text slot="right" class="data-v-7eb1df4c">天</text></u-form-item><u-form-item vue-id="{{('ba7d7aee-13')+','+('ba7d7aee-8')}}" label="总次数" prop="name" borderBottom="{{true}}" data-ref="item1" class="data-v-7eb1df4c vue-ref" bind:__l="__l" vue-slots="{{['default','right']}}"><u--input bind:input="__e" vue-id="{{('ba7d7aee-14')+','+('ba7d7aee-13')}}" border="none" value="{{model1.order}}" data-event-opts="{{[['^input',[['__set_model',['$0','order','$event',[]],['model1']]]]]}}" class="data-v-7eb1df4c" bind:__l="__l"></u--input><text slot="right" class="data-v-7eb1df4c">次</text></u-form-item><u-form-item vue-id="{{('ba7d7aee-15')+','+('ba7d7aee-8')}}" label="售价" prop="name" borderBottom="{{true}}" data-ref="item1" class="data-v-7eb1df4c vue-ref" bind:__l="__l" vue-slots="{{['default','right']}}"><u--input bind:input="__e" vue-id="{{('ba7d7aee-16')+','+('ba7d7aee-15')}}" border="none" value="{{model1.coin}}" data-event-opts="{{[['^input',[['__set_model',['$0','coin','$event',[]],['model1']]]]]}}" class="data-v-7eb1df4c" bind:__l="__l"></u--input><text slot="right" class="data-v-7eb1df4c">元</text></u-form-item></u--form><u--textarea bind:input="__e" vue-id="{{('ba7d7aee-17')+','+('ba7d7aee-7')}}" placeholder="备注" count="{{true}}" value="{{value}}" data-event-opts="{{[['^input',[['__set_model',['','value','$event',[]]]]]]}}" class="data-v-7eb1df4c" bind:__l="__l"></u--textarea><view data-event-opts="{{[['tap',[['saveCoupon']]]]}}" class="saveBtn u-m-t-20 data-v-7eb1df4c" style="{{'background:'+($root.m4['buttonLightBgColor'])+';'+('color:'+($root.m5['buttonTextColor'])+';')+('border-color:'+($root.m6['buttonLightBgColor'])+';')}}" bindtap="__e">确认</view></view></view></u-popup></view></theme-wrap>