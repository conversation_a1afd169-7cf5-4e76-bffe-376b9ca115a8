(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-toast/u-toast"],{67559:function(n,e,t){"use strict";t.r(e),t.d(e,{__esModule:function(){return s.__esModule},default:function(){return p}});var o,i={uOverlay:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-overlay/u-overlay")]).then(t.bind(t,15550))},uLoadingIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(t.bind(t,94597))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(t,78278))},uGap:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-gap/u-gap")]).then(t.bind(t,9932))}},u=function(){var n=this,e=n.$createElement,t=(n._self._c,n.__get_style([n.contentStyle]));n.$mp.data=Object.assign({},{$root:{s0:t}})},r=[],s=t(96345),c=s["default"],a=t(80033),l=t.n(a),m=(l(),t(18535)),f=(0,m["default"])(c,u,r,!1,null,"60b6a9e7",null,!1,i,o),p=f.exports},80033:function(){},96345:function(n,e,t){"use strict";var o=t(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;e["default"]={name:"u-toast",mixins:[o.$u.mpMixin,o.$u.mixin],data:function(){return{isShow:!1,timer:null,config:{message:"",type:"",duration:2e3,icon:!0,position:"center",complete:null,overlay:!1,loading:!1},tmpConfig:{}}},computed:{iconName:function(){return this.tmpConfig.icon&&"none"!=this.tmpConfig.icon&&["error","warning","success","primary"].includes(this.tmpConfig.type)?o.$u.type2icon(this.tmpConfig.type):""},overlayStyle:function(){var n={justifyContent:"center",alignItems:"center",display:"flex",backgroundColor:"rgba(0, 0, 0, 0)"};return n},iconStyle:function(){var n={marginRight:"4px"};return n},loadingIconColor:function(){var n="rgb(255, 255, 255)";return["error","warning","success","primary"].includes(this.tmpConfig.type)&&(n=o.$u.hexToRgb(o.$u.color[this.tmpConfig.type])),n},contentStyle:function(){var n=o.$u.sys().windowHeight,e={},t=0;return"top"===this.tmpConfig.position?t=.25*-n:"bottom"===this.tmpConfig.position&&(t=.25*n),e.transform="translateY(".concat(t,"px)"),e}},created:function(){var n=this;["primary","success","error","warning","default","loading"].map((function(e){n[e]=function(t){return n.show({type:e,message:t})}}))},methods:{show:function(n){var e=this;this.tmpConfig=o.$u.deepMerge(this.config,n),this.clearTimer(),this.isShow=!0,this.timer=setTimeout((function(){e.clearTimer(),"function"===typeof e.tmpConfig.complete&&e.tmpConfig.complete()}),this.tmpConfig.duration)},hide:function(){this.clearTimer()},clearTimer:function(){this.isShow=!1,clearTimeout(this.timer),this.timer=null}},beforeDestroy:function(){this.clearTimer()}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-toast/u-toast-create-component"],{},function(n){n("81715")["createComponent"](n(67559))}]);