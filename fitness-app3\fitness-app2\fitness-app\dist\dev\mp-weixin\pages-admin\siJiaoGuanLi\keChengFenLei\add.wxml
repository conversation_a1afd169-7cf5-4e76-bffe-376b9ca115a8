<theme-wrap scoped-slots-compiler="augmented" vue-id="8dbfbd1e-1" class="data-v-37852635" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-37852635" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('8dbfbd1e-2')+','+('8dbfbd1e-1')}}" title="新增课程" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-37852635" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-37852635"><view class="formView data-v-37852635"><view class="formList data-v-37852635"><view class="data-v-37852635">课程类型名称:</view><u--input bind:input="__e" vue-id="{{('8dbfbd1e-3')+','+('8dbfbd1e-1')}}" border="{{false}}" value="{{formList.courseTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseTypeName','$event',[]],['formList']]]]]}}" class="data-v-37852635" bind:__l="__l"></u--input></view></view></view><view class="bottonBtn u-flex data-v-37852635"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-37852635" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增课程</view></view></view></theme-wrap>