"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/ruChang/history"],{53483:function(t,a,e){e.r(a),e.d(a,{__esModule:function(){return l.__esModule},default:function(){return s}});var n,r={uNavbar:function(){return Promise.all([e.e("common/vendor"),e.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(e.bind(e,66372))},uIcon:function(){return Promise.all([e.e("common/vendor"),e.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(e.bind(e,78278))}},u=function(){var t=this,a=t.$createElement,e=(t._self._c,t.$hasSSP("3d986150-1")),n=e?{color:t.$getSSP("3d986150-1","content")["buttonTextColor"]}:null,r=e?t.$getSSP("3d986150-1","content"):null,u=e?t.list.length:null,o=e&&u?t.__map(t.list,(function(a,e){var n=t.__get_orig(a),r=t.$getSSP("3d986150-1","content");return{$orig:n,m2:r}})):null,l=e&&!u?!t.list.length&&!t.loading:null;t.$mp.data=Object.assign({},{$root:{m0:e,a0:n,m1:r,g0:u,l0:o,g1:l}})},o=[],l=e(64569),i=l["default"],c=e(18535),d=(0,c["default"])(i,u,o,!1,null,null,null,!1,r,n),s=d.exports},64569:function(t,a,e){var n=e(81715)["default"];Object.defineProperty(a,"__esModule",{value:!0}),a["default"]=void 0;a["default"]={data:function(){return{currentPage:1,totalPages:1,list:[{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"},{avatar:e(22943),created_at:"2020-10-10 10:10:10"}],amount:1e3,currentChangGuan:"",changGuanList:[{label:"场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1",id:1},{label:"场馆2",id:1},{label:"场馆3",id:1},{label:"场馆4",id:1},{label:"场馆5",id:1}],currentIndex:0,loading:!1}},onLoad:function(){this.currentChangGuan=this.changGuanList[this.currentIndex].label},onReachBottom:function(){this.currentPage<this.totalPages?(this.currentPage++,this.loadData()):n.showToast({title:"没有更多了",icon:"none"})},onShow:function(){},methods:{changeChangGuan:function(t){this.currentIndex=t.detail.value,this.currentChangGuan=this.changGuanList[t.detail.value].label},loadData:function(){var t=this;api.getData().then((function(a){t.$nextTick((function(){t.loading=!1}))}))}}}},71418:function(t,a,e){var n=e(51372)["default"],r=e(81715)["createPage"];e(96910);o(e(923));var u=o(e(53483));function o(t){return t&&t.__esModule?t:{default:t}}n.__webpack_require_UNI_MP_PLUGIN__=e,r(u.default)}},function(t){var a=function(a){return t(t.s=a)};t.O(0,["common/vendor"],(function(){return a(71418)}));t.O()}]);