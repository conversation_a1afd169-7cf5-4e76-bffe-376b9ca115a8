<theme-wrap scoped-slots-compiler="augmented" vue-id="0a7b7799-1" class="data-v-e075b804" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-e075b804" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('0a7b7799-2')+','+('0a7b7799-1')}}" title="会员卡明细" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-e075b804" bind:__l="__l"></u-navbar><view class="container u-p-t-40 data-v-e075b804"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 data-v-e075b804" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view></view><block wx:if="{{$root.g0>0}}"><view class="u-p-l-20 u-p-r-20 data-v-e075b804"><block wx:for="{{roleList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view class="u-p-t-20 u-p-b-20 u-border-bottom u-flex data-v-e075b804"><view class="u-flex-1 data-v-e075b804"><view class="title bold data-v-e075b804">{{"购买用户："+list.memberName}}</view><view class="value u-flex u-m-t-20 data-v-e075b804"><view class="u-flex-1 bold data-v-e075b804">{{"会员卡名称："+list.cardName}}</view></view></view><view data-event-opts="{{[['tap',[['gotoDetails',['$0'],[[['roleList','',index]]]]]]]}}" class="u-m-l-20 data-v-e075b804" bindtap="__e"><u-icon vue-id="{{('0a7b7799-3-'+index)+','+('0a7b7799-1')}}" name="arrow-right" class="data-v-e075b804" bind:__l="__l"></u-icon></view></view></block><view class="whiteView data-v-e075b804"></view></view></block><block wx:else><u-empty vue-id="{{('0a7b7799-4')+','+('0a7b7799-1')}}" marginTop="150" mode="data" class="data-v-e075b804" bind:__l="__l"></u-empty></block><u-picker vue-id="{{('0a7b7799-5')+','+('0a7b7799-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-e075b804" bind:__l="__l"></u-picker></view></theme-wrap>