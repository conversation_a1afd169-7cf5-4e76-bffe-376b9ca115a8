<theme-wrap scoped-slots-compiler="augmented" vue-id="38e8ed72-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('38e8ed72-2')+','+('38e8ed72-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="一键预约" safeAreaInsetTop="{{true}}" bind:__l="__l" vue-slots="{{['left']}}"><view slot="left"></view></u-navbar><view class="w-100 u-p-t-20 u-p-b-20 nbc u-flex u-row-center u-col-center"><view style="width:35vw;"><u-subsection vue-id="{{('38e8ed72-3')+','+('38e8ed72-1')}}" list="{{typeList}}" mode="subsection" inactiveColor="{{$root.m2['buttonTextColor']}}" activeColor="{{$root.m3['buttonLightBgColor']}}" bgColor="{{$root.m4['navBarColor']}}" current="{{currentType}}" data-event-opts="{{[['^change',[['changeCurrentType']]]]}}" bind:change="__e" bind:__l="__l"></u-subsection></view></view><calendar vue-id="{{('38e8ed72-4')+','+('38e8ed72-1')}}" showFilter="{{false}}" data-event-opts="{{[['^handleFilter',[['e0']]],['^changeFormatActive',[['changeActive']]]]}}" bind:handleFilter="__e" bind:changeFormatActive="__e" bind:__l="__l"></calendar><view hidden="{{!(currentType==0)}}" class="container" alt="私教"><block alt="明星私教"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between"><text class="font-bold u-font-36">精选课程</text></view><view class="fitnessBody"><block wx:for="{{$root.l0}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['gotoJiaoList',['$0'],[[['textList','',index]]]]]]]}}" class="fitenessItem" bindtap="__e"><block wx:if="{{list.$orig.cover}}"><image class="fitIcon" mode="aspectFill" src="{{list.f0}}"></image></block><block wx:else><image class="fitIcon" mode="aspectFill" src="/static/images/default/course.png"></image></block><view class="fitText u-p-t-10 u-font-30 u-line-1">{{list.$orig.courseName}}</view></view></block></view></block><block alt="明星教练"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between"><text class="font-bold u-font-36">明星教练</text><navigator url="/pages/jiaoLian/list" open-type="switchTab" hover-class="none"><u-icon vue-id="{{('38e8ed72-5')+','+('38e8ed72-1')}}" label="查看更多" name="arrow-right" labelPos="left" size="15" labelSize="15" color="#999" bind:__l="__l"></u-icon></navigator></view><view class="w-100 border-16 u-m-b-20 u-flex" style="overflow:scroll;"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toJiaoLianDetail',['$0'],[[['mingXingList','',index]]]]]]]}}" class="jiaolian-item flex-0 u-flex-col u-row-center u-col-center border-16 u-m-r-30" bindtap="__e"><image class="mxIcon" style="width:60rpx;height:60rpx;" src="../../static/images/icons/diamond.png"></image><view class="u-flex u-row-center u-col-center" style="height:230rpx;overflow:hidden;"><block wx:if="{{item.$orig.coachPhotos}}"><image class="h-100" src="{{item.f1}}" mode="heightFix"></image></block><block wx:else><image class="h-100" src="/static/images/default/coach_photo.png" mode="heightFix"></image></block></view><view class="u-p-t-20 u-p-b-10 u-font-34 font-bold u-line-1 u-text-center" style="position:absolute;bottom:5px;color:#fff;">{{''+item.$orig.nickName+''}}</view></view></block></view></block><view hidden="{{!(!loading)}}" class="team-list"><text class="font-bold u-font-36">私教团队</text><view class="w-100" style="overflow-x:auto;"><view class="u-flex u-m-t-40 u-p-b-20"><view data-event-opts="{{[['tap',[['clickTag',[999,'']]]]]}}" class="{{['my-tag',(tagIdx==999)?'activeTag':'']}}" bindtap="__e">全部</view><block wx:for="{{tagList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['clickTag',[index,'$0'],[[['tagList','',index,'coachTypeName']]]]]]]}}" class="{{['my-tag',(tagIdx==index)?'activeTag':'']}}" style="margin-left:20rpx;" bindtap="__e">{{''+list.coachTypeName+''}}</view></block></view></view><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toJiaoLianDetail',['$0'],[[['list','',index]]]]]]]}}" hidden="{{!(item.m5)}}" style="margin-top:96rpx !important;" bindtap="__e"><view class="w-100 u-flex u-row-between u-col-end border-16 u-relative" style="max-height:260rpx;height:240rpx;background-color:#e0e0e0;"><view class="overflow-hidden u-relative" style="width:30%;line-height:0;"><block wx:if="{{item.$orig.coachPhotos}}"><image class="w-100" style="height:100%;" src="{{item.f2}}" mode="widthFix"></image></block><block wx:else><image class="w-100" style="height:100%;" src="/static/images/default/coach_photo.png" mode="widthFix"></image></block></view><view class="u-p-l-10 u-relative u-p-20" style="width:70%;align-self:flex-start;"><view class="u-p-r-10 u-flex" style="align-items:center;"><u-icon vue-id="{{('38e8ed72-6-'+index)+','+('38e8ed72-1')}}" size="30" color="#000" name="account-fill" bind:__l="__l"></u-icon><text class="font-bold u-font-34 u-p-r-10">{{item.$orig.nickName}}</text></view><view class="u-p-r-10 u-p-t-20 u-p-b-20 w-100 u-flex no-wrap"><view class="btn-blk fc-fff u-font-24 font-bold u-m-r-20 u-text-center border-8" style="background:#000;">{{''+(item.$orig.coachTypeName||"教练类型")+''}}</view><view style="width:1px;height:16px;background-color:#a19fcc;"></view><block wx:if="{{item.g1}}"><view class="overflow-hidden u-p-l-20"><view class="u-flex no-wrap" style="overflow:scroll;"><block wx:for="{{item.$orig.specialtyList}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="bgc u-m-r-10 u-m-l-10 text-no-wrap u-font-22 u-p-t-8 u-p-b-8 u-p-l-14 u-p-r-14">{{''+i+''}}</view></block></view></view></block></view><view class="u-flex u-m-r-20" style="flex-direction:row-reverse;"><block alt="预约按钮"><block wx:if="{{item.g2}}"><view data-event-opts="{{[['tap',[['toCoursesList',[index]]]]]}}" class="{{['border-8','flex-0','u-text-center','lbc','btc','font-bold','u-font-24','btn-blk',(item.$orig.status==0)?'disabled':'']}}" catchtap="__e">所授课程</view></block><block wx:else><block wx:if="{{item.g3}}"><view data-event-opts="{{[['tap',[['toCoursesList',[index]]]]]}}" class="border-8 flex-0 u-text-center lbc btc font-bold u-font-24 btn-blk" catchtap="__e">预约</view></block><block wx:else><view class="border-8 disabled flex-0 u-text-center lbc btc font-bold u-font-24 btn-blk">暂无课程</view></block></block></block></view></view></view></view></block></block><block wx:else><view class="w-100 u-p-t-40 u-p-b-40 u-flex-col u-row-center u-col-center"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width"></image><view class="u-p-t-10 u-font-30 u-tips-color">暂无课程</view></view></block></view><view hidden="{{!(loading)}}"><block wx:for="{{$root.l3}}" wx:for-item="j" wx:for-index="idx" wx:key="idx"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between"><view class="u-flex u-col-center u-row-start"><view class="placeholder flex-0" style="height:120rpx;width:120rpx;border-radius:50%;"></view><view class="placeholder flex-0 u-m-l-20" style="width:200rpx;height:40rpx;"></view></view><view class="placeholder border-32" style="width:180rpx;min-width:180rpx;height:64rpx;line-height:64rpx;"></view></view></block></view></view><view hidden="{{!(currentType==1)}}" alt="团课"><view class="container u-p-t-40 bottom-placeholder"><block wx:if="{{$root.g4}}"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="group-course-item"><view class="u-col-center u-row-start"><block wx:if="{{item.$orig.background}}"><image class="bg" src="{{item.$orig.background}}" mode="widthFix"></image></block><block wx:else><image class="bg" src="/static/images/default/banner.png" mode="widthFix"></image></block><view class="content"><view class="group-course-title">{{item.$orig.title}}</view><view class="u-flex u-font-26 u-p-t-10 u-p-b-10 text-no-wrap">{{''+(item.m6-item.m7)+"/"+(item.$orig.attendance||0)+''}}</view><view class="u-font-26">{{'教练：'+(item.$orig.coachName||"-")+''}}</view><view class="u-font-26">{{'开课时间：'+(item.$orig.classTime||"-")+''}}</view><view class="u-font-26">{{'课程时长：'+(item.$orig.classLength||"-")+"分钟 ￥"+(item.$orig.price||"-")+''}}</view></view></view><view class="btn-wrap" style="z-index:1;"><block wx:if="{{item.m8}}"><view class="group-course-btn group-course-btn-end" style="background-color:#555;">已结束</view></block><block wx:else><block wx:if="{{item.$orig.remainder>0}}"><navigator class="group-course-btn group-course-btn-yuyue" url="{{'/pages/yu-yue/tuanKe?id='+item.$orig.groupCourseId}}">预约</navigator></block><block wx:else><view class="group-course-btn group-course-btn-full" style="background-color:#555;">已满员</view></block></block></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width"></image><view class="u-p-t-10 u-font-30 u-tips-color">暂无课程</view></view></block></view></view><u-picker vue-id="{{('38e8ed72-7')+','+('38e8ed72-1')}}" show="{{currentCourseShow}}" closeOnClickOverlay="{{true}}" columns="{{[currentCourse]}}" keyName="courseName" data-event-opts="{{[['^close',[['e1']]],['^cancel',[['e2']]],['^confirm',[['chooseCourse']]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:__l="__l"></u-picker><zw-tab-bar vue-id="{{('38e8ed72-8')+','+('38e8ed72-1')}}" selIdx="{{2}}" bigIdx="{{2}}" bind:__l="__l"></zw-tab-bar></view></theme-wrap>