<u-transition vue-id="da5566a6-1" mode="slide-down" customStyle="{{containerStyle}}" show="{{open}}" class="data-v-099d0df0" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['u-notify','data-v-099d0df0','u-notify--'+tmpConfig.type]}}" style="{{$root.s0}}"><block wx:if="{{tmpConfig.safeAreaInsetTop}}"><u-status-bar vue-id="{{('da5566a6-2')+','+('da5566a6-1')}}" class="data-v-099d0df0" bind:__l="__l"></u-status-bar></block><view class="u-notify__warpper data-v-099d0df0"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><block wx:if="{{$root.g0}}"><u-icon vue-id="{{('da5566a6-3')+','+('da5566a6-1')}}" name="{{tmpConfig.icon}}" color="{{tmpConfig.color}}" size="{{1.3*tmpConfig.fontSize}}" customStyle="{{({marginRight:'4px'})}}" class="data-v-099d0df0" bind:__l="__l"></u-icon></block></block><text class="u-notify__warpper__text data-v-099d0df0" style="{{'font-size:'+($root.g1)+';'+('color:'+(tmpConfig.color)+';')}}">{{tmpConfig.message}}</text></view></view></u-transition>