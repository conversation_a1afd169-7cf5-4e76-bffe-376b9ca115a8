<theme-wrap scoped-slots-compiler="augmented" vue-id="5b266853-1" class="data-v-28c274a9" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-28c274a9"><u-navbar vue-id="{{('5b266853-2')+','+('5b266853-1')}}" title="人脸下发" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-28c274a9" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-28c274a9"><block wx:if="{{$root.g0}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-28c274a9"><view class="u-flex u-col-center u-row-start data-v-28c274a9" style="flex-wrap:no-wrap;overflow:hidden;"><view class="overflow-hidden flex-0 border-16 data-v-28c274a9" style="width:140rpx;height:140rpx;line-height:0;"><image class="h-100 data-v-28c274a9" src="{{item.imageUrl}}" mode="heightFix"></image></view><view class="w-100 u-p-l-20 data-v-28c274a9"><view class="u-tips-color u-font-26 data-v-28c274a9" style="margin-top:30rpx;">{{'类型：'+(item.type=='0'?'白名单':'黑名单')+''}}</view><view class="u-tips-color u-font-26 data-v-28c274a9" style="margin-top:10rpx;">状态：<block wx:if="{{item.status==88}}"><label class="_span data-v-28c274a9">待发送</label></block><block wx:if="{{item.status==1}}"><label class="_span data-v-28c274a9">已发送</label></block><block wx:if="{{item.status==0}}"><label class="_span data-v-28c274a9">成功</label></block><block wx:if="{{item.status==-1}}"><label class="_span data-v-28c274a9">失败</label></block></view><view class="u-tips-color u-font-26 data-v-28c274a9" style="margin-top:10rpx;">{{'开始时间：'+item.startTime+''}}</view><view class="u-tips-color u-font-26 data-v-28c274a9" style="margin-top:10rpx;">{{'结束时间：'+item.endTime+''}}</view><view class="u-tips-color u-font-26 data-v-28c274a9" style="margin-top:10rpx;">{{'创建时间：'+item.createTime+''}}</view></view></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-28c274a9"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-28c274a9"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-28c274a9">暂无用户</view></view></block></view><view class="bottom-blk bg-fff w-100 u-p-40 data-v-28c274a9"><u-button vue-id="{{('5b266853-3')+','+('5b266853-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['toAddCourse']]]]}}" bind:click="__e" class="data-v-28c274a9" bind:__l="__l" vue-slots="{{['default']}}">添加人脸</u-button></view></view></theme-wrap>