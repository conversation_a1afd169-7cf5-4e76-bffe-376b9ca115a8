<theme-wrap scoped-slots-compiler="augmented" vue-id="91f05cca-1" class="data-v-17576323" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-17576323"><u-navbar vue-id="{{('91f05cca-2')+','+('91f05cca-1')}}" title="会员卡类型管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-17576323" bind:__l="__l"></u-navbar></view><scroll-view class="scrollView data-v-17576323" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:if="{{showShop}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 u-m-t-40 u-m-l-40 data-v-17576323" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view></block><block wx:if="{{$root.g0>0}}"><view class="data-v-17576323"><block wx:for="{{$root.l0}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view class="coupon border-16 redCoupon data-v-17576323" style="{{'background:'+(list.m3['couponColor'])+';'}}"><view class="left data-v-17576323"></view><view class="right u-flex data-v-17576323"><view class="couponTitle data-v-17576323">{{list.$orig.cardName}}</view><view class="couponValue u-flex w-100 data-v-17576323"><view class="data-v-17576323">{{"￥"+list.$orig.cardPrice}}</view><view data-event-opts="{{[['tap',[['editCoupon',['$0'],[[['cardList','',index,'memberCardId']]]]]]]}}" class="pushRight data-v-17576323" bindtap="__e"><u-icon vue-id="{{('91f05cca-3-'+index)+','+('91f05cca-1')}}" name="arrow-right" size="16" color="#fff" class="data-v-17576323" bind:__l="__l"></u-icon></view></view><view class="couponBotton u-flex w-100 data-v-17576323"><view class="couponBottonView line data-v-17576323">{{"有效天数："+list.$orig.validDays}}</view><view class="couponBottonView line data-v-17576323">{{"总次数："+list.f0}}</view><view class="couponBottonView data-v-17576323">{{"卡类型："+list.f1}}</view></view></view></view></block></view></block><block wx:else><u-empty vue-id="{{('91f05cca-4')+','+('91f05cca-1')}}" marginTop="150" mode="data" class="data-v-17576323" bind:__l="__l"></u-empty></block><u-picker vue-id="{{('91f05cca-5')+','+('91f05cca-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-17576323" bind:__l="__l"></u-picker></scroll-view><view class="bottonBtn u-flex data-v-17576323"><view data-event-opts="{{[['tap',[['activateCard']]]]}}" class="moreBtn data-v-17576323" style="{{'color:'+($root.m4['buttonLightBgColor'])+';'+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">用户开卡</view><view data-event-opts="{{[['tap',[['addCoupon']]]]}}" class="addHuiYuan data-v-17576323" style="{{'background:'+($root.m6['buttonLightBgColor'])+';'+('color:'+($root.m7['buttonTextColor'])+';')+('border-color:'+($root.m8['buttonLightBgColor'])+';')}}" bindtap="__e">添加会员卡类型</view></view></view></theme-wrap>