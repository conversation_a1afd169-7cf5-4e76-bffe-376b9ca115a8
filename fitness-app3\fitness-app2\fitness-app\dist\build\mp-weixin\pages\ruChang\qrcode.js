(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/ruChang/qrcode"],{25980:function(){},75234:function(e,n,t){"use strict";var o=t(51372)["default"],a=t(81715)["createPage"];t(96910);r(t(923));var u=r(t(86530));function r(e){return e&&e.__esModule?e:{default:e}}o.__webpack_require_UNI_MP_PLUGIN__=t,a(u.default)},86530:function(e,n,t){"use strict";t.r(n),t.d(n,{__esModule:function(){return c.__esModule},default:function(){return m}});var o,a={uNavbar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(t.bind(t,66372))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(t,78278))}},u=function(){var e=this,n=e.$createElement,t=(e._self._c,e.$hasSSP("bc804e3c-1")),o=t?{color:e.$getSSP("bc804e3c-1","content")["buttonTextColor"]}:null,a=t?e.$getSSP("bc804e3c-1","content"):null,u=!t||e.loading||e.memberId?null:e.$getSSP("bc804e3c-1","content"),r=!t||e.loading||e.memberId?null:e.$getSSP("bc804e3c-1","content");e.$mp.data=Object.assign({},{$root:{m0:t,a0:o,m1:a,m2:u,m3:r}})},r=[],c=t(90663),l=c["default"],i=t(25980),d=t.n(i),s=(d(),t(18535)),f=(0,s["default"])(l,u,r,!1,null,null,null,!1,a,o),m=f.exports},90663:function(e,n,t){"use strict";var o=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var a=c(t(49873)),u=c(t(85914)),r=c(t(68466));function c(e){return e&&e.__esModule?e:{default:e}}n["default"]={data:function(){return{list:[],qrcode:"",loading:!0,shopId:"",memberId:null,qrcodeUrl:""}},onShow:function(){this.loadData()},onLoad:function(){this.shopId=o.getStorageSync("nowShopId"),this.shopId||o.showToast({title:"请先选择门店",icon:"none",duration:1200,mask:!0,complete:function(){setTimeout((function(){o.navigateBack()}),1200)}})},methods:{loadData:function(){var e=this;r.default.getMyCardPayment({data:{}}).then((function(n){if(200==n.code){console.log(n);var t=n.rows;t.length>0&&t[0].restDays>0?(e.memberId=t[0].memberId,e.generateQrcode(t[0].memberId)):e.loading=!1}}))},generateQrcode:function(e){var n=this;if(e){var t=(0,a.default)(0,"L");t.addData(String(e)),t.make();var o=t.createSvgTag({cellColor:"#000",margin:0}),r="data:image/svg+xml;base64,"+u.default.encode(o);this.qrcodeUrl=r,this.$nextTick((function(){n.loading=!1}))}}}}}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],(function(){return n(75234)}));e.O()}]);