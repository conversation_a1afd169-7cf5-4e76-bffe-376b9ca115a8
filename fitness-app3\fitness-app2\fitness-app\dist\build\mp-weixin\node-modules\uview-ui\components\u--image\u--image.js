"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u--image/u--image"],{66307:function(e,n,u){var t=u(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var o=i(u(75515));function i(e){return e&&e.__esModule?e:{default:e}}var a=function(){u.e("node-modules/uview-ui/components/u-image/u-image").then(function(){return resolve(u(11590))}.bind(null,u))["catch"](u.oe)};n["default"]={name:"u--image",mixins:[t.$u.mpMixin,o.default,t.$u.mixin],components:{uvImage:a}}},84027:function(e,n,u){var t;u.r(n),u.d(n,{__esModule:function(){return l.__esModule},default:function(){return r}});var o,i=function(){var e=this,n=e.$createElement;e._self._c},a=[],l=u(66307),c=l["default"],s=u(18535),f=(0,s["default"])(c,i,a,!1,null,null,null,!1,t,o),r=f.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u--image/u--image-create-component"],{},function(e){e("81715")["createComponent"](e(84027))}]);