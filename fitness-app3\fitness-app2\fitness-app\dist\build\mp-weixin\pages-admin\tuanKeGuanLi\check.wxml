<theme-wrap scoped-slots-compiler="augmented" vue-id="31b98dd4-1" class="data-v-fd096338" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-sticky vue-id="{{('31b98dd4-2')+','+('31b98dd4-1')}}" offset-top="0" class="data-v-fd096338" bind:__l="__l" vue-slots="{{['default']}}"><view class="bg-fff data-v-fd096338"><u-tabs vue-id="{{('31b98dd4-3')+','+('31b98dd4-2')}}" list="{{tabList}}" name="title" lineColor="{{$root.m1['buttonLightBgColor']}}" activeStyle="{{({fontWeight:'bold'})}}" scrollable="{{false}}" current="{{current}}" data-event-opts="{{[['^change',[['changeTabs']]]]}}" bind:change="__e" class="data-v-fd096338" bind:__l="__l"></u-tabs></view></u-sticky><view class="container u-p-t-40 u-p-b-40 data-v-fd096338"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-fd096338"><view class="u-flex u-col-center u-row-start data-v-fd096338" style="flex-wrap:no-wrap;overflow:hidden;"><view class="u-p-r-10 data-v-fd096338"><view class="overflow-hidden flex-0 border-16 data-v-fd096338" style="width:180rpx;height:140rpx;line-height:0;"><image class="h-100 data-v-fd096338" src="{{item.$orig.banner}}" mode="heightFix"></image></view><view class="u-tips-color u-text-center u-font-26 data-v-fd096338">开课时间<view class="u-text-center data-v-fd096338">{{''+(item.$orig.classTime||"")+''}}</view></view></view><view class="w-100 u-p-l-20 data-v-fd096338"><view class="u-line-1 w-100 u-p-b-10 u-font-34 font-bold data-v-fd096338">{{''+item.$orig.title+''}}</view><view class="u-p-b-10 u-font-28 data-v-fd096338">{{'用户昵称：'+(item.$orig.userName||"")+''}}</view><view class="u-p-b-10 u-font-28 data-v-fd096338">{{'用户手机：'+item.m2+''}}</view><view class="u-p-b-10 u-font-28 data-v-fd096338">{{'教练昵称：'+(item.$orig.coachName||"")+''}}</view><view class="u-p-b-10 u-font-28 data-v-fd096338">{{'教练联系号码：'+(item.$orig.phone||"")+''}}</view></view></view><view class="btn-wrap data-v-fd096338"><block wx:if="{{item.$orig.courseStatus==1}}"><view data-event-opts="{{[['tap',[['checkCourse',['$0','$1'],[[['list','',index,'memberId']],[['list','',index,'title']]]]]]]}}" class="u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 border-8 btc text-no-wrap lbc u-font-26 font-bold data-v-fd096338" bindtap="__e">签到</view></block></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-fd096338"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-fd096338"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-fd096338">暂无预约</view></view></block></view></view></theme-wrap>