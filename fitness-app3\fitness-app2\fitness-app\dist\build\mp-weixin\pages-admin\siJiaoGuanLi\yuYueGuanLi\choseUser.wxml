<theme-wrap scoped-slots-compiler="augmented" vue-id="0269a3b4-1" class="data-v-5ace1d6c" bind:__l="__l" vue-slots="{{['content']}}"><view class="con data-v-5ace1d6c" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('0269a3b4-2')+','+('0269a3b4-1')}}" title="选择用户" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-5ace1d6c" bind:__l="__l"></u-navbar><view class="searchView data-v-5ace1d6c" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('0269a3b4-3')+','+('0269a3b4-1')}}" showAction="{{false}}" placeholder="请输入会员昵称" animation="{{true}}" data-event-opts="{{[['^change',[['searchView']]]]}}" bind:change="__e" class="data-v-5ace1d6c" bind:__l="__l"></u-search></view><view data-event-opts="{{[['tap',[['clickSuspension',['$event']]]]]}}" class="suspension data-v-5ace1d6c" style="{{'background:'+($root.m4['buttonLightBgColor'])+';'}}" bindtap="__e"><u-icon vue-id="{{('0269a3b4-4')+','+('0269a3b4-1')}}" name="arrow-right-double" size="60rpx" color="#fff" data-event-opts="{{[['^click',[['clickSuspension']]]]}}" bind:click="__e" class="data-v-5ace1d6c" bind:__l="__l"></u-icon></view><u-popup vue-id="{{('0269a3b4-5')+','+('0269a3b4-1')}}" show="{{show}}" mode="left" data-event-opts="{{[['^close',[['close']]],['^open',[['open']]]]}}" bind:close="__e" bind:open="__e" class="data-v-5ace1d6c" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$root.g0>0}}"><view class="u-m-t-80 u-p-l-20 u-p-r-20 shopView data-v-5ace1d6c"><view class="title data-v-5ace1d6c">选择场馆</view><block wx:for="{{shopList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view class="{{['u-p-t-20','u-p-b-20','u-border-bottom','u-flex','data-v-5ace1d6c',index==nowChoseShop?'active':'']}}"><view data-event-opts="{{[['tap',[['choseShop',['$0',index],[[['shopList','',index]]]]]]]}}" class="u-flex-1 data-v-5ace1d6c" bindtap="__e"><view class="u-text-center data-v-5ace1d6c">{{list.shopName}}</view></view></view></block></view></block></u-popup><u-gap vue-id="{{('0269a3b4-6')+','+('0269a3b4-1')}}" height="1" bgColor="#bbb" class="data-v-5ace1d6c" bind:__l="__l"></u-gap><scroll-view class="scrollView data-v-5ace1d6c" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="lit" wx:for-index="index" wx:key="*this"><view class="userList data-v-5ace1d6c"><view class="user_avatar data-v-5ace1d6c"><u-avatar vue-id="{{('0269a3b4-7-'+index)+','+('0269a3b4-1')}}" size="80rpx" src="{{lit.f0}}" shape="circle" class="data-v-5ace1d6c" bind:__l="__l"></u-avatar></view><view data-event-opts="{{[['tap',[['addHuiYuan',['$0'],['showList.'+index+'']]]]]}}" class="user_textView data-v-5ace1d6c" bindtap="__e"><text class="data-v-5ace1d6c">{{lit.$orig.nickName}}</text><view class="user_tag data-v-5ace1d6c">{{"所属场馆："+lit.$orig.companyName}}</view></view><view class="user_rightView data-v-5ace1d6c"><u-icon vue-id="{{('0269a3b4-8-'+index)+','+('0269a3b4-1')}}" name="arrow-right" class="data-v-5ace1d6c" bind:__l="__l"></u-icon></view></view></block></scroll-view></view></theme-wrap>