(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["layout/theme-wrap","pages/user/edit"],{12556:function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o["default"]=void 0;var n=t(45013);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function u(e,o){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);o&&(n=n.filter((function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable}))),t.push.apply(t,n)}return t}function i(e){for(var o=1;o<arguments.length;o++){var t=null!=arguments[o]?arguments[o]:{};o%2?u(Object(t),!0).forEach((function(o){a(e,o,t[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach((function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(t,o))}))}return e}function a(e,o,t){return(o=l(o))in e?Object.defineProperty(e,o,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[o]=t,e}function l(e){var o=c(e,"string");return"symbol"==r(o)?o:o+""}function c(e,o){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,o||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(e)}o["default"]={computed:i({},(0,n.mapGetters)(["themeConfig"])),props:{isTab:{type:Boolean,default:!1}},mounted:function(){console.log(this.themeConfig)}}},31051:function(e,o,t){"use strict";var n;t.r(o),t.d(o,{__esModule:function(){return a.__esModule},default:function(){return f}});var r,u=function(){var e=this,o=e.$createElement;e._self._c;e.$initSSP(),"augmented"===e.$scope.data.scopedSlotsCompiler&&e.$setSSP("content",{logo:e.themeConfig.logo,bgColor:e.themeConfig.baseBgColor,color:e.themeConfig.baseColor,buttonBgColor:e.themeConfig.buttonBgColor,buttonTextColor:e.themeConfig.buttonTextColor,buttonLightBgColor:e.themeConfig.buttonLightBgColor,navBarColor:e.themeConfig.navBarColor,navBarTextColor:e.themeConfig.navBarTextColor,couponColor:e.themeConfig.couponColor}),e.$callSSP()},i=[],a=t(12556),l=a["default"],c=t(69601),s=t.n(c),d=(s(),t(18535)),m=(0,d["default"])(l,u,i,!1,null,"5334cd47",null,!1,n,r),f=m.exports},39724:function(e,o,t){"use strict";t.r(o),t.d(o,{__esModule:function(){return a.__esModule},default:function(){return f}});var n,r={uNavbar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(t.bind(t,66372))},uNoNetwork:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-no-network/u-no-network")]).then(t.bind(t,43763))},uToast:function(){return t.e("node-modules/uview-ui/components/u-toast/u-toast").then(t.bind(t,67559))},uNotify:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-notify/u-notify")]).then(t.bind(t,11380))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(t,78278))},"u-Form":function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u--form/u--form")]).then(t.bind(t,26763))},uFormItem:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-form-item/u-form-item")]).then(t.bind(t,60088))},"u-Input":function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u--input/u--input")]).then(t.bind(t,59242))},uRadioGroup:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-radio-group/u-radio-group")]).then(t.bind(t,97231))},uRadio:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-radio/u-radio")]).then(t.bind(t,77148))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-button/u-button")]).then(t.bind(t,65610))},uDatetimePicker:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-datetime-picker/u-datetime-picker")]).then(t.bind(t,40015))}},u=function(){var e=this,o=e.$createElement,t=(e._self._c,e.$hasSSP("96ebc1fe-1")),n=t?{color:e.$getSSP("96ebc1fe-1","content")["navBarTextColor"]}:null,r=t?e.$getSSP("96ebc1fe-1","content"):null,u=t?e.$getSSP("96ebc1fe-1","content"):null;e._isMounted||(e.e0=function(o){return e.uni.navigateBack()}),e.$mp.data=Object.assign({},{$root:{m0:t,a0:n,m1:r,m2:u}})},i=[],a=t(59900),l=a["default"],c=t(98967),s=t.n(c),d=(s(),t(18535)),m=(0,d["default"])(l,u,i,!1,null,null,null,!1,r,n),f=m.exports},40433:function(e,o,t){"use strict";var n=t(51372)["default"],r=t(81715)["createPage"];t(96910);i(t(923));var u=i(t(39724));function i(e){return e&&e.__esModule?e:{default:e}}n.__webpack_require_UNI_MP_PLUGIN__=t,r(u.default)},59900:function(e,o,t){"use strict";var n=t(81715)["default"];Object.defineProperty(o,"__esModule",{value:!0}),o["default"]=void 0;var r=u(t(68466));t(77020),u(t(31051));function u(e){return e&&e.__esModule?e:{default:e}}o["default"]={data:function(){return{loading:!0,showBirthday:!1,birthdate:Number(new Date),minDate:Number(new Date(1900,0,1)),btnStyle:{marginBottom:"40upx",borderRadius:"24upx",height:"100upx",color:"#000",fontSize:"32upx",fontWeight:"bold"},sexList:[{dictValue:"0",dictLabel:"男"},{dictValue:"1",dictLabel:"女"},{dictValue:"2",dictLabel:"未知"}],form:{avatar_url:"",nickName:"",phonenumber:"",sex:"2"},rules:{nickName:[{required:!0,message:"请输入姓名",trigger:"blur, change"},{max:20,message:"最多20个字",trigger:"blur, change"}]}}},onLoad:function(){var e=this;r.default.getDataType({data:{companyId:1},dictType:"sys_user_sex",method:"GET"}).then((function(o){console.log(o),200==o.code&&(e.sexList=o.data),e.$nextTick((function(){e.loadData()}))})).catch((function(){e.loadData()}))},onReady:function(){this.$refs.Form.setRules(this.rules)},methods:{getInfo:function(e){console.log(e)},birthdayClose:function(){this.showBirthday=!1},birthdayConfirm:function(e){this.showBirthday=!1,this.form.birthdate=n.$u.timeFormat(e.value,"yyyy-mm-dd")},changeName:function(e){e.detail.value.trim()&&(this.form.nickName=e.detail.value)},loadData:function(){var e=this;r.default.getInfo({data:{companyId:1},method:"GET"}).then((function(o){console.log(o),200==o.code&&(e.form={avatar_url:e.$serverUrl+o.user.avatar,nickName:o.user.nickName,phonenumber:o.user.phonenumber,sex:o.user.sex})}))},chooseWxAvatar:function(e){var o=this,t=n.getStorageSync("token");n.uploadFile({url:this.$serverUrl+"/wx/avatar",filePath:e.detail.avatarUrl,name:"avatarfile",header:{Authorization:t},success:function(e){console.log(e.data);var t=JSON.parse(e.data),n=t.imgUrl;console.log(n),r.default.putUser({data:{avatar:n},method:"PUT"}).then((function(e){o.form.avatar_url=o.$serverUrl+n,console.log(e)}))},fail:function(e){console.log(e)},complete:function(){}})},submit:function(){var e=this;console.log(this.form),this.$refs.Form.validate().then((function(o){o?e.save():console.log("验证失败")})).catch((function(e){console.log(e)}))},save:function(){var e=this;n.showLoading({mask:!0,title:"修改信息中……"}),r.default.putUser({data:this.form,method:"PUT"}).then((function(e){console.log(e),n.hideLoading(),n.reLaunch({url:"/pages/index/index"})})).catch((function(o){n.hideLoading(),e.$refs.uNotify.show({message:"修改失败!",type:"error",duration:"1500"})}))}},onPullDownRefresh:function(){this.loadData(),n.stopPullDownRefresh()}}},69601:function(){},98967:function(){}},function(e){var o=function(o){return e(e.s=o)};e.O(0,["common/vendor"],(function(){return o(40433)}));e.O()}]);