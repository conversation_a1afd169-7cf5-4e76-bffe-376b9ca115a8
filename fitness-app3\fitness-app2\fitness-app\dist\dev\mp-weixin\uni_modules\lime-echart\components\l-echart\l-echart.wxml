<block wx:if="{{canvasId}}"><view class="lime-echart vue-ref" style="{{$root.s0}}" aria-label="{{ariaLabel}}" data-ref="limeEchart"><block wx:if="{{use2dCanvas}}"><canvas class="lime-echart__canvas" style="{{(canvasStyle)}}" type="2d" id="{{canvasId}}" disable-scroll="{{isDisableScroll}}" data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></canvas></block><block wx:else><canvas class="lime-echart__canvas" style="{{(canvasStyle)}}" width="{{nodeWidth}}" height="{{nodeHeight}}" canvas-id="{{canvasId}}" id="{{canvasId}}" disable-scroll="{{isDisableScroll}}" data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></canvas></block><block wx:if="{{isPC}}"><view data-event-opts="{{[['mousedown',[['touchStart',['$event']]]],['mousemove',[['touchMove',['$event']]]],['mouseup',[['touchEnd',['$event']]]],['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" class="lime-echart__mask" bindmousedown="__e" bindmousemove="__e" bindmouseup="__e" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></view></block><block wx:if="{{isOffscreenCanvas}}"><canvas style="{{(offscreenStyle)}}" canvas-id="{{offscreenCanvasId}}"></canvas></block></view></block>