<theme-wrap scoped-slots-compiler="augmented" vue-id="3d986150-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('3d986150-2')+','+('3d986150-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="入场记录" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40"><picker class="u-m-b-40" mode="selector" range="{{changGuanList}}" range-key="label" value="{{currentIndex}}" data-event-opts="{{[['change',[['changeChangGuan',['$event']]]]]}}" bindchange="__e"><view class="w-100 u-flex u-row-between u-col-center"><view class="{{['u-font-36','font-bold','u-line-1',(!currentChangGuan)?'u-tips-color':'']}}">{{currentChangGuan||"请选择场馆"}}</view><u-icon vue-id="{{('3d986150-3')+','+('3d986150-1')}}" name="arrow-right" size="21" color="#999999" bind:__l="__l"></u-icon></view></picker><view class="u-p-t-40 u-border-top"><view class="u-p-40 bg-fff border-16"><block wx:if="{{$root.g0}}"><view class="u-tips-color u-flex u-row-end font-bold u-font-24 u-m-b-30">共<text class="u-m-l-10 u-m-r-10 u-font-30" style="color:#000;">{{amount}}</text>记录</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-m-b-30 item-blk u-flex u-row-satrt u-col-start"><view class="u-flex-1 u-p-r-20"><image class="w-100" style="height:60rpx;border-radius:50%;" src="{{item.$orig.avatar}}" mode="widthFix"></image></view><view class="u-p-20 u-flex-6 u-flex u-row-between u-relative border-16 bgc checkin-info-item"><view>{{item.$orig.created_at}}</view><view class="u-tips-color u-font-26">{{"第"+(index+1)+"条"}}</view><view class="u-absolute" style="left:-17rpx;top:16rpx;"><u-icon vue-id="{{('3d986150-4-'+index)+','+('3d986150-1')}}" name="arrow-down-fill" size="18" color="{{item.m2['bgColor']}}" bind:__l="__l"></u-icon></view></view></view></block></block><block wx:else><block wx:if="{{$root.g1}}"><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/history.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无记录</view></view></block></block><view hidden="{{!(loading)}}"></view></view></view></view></view></theme-wrap>