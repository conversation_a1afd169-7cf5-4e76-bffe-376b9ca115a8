<theme-wrap scoped-slots-compiler="augmented" vue-id="63c236b9-1" class="data-v-7db17026" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-tabs vue-id="{{('63c236b9-2')+','+('63c236b9-1')}}" list="{{tabList}}" name="title" lineColor="{{$root.m1['buttonLightBgColor']}}" activeStyle="{{({fontWeight:'bold'})}}" scrollable="{{false}}" current="{{current}}" data-event-opts="{{[['^change',[['changeTabs']]]]}}" bind:change="__e" class="data-v-7db17026" bind:__l="__l"></u-tabs><view class="u-flex u-p-b-10 u-border-bottom data-v-7db17026"><view class="w-100 u-flex-1 data-v-7db17026"><picker class="w-100 data-v-7db17026" mode="date" end="{{maxDate}}" value="{{maxDate}}" data-event-opts="{{[['change',[['changeDate',['$event','startDate']]]]]}}" bindchange="__e"><view class="u-tips-color u-flex w-100 u-row-center u-p-10 data-v-7db17026"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-line-1 u-p-r-10 data-v-7db17026" bindtap="__e">{{''+(form.startDate||"开始时间")+''}}</view><block wx:if="{{!form.endData}}"><u-icon vue-id="{{('63c236b9-3')+','+('63c236b9-1')}}" name="calendar" color="#999" size="18" class="data-v-7db17026" bind:__l="__l"></u-icon></block><block wx:else><u-icon vue-id="{{('63c236b9-4')+','+('63c236b9-1')}}" name="close-fill" color="#999" size="18" class="data-v-7db17026" bind:__l="__l"></u-icon></block></view></picker></view><view class="w-100 u-flex-1 data-v-7db17026"><picker class="w-100 data-v-7db17026" mode="date" end="{{maxDate}}" value="{{maxDate}}" data-event-opts="{{[['change',[['changeDate',['$event','endDate']]]]]}}" bindchange="__e"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-tips-color u-flex w-100 u-row-center u-p-10 data-v-7db17026" bindtap="__e"><view class="u-line-1 u-p-r-10 data-v-7db17026">{{''+(form.endDate||"结束时间")+''}}</view><block wx:if="{{!form.endData}}"><u-icon vue-id="{{('63c236b9-5')+','+('63c236b9-1')}}" name="calendar" color="#999" size="18" class="data-v-7db17026" bind:__l="__l"></u-icon></block><block wx:else><u-icon vue-id="{{('63c236b9-6')+','+('63c236b9-1')}}" name="close-fill" color="#999" size="18" class="data-v-7db17026" bind:__l="__l"></u-icon></block></view></picker></view></view><view class="data-v-7db17026"><echart vue-id="{{('63c236b9-7')+','+('63c236b9-1')}}" option="{{option}}" exportBase64="{{true}}" data-ref="chart" class="data-v-7db17026 vue-ref" bind:__l="__l"></echart><block wx:if="{{image}}"><image src="{{image}}" class="data-v-7db17026"></image></block></view></view></theme-wrap>