<view class="page"><u-no-network vue-id="1e799ffe-1" bind:__l="__l"></u-no-network><u-toast class="vue-ref" vue-id="1e799ffe-2" data-ref="uToast" bind:__l="__l"></u-toast><u-notify class="vue-ref" vue-id="1e799ffe-3" data-ref="uNotify" bind:__l="__l"></u-notify><block wx:if="{{loading}}"><view class="loading"><u-loading-page vue-id="1e799ffe-4" loading="{{loading}}" bind:__l="__l"></u-loading-page></view></block><block wx:else><view class="content"><view class="authorize-page"><view class="logo"><u--image vue-id="1e799ffe-5" src="@/static/images/logo.png" mode="widthFix" width="150rpx" fade="{{true}}" duration="1000" bind:__l="__l"></u--image></view><view class="auth-title">绑定手机号</view><view class="auth-info">获取并绑定您的手机号</view><view class="auth-btn"><u-button vue-id="1e799ffe-6" open-type="getPhoneNumber" type="warning" ripple="{{true}}" loading="{{btnLoading}}" data-event-opts="{{[['^getphonenumber',[['getPhoneNumber']]]]}}" bind:getphonenumber="__e" bind:__l="__l" vue-slots="{{['default']}}">绑定手机</u-button></view></view></view></block></view>