<theme-wrap scoped-slots-compiler="augmented" vue-id="2bf68c72-1" class="data-v-56ede972" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('2bf68c72-2')+','+('2bf68c72-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="{{title}}" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" class="data-v-56ede972" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-56ede972"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-56ede972"><view class="u-flex u-col-center u-row-start data-v-56ede972" style="flex-wrap:no-wrap;overflow:hidden;"><view class="overflow-hidden flex-0 border-16 data-v-56ede972" style="width:160rpx;height:140rpx;line-height:0;"><image class="h-100 data-v-56ede972" src="{{item.$orig.banner}}" mode="heightFix"></image></view><view class="w-100 u-p-l-20 data-v-56ede972"><view class="u-p-b-10 u-font-28 data-v-56ede972">{{'用户昵称：'+(item.$orig.memberName||"")+''}}</view><view class="u-p-b-10 u-font-28 data-v-56ede972">{{'用户手机：'+item.m2+''}}</view><block wx:if="{{item.$orig.bookingStatus=='0'}}"><view class="booking-status data-v-56ede972" style="background:cornflowerblue;">预约中</view></block><block wx:if="{{item.$orig.bookingStatus=='1'}}"><view class="booking-status data-v-56ede972" style="background:aquamarine;">已预约</view></block><block wx:if="{{item.$orig.bookingStatus=='2'}}"><view class="booking-status data-v-56ede972" style="background:aquamarine;">已签到</view></block><block wx:if="{{item.$orig.bookingStatus=='3'}}"><view class="booking-status data-v-56ede972" style="background:azure;">己取消</view></block></view></view><view class="btn-wrap data-v-56ede972"><block wx:if="{{item.$orig.courseStatus==1}}"><view data-event-opts="{{[['tap',[['checkCourse',['$0','$1'],[[['list','',index,'memberId']],[['list','',index,'title']]]]]]]}}" class="u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 border-8 btc text-no-wrap lbc u-font-26 font-bold data-v-56ede972" bindtap="__e">签到</view></block></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-56ede972"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-56ede972"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-56ede972">暂无预约</view></view></block></view></view></theme-wrap>