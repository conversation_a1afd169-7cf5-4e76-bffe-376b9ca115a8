(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-button/u-button"],{29439:function(t,o,e){"use strict";var i=e(81715)["default"];Object.defineProperty(o,"__esModule",{value:!0}),o["default"]=void 0;var n=l(e(8555)),r=l(e(29103)),u=l(e(366));function l(t){return t&&t.__esModule?t:{default:t}}o["default"]={name:"u-button",mixins:[i.$u.mpMixin,i.$u.mixin,n.default,r.default,u.default],data:function(){return{}},computed:{bemClass:function(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor:function(){return this.plain?this.color?this.color:i.$u.config.color["u-".concat(this.type)]:"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom:function(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor:function(){var t={};return this.color&&(t.color=this.plain?this.color:"white",this.plain||(t["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(t.borderTopWidth=0,t.borderRightWidth=0,t.borderBottomWidth=0,t.borderLeftWidth=0,this.plain||(t.backgroundImage=this.color)):(t.borderColor=this.color,t.borderWidth="1px",t.borderStyle="solid")),t},nvueTextStyle:function(){var t={};return"info"===this.type&&(t.color="#323233"),this.color&&(t.color=this.plain?this.color:"white"),t.fontSize=this.textSize+"px",t},textSize:function(){var t=14,o=this.size;return"large"===o&&(t=16),"normal"===o&&(t=14),"small"===o&&(t=12),"mini"===o&&(t=10),t}},methods:{clickHandler:function(){var t=this;this.disabled||this.loading||i.$u.throttle((function(){t.$emit("click")}),this.throttleTime)},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)},agreeprivacyauthorization:function(t){this.$emit("agreeprivacyauthorization",t)}}}},33230:function(){},65610:function(t,o,e){"use strict";e.r(o),e.d(o,{__esModule:function(){return l.__esModule},default:function(){return f}});var i,n={uLoadingIcon:function(){return Promise.all([e.e("common/vendor"),e.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(e.bind(e,94597))},uIcon:function(){return Promise.all([e.e("common/vendor"),e.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(e.bind(e,78278))}},r=function(){var t=this,o=t.$createElement,e=(t._self._c,t.__get_style([t.baseColor,t.$u.addStyle(t.customStyle)])),i=Number(t.hoverStartTime),n=Number(t.hoverStayTime);t.$mp.data=Object.assign({},{$root:{s0:e,m0:i,m1:n}})},u=[],l=e(29439),s=l["default"],c=e(33230),a=e.n(c),h=(a(),e(18535)),d=(0,h["default"])(s,r,u,!1,null,"30da201b",null,!1,n,i),f=d.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-button/u-button-create-component"],{},function(t){t("81715")["createComponent"](t(65610))}]);