(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-column-notice/u-column-notice"],{13235:function(e,n,t){"use strict";t.r(n),t.d(n,{__esModule:function(){return l.__esModule},default:function(){return m}});var o,u={uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(t,78278))}},i=function(){var e=this,n=e.$createElement,t=(e._self._c,e.__get_style([e.textStyle])),o=["link","closable"].includes(e.mode);e.$mp.data=Object.assign({},{$root:{s0:t,g0:o}})},c=[],l=t(61145),r=l["default"],s=t(34779),a=t.n(s),d=(a(),t(18535)),f=(0,d["default"])(r,i,c,!1,null,"6e2112fe",null,!1,u,o),m=f.exports},34779:function(){},61145:function(e,n,t){"use strict";var o=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var u=i(t(85263));function i(e){return e&&e.__esModule?e:{default:e}}n["default"]={mixins:[o.$u.mpMixin,o.$u.mixin,u.default],watch:{text:{immediate:!0,handler:function(e,n){o.$u.test.array(e)||o.$u.error("noticebar组件direction为column时，要求text参数为数组形式")}}},computed:{textStyle:function(){var e={};return e.color=this.color,e.fontSize=o.$u.addUnit(this.fontSize),e},vertical:function(){return"horizontal"!=this.mode}},data:function(){return{index:0}},methods:{noticeChange:function(e){this.index=e.detail.current},clickHandler:function(){this.$emit("click",this.index)},close:function(){this.$emit("close")}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-column-notice/u-column-notice-create-component"],{},function(e){e("81715")["createComponent"](e(13235))}]);