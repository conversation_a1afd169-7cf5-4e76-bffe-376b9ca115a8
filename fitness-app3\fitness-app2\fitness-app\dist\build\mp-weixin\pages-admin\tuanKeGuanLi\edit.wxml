<theme-wrap scoped-slots-compiler="augmented" vue-id="23031bf4-1" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast vue-id="{{('23031bf4-2')+','+('23031bf4-1')}}" data-ref="toast" class="data-v-e0a2dc00 vue-ref" bind:__l="__l"></u-toast><view class="data-v-e0a2dc00"><u-navbar vue-id="{{('23031bf4-3')+','+('23031bf4-1')}}" title="编辑" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-e0a2dc00" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-e0a2dc00"><u-form vue-id="{{('23031bf4-4')+','+('23031bf4-1')}}" model="{{form}}" labelWidth="140" data-ref="uForm" class="data-v-e0a2dc00 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e0a2dc00"><u-form-item vue-id="{{('23031bf4-5')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="title" label="团课标题" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-6')+','+('23031bf4-5')}}" inputAlign="right" border="none" placeholder="请输入团课标题" value="{{form.title}}" data-event-opts="{{[['^input',[['__set_model',['$0','title','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-7')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="attendance" label="最多人数" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-8')+','+('23031bf4-7')}}" inputAlign="right" type="number" border="none" placeholder="请输入上课人数上限" value="{{form.attendance}}" data-event-opts="{{[['^input',[['__set_model',['$0','attendance','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-9')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classTime" label="课程日期" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-e0a2dc00" style="{{'color:'+(classTimeListName?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(classTimeListName||'请选择课程日期')+''}}</view><u-calendar vue-id="{{('23031bf4-10')+','+('23031bf4-9')}}" show="{{showCalendar}}" mode="multiple" data-event-opts="{{[['^confirm',[['confirmCalendar']]]]}}" bind:confirm="__e" class="data-v-e0a2dc00" bind:__l="__l"></u-calendar></u-form-item><u-form-item vue-id="{{('23031bf4-11')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classTime" label="课程时间" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-e0a2dc00" style="{{'color:'+(form.classTime?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.classTime||'请选择课程时间')+''}}</view><u-datetime-picker vue-id="{{('23031bf4-12')+','+('23031bf4-11')}}" mode="time" show="{{timePicker}}" closeOnClickOverlay="{{true}}" minHour="5" minDate="{{minDate}}" maxHour="23" data-event-opts="{{[['^close',[['e3']]],['^confirm',[['changeTime']]],['^cancel',[['e4']]]]}}" bind:close="__e" bind:confirm="__e" bind:cancel="__e" class="data-v-e0a2dc00" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('23031bf4-13')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classLength" label="课程时长(分钟)" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-14')+','+('23031bf4-13')}}" inputAlign="right" type="number" border="none" placeholder="请输入课程时长" value="{{form.classLength}}" data-event-opts="{{[['^input',[['__set_model',['$0','classLength','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-15')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="phone" label="联系人号码" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-16')+','+('23031bf4-15')}}" inputAlign="right" border="none" placeholder="请输入联系人号码" value="{{form.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-17')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="price" label="团课价格" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-18')+','+('23031bf4-17')}}" inputAlign="right" type="digit" border="none" placeholder="请输入团课价格" value="{{form.price}}" data-event-opts="{{[['^input',[['__set_model',['$0','price','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-19')+','+('23031bf4-4')}}" required="{{true}}" borderBottom="{{true}}" prop="coachId" label="选择教练" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-e0a2dc00" style="{{'color:'+(form.coachId?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.coachName||'请选择教练')+''}}</view><u-picker vue-id="{{('23031bf4-20')+','+('23031bf4-19')}}" show="{{showCoach}}" columns="{{columnsCoach}}" keyName="nickName" data-event-opts="{{[['^cancel',[['e6']]],['^confirm',[['confirmCoach']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-e0a2dc00" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('23031bf4-21')+','+('23031bf4-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="beforeTimeBooking" label="禁止预约(课程开始前)" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-22')+','+('23031bf4-21')}}" inputAlign="right" type="number" border="none" placeholder="分钟" value="{{form.beforeTimeBooking}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeBooking','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-23')+','+('23031bf4-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="beforeTimeCancel" label="禁止取消(课程开始前)" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-24')+','+('23031bf4-23')}}" inputAlign="right" type="number" border="none" placeholder="分钟" value="{{form.beforeTimeCancel}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeCancel','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-25')+','+('23031bf4-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="minAttendance" label="最低开课人数" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-26')+','+('23031bf4-25')}}" inputAlign="right" type="number" border="none" placeholder="人数(人)" value="{{form.minAttendance}}" data-event-opts="{{[['^input',[['__set_model',['$0','minAttendance','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('23031bf4-27')+','+('23031bf4-4')}}" labelWidth="200" required="{{true}}" prop="beforeTimeClose" label="未满足开课人数关闭课程时间" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('23031bf4-28')+','+('23031bf4-27')}}" inputAlign="right" type="number" border="none" placeholder="(分钟)" value="{{form.beforeTimeClose}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeClose','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-input></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e0a2dc00"><u-form-item vue-id="{{('23031bf4-29')+','+('23031bf4-4')}}" required="{{true}}" prop="background" labelPosition="top" label="团课列表背景图片" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-e0a2dc00"><view data-event-opts="{{[['tap',[['chooseBackground',['$event']]]]]}}" class="upload-img-box data-v-e0a2dc00" catchtap="__e"><block wx:if="{{!form.background}}"><u-icon vue-id="{{('23031bf4-30')+','+('23031bf4-29')}}" name="camera" size="40" color="#ddd" class="data-v-e0a2dc00" bind:__l="__l"></u-icon></block><block wx:else><view class="u-relative w-100 h-100 data-v-e0a2dc00"><image class="w-100 data-v-e0a2dc00" src="{{form.background}}" mode="widthFix" data-event-opts="{{[['tap',[['previewBackground',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delBackground',['$event']]]]]}}" hidden="{{!(form.background)}}" class="u-absolute u-p-10 data-v-e0a2dc00" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('23031bf4-31')+','+('23031bf4-29')}}" name="close" color="#fff" size="13" class="data-v-e0a2dc00" bind:__l="__l"></u-icon></view></view></block></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e0a2dc00"><u-form-item vue-id="{{('23031bf4-32')+','+('23031bf4-4')}}" prop="banner" labelPosition="top" label="团课banner图片" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-e0a2dc00"><block wx:for="{{form.banner}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="upload-img-box data-v-e0a2dc00"><view class="u-relative w-100 h-100 data-v-e0a2dc00"><image class="w-100 data-v-e0a2dc00" src="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewBanner',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delBanner',[index]]]]]}}" class="u-absolute u-p-10 data-v-e0a2dc00" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('23031bf4-33-'+index)+','+('23031bf4-32')}}" name="close" color="#fff" size="13" class="data-v-e0a2dc00" bind:__l="__l"></u-icon></view></view></view></block><view data-event-opts="{{[['tap',[['chooseBanner',['$event']]]]]}}" class="upload-img-box data-v-e0a2dc00" catchtap="__e"><u-icon vue-id="{{('23031bf4-34')+','+('23031bf4-32')}}" name="camera" size="40" color="#ddd" class="data-v-e0a2dc00" bind:__l="__l"></u-icon></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e0a2dc00"><u-form-item vue-id="{{('23031bf4-35')+','+('23031bf4-4')}}" prop="classInfoPic" labelPosition="top" label="团课详情图片" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-e0a2dc00"><block wx:for="{{form.classInfoPic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="upload-img-box data-v-e0a2dc00"><view class="u-relative w-100 h-100 data-v-e0a2dc00"><image class="w-100 data-v-e0a2dc00" src="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewClassInfoPic',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delClassInfoPic',[index]]]]]}}" class="u-absolute u-p-10 data-v-e0a2dc00" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('23031bf4-36-'+index)+','+('23031bf4-35')}}" name="close" color="#fff" size="13" class="data-v-e0a2dc00" bind:__l="__l"></u-icon></view></view></view></block><view data-event-opts="{{[['tap',[['chooseClassInfoPic',['$event']]]]]}}" class="upload-img-box data-v-e0a2dc00" catchtap="__e"><u-icon vue-id="{{('23031bf4-37')+','+('23031bf4-35')}}" name="camera" size="40" color="#ddd" class="data-v-e0a2dc00" bind:__l="__l"></u-icon></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e0a2dc00"><u-form-item vue-id="{{('23031bf4-38')+','+('23031bf4-4')}}" required="{{true}}" prop="remark" labelPosition="top" label="课程介绍" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('23031bf4-39')+','+('23031bf4-38')}}" border="none" maxLength="300" placeholder="请输入课程介绍" value="{{form.remark}}" data-event-opts="{{[['^input',[['__set_model',['$0','remark','$event',[]],['form']]]]]}}" class="data-v-e0a2dc00" bind:__l="__l"></u-textarea></u-form-item></view></u-form></view><view class="bottom-blk bg-fff w-100 u-p-40 data-v-e0a2dc00"><u-button vue-id="{{('23031bf4-40')+','+('23031bf4-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" loading="{{disabled}}" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['createGroupCourse']]]]}}" bind:click="__e" class="data-v-e0a2dc00" bind:__l="__l" vue-slots="{{['default']}}">创建</u-button></view></view></theme-wrap>