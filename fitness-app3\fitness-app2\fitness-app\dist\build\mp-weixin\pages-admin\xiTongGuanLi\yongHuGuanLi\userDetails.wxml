<theme-wrap scoped-slots-compiler="augmented" vue-id="a51610c2-1" bind:__l="__l" vue-slots="{{['content']}}"><view class="page" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('a51610c2-2')+','+('a51610c2-1')}}" title="体征信息" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" bind:__l="__l"></u-navbar><u-no-network vue-id="{{('a51610c2-3')+','+('a51610c2-1')}}" bind:__l="__l"></u-no-network><u-toast class="vue-ref" vue-id="{{('a51610c2-4')+','+('a51610c2-1')}}" data-ref="uToast" bind:__l="__l"></u-toast><u-notify class="vue-ref" vue-id="{{('a51610c2-5')+','+('a51610c2-1')}}" data-ref="uNotify" bind:__l="__l"></u-notify><view class="content container"><view class="bg-fff border-16 u-p-t-20 u-p-b-20 u-p-r-30 u-p-l-30"><u-form class="u-p-20 vue-ref" vue-id="{{('a51610c2-6')+','+('a51610c2-1')}}" labelPosition="left" model="{{form}}" labelWidth="140" errorType="toast" data-ref="uForm" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('a51610c2-7')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="性别" prop="sex" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-text-right">{{''+gender+''}}</view></u-form-item><u-form-item vue-id="{{('a51610c2-8')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="年龄" prop="age" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-9')+','+('a51610c2-8')}}" disabledColor="#ffffff" border="none" type="number" placeholder="请输入您的年龄" value="{{form.age}}" data-event-opts="{{[['^input',[['__set_model',['$0','age','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-10')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="身高(cm)" prop="height" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-11')+','+('a51610c2-10')}}" disabledColor="#ffffff" border="none" type="digit" placeholder="请输入您的身高" value="{{form.height}}" data-event-opts="{{[['^input',[['__set_model',['$0','height','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-12')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="体重(kg)" prop="weight" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-13')+','+('a51610c2-12')}}" disabledColor="#ffffff" type="digit" border="none" placeholder="请输入您的体重" value="{{form.weight}}" data-event-opts="{{[['^input',[['__set_model',['$0','weight','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-14')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="体脂率(%)" prop="bf" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-15')+','+('a51610c2-14')}}" readonly="{{true}}" type="digit" disabledColor="#ffffff" border="none" placeholder=" " value="{{form.bf}}" data-event-opts="{{[['^input',[['__set_model',['$0','bf','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-16')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="BMI" prop="BMI" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-17')+','+('a51610c2-16')}}" readonly="{{true}}" type="digit" disabledColor="#ffffff" border="none" placeholder=" " value="{{form.BMI}}" data-event-opts="{{[['^input',[['__set_model',['$0','BMI','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-18')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="肌肉量(kg)" prop="lbm" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-19')+','+('a51610c2-18')}}" disabledColor="#ffffff" type="digit" border="none" placeholder="请输入您的肌肉量" value="{{form.lbm}}" data-event-opts="{{[['^input',[['__set_model',['$0','lbm','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-20')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="腰臀比" prop="waistToHipRatio" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-21')+','+('a51610c2-20')}}" disabledColor="#ffffff" type="digit" border="none" placeholder="请输入您的腰臀比" value="{{form.waistToHipRatio}}" data-event-opts="{{[['^input',[['__set_model',['$0','waistToHipRatio','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-22')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="胸围(cm)" prop="chestGirth" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-23')+','+('a51610c2-22')}}" type="digit" disabledColor="#ffffff" border="none" placeholder="请输入您的胸围" value="{{form.chestGirth}}" data-event-opts="{{[['^input',[['__set_model',['$0','chestGirth','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-24')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="腰围(cm)" prop="waistCircumference" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-25')+','+('a51610c2-24')}}" disabledColor="#ffffff" type="digit" border="none" placeholder="请输入您的腰围" value="{{form.waistCircumference}}" data-event-opts="{{[['^input',[['__set_model',['$0','waistCircumference','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-26')+','+('a51610c2-6')}}" borderBottom="{{true}}" label="臀围(cm)" prop="hipCircumference" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-27')+','+('a51610c2-26')}}" type="digit" disabledColor="#ffffff" border="none" placeholder="请输入您的臀围" value="{{form.hipCircumference}}" data-event-opts="{{[['^input',[['__set_model',['$0','hipCircumference','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('a51610c2-28')+','+('a51610c2-6')}}" label="骨骼肌含量(kg)" prop="smm" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('a51610c2-29')+','+('a51610c2-28')}}" type="digit" disabledColor="#ffffff" border="none" placeholder="请输入您的骨骼肌含量" value="{{form.smm}}" data-event-opts="{{[['^input',[['__set_model',['$0','smm','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item></u-form></view><view class="u-p-t-50 u-p-b-40"><u-button vue-id="{{('a51610c2-30')+','+('a51610c2-1')}}" loading="{{btnLoading}}" size="large" color="{{$root.m3['buttonLightBgColor']}}" customStyle="{{btnStyle}}" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">确认</u-button></view></view></view></theme-wrap>