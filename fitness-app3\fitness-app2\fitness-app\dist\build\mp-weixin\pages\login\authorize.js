(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/login/authorize"],{6257:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return d}});var r,o={uNoNetwork:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-no-network/u-no-network")]).then(n.bind(n,43763))},uToast:function(){return n.e("node-modules/uview-ui/components/u-toast/u-toast").then(n.bind(n,67559))},uNotify:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-notify/u-notify")]).then(n.bind(n,11380))},uLoadingPage:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loading-page/u-loading-page")]).then(n.bind(n,51873))},"u-Image":function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u--image/u--image")]).then(n.bind(n,84027))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(n,65610))}},i=function(){var t=this,e=t.$createElement;t._self._c},a=[],u=n(45461),c=u["default"],s=n(45228),f=n.n(s),l=(f(),n(18535)),h=(0,l["default"])(c,i,a,!1,null,null,null,!1,o,r),d=h.exports},45228:function(){},45461:function(t,e,n){"use strict";var r=n(81715)["default"];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(n(68466));function a(t){return t&&t.__esModule?t:{default:t}}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",f=a.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),u=new $(r||[]);return i(a,"_invoke",{value:O(t,n,u)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",y="suspendedYield",g="executing",v="completed",m={};function w(){}function b(){}function _(){}var x={};l(x,c,(function(){return this}));var L=Object.getPrototypeOf,E=L&&L(L(I([])));E&&E!==n&&r.call(E,c)&&(x=E);var S=_.prototype=w.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function n(i,a,u,c){var s=d(t[i],t,a);if("throw"!==s.type){var f=s.arg,l=f.value;return l&&"object"==o(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):e.resolve(l).then((function(t){f.value=t,u(f)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var a;i(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return a=a?a.then(o,o):o()}})}function O(e,n,r){var o=p;return function(i,a){if(o===g)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var u=r.delegate;if(u){var c=T(u,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var s=d(e,n,r);if("normal"===s.type){if(o=r.done?v:y,s.arg===m)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=v,r.method="throw",r.arg=s.arg)}}}function T(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,T(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=d(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function I(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=_,i(S,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:b,configurable:!0}),b.displayName=l(_,f,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,l(t,f,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},k(P.prototype),l(P.prototype,s,(function(){return this})),e.AsyncIterator=P,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new P(h(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(S),l(S,f,"Generator"),l(S,c,(function(){return this})),l(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=I,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return u.type="throw",u.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),N(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;N(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:I(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function c(t,e,n,r,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(t){c(i,r,o,a,u,"next",t)}function u(t){c(i,r,o,a,u,"throw",t)}a(void 0)}))}}e["default"]={data:function(){return{loading:!1,btnLoading:!1}},onShow:function(){r.getStorageSync("token")&&r.getStorageSync("session")&&r.switchTab({url:"/pages/index/index"})},onLoad:function(){},onReady:function(){},methods:{goBack:function(){r.switchTab({url:"/pages/index/index"})},authorize:function(t){this.btnLoading=!0;var e=this;r.getUserProfile({desc:"用于完善资料",success:function(t){"getUserProfile:ok"===t.errMsg?(e.logining(t.userInfo),r.setStorageSync("userInfo",t.userInfo)):(e.btnLoading=!1,e.$refs.uToast.show({message:"授权失败",type:"error",duration:"2300"}))},fail:function(){e.btnLoading=!1,e.$refs.uToast.show({message:"授权失败",type:"error",duration:"2300"})}})},logining:function(t){var e=this;return s(u().mark((function n(){var o;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,r.login();case 2:o=n.sent,o[1].code?i.default.getTokenXcx({data:{code:o[1].code,userInfo:t},method:"POST"}).then((function(t){if(e.btnLoading=!1,"success"===t.data.status)if(e.$u.test.isEmpty(t.data.data))e.$refs.uToast.show({message:"授权失败",type:"error",duration:"2300"});else{r.removeStorageSync("loadUser");var n=t.data.data;!e.$u.test.isEmpty(n.user)&&e.useStorage("user",n.user),!e.$u.test.isEmpty(n.session_info)&&e.useStorage("session",n.session_info),!e.$u.test.isEmpty(n.token)&&e.useStorage("token",n.token),e.$u.test.isEmpty(n.user)||e.$u.test.isEmpty(n.user.mobile)?r.redirectTo({url:"/pages/login/index"}):r.navigateBack()}else e.$refs.uToast.show({message:"授权失败",type:"error",duration:"2300"})})):(e.btnLoading=!1,e.$refs.uToast.show({message:"获取用户登录凭证失败",type:"error",duration:"2300"}));case 4:case"end":return n.stop()}}),n)})))()},useStorage:function(t,e){r.removeStorageSync(t),r.setStorageSync(t,e)}}}},52462:function(t,e,n){"use strict";var r=n(51372)["default"],o=n(81715)["createPage"];n(96910);a(n(923));var i=a(n(6257));function a(t){return t&&t.__esModule?t:{default:t}}r.__webpack_require_UNI_MP_PLUGIN__=n,o(i.default)}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(52462)}));t.O()}]);