<theme-wrap scoped-slots-compiler="augmented" vue-id="0efa40b0-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('0efa40b0-2')+','+('0efa40b0-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="强制取消预约管理" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><u-tabs vue-id="{{('0efa40b0-3')+','+('0efa40b0-1')}}" list="{{tabList}}" scrollable="{{false}}" lineColor="{{$root.m2['buttonLightBgColor']}}" lineWidth="60rpx" activeStyle="{{$root.a1}}" current="{{currentTab}}" data-event-opts="{{[['^change',[['clickTab']]]]}}" bind:change="__e" bind:__l="__l"></u-tabs><view class="container u-p-t-40 u-p-b-40"><view hidden="{{!(!currentType)}}"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="u-p-40 bg-fff border-16 u-m-b-40"><view class="w-100 u-flex u-col-start u-p-b-20"><view class="u-flex-5"><view class="u-flex u-row-center"><view class="u-p-t-10 u-p-b-10 u-p-r-24 u-p-l-24 border-8 font-bold u-font-24" style="display:inline-block;color:var(--button-light-bg-color);border:2rpx solid var(--button-light-bg-color);">{{i.m3}}</view></view></view><view class="u-flex-11 u-p-l-20"><view class="u-font-36 font-bold u-m-b-10">{{''+i.$orig.courseName+''}}</view><view class="u-font-30 u-m-b-10">{{''+i.$orig.bookingTime+''}}</view><view class="u-font-30 u-m-b-10 u-flex w-100 u-line-1">{{'教练：'+i.$orig.coachName+''}}</view><view class="u-font-30 u-m-b-10 u-flex w-100 u-line-1">{{'学员：'+i.$orig.memberName+''}}</view><view class="u-font-28 u-m-b-10 u-tips-color u-flex w-100 u-line-1">{{''+i.$orig.shopName+''}}</view></view></view><view class="u-p-t-20 u-border-top u-flex u-row-end"><block wx:if="{{i.$orig.superCancelStatus=='0'}}"><view data-event-opts="{{[['tap',[['agree',['$0'],[[['list','',index,'memberBookingId']]]]]]]}}" class="u-m-l-20 lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">同意</view></block><block wx:if="{{i.$orig.superCancelStatus=='0'}}"><view data-event-opts="{{[['tap',[['disagree',['$0'],[[['list','',index,'memberBookingId']]]]]]]}}" class="u-m-l-20 lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">拒绝</view></block></view></view></block></block><block wx:else><block wx:if="{{$root.g1}}"><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:300rpx;height:300rpx;" src="/static/images/empty/list.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无预约</view></view></block></block><u-loadmore vue-id="{{('0efa40b0-4')+','+('0efa40b0-1')}}" status="{{loadStatus}}" data-event-opts="{{[['^click',[['loadClick']]]]}}" bind:click="__e" bind:__l="__l"></u-loadmore></view><view hidden="{{!(loading)}}"><block wx:for="{{$root.l2}}" wx:for-item="j" wx:for-index="idx" wx:key="idx"><view clas="u-p-t-40 u-p-b-40 u-font-36 font-bold"><view class="placeholder-w-1 u-m-b-40 u-m-t-40" style="height:50rpx;"></view><view class="border-16 bg-fff u-m-b-40 u-p-40 u-flex"><block wx:for="{{j.l1}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="u-p-r-10 u-p-l-10 u-flex-1"><view class="placeholder" style="aspect-ratio:3/2;"></view></view></block></view></view></block></view></view></view></theme-wrap>