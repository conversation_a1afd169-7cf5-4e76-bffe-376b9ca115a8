<theme-wrap scoped-slots-compiler="augmented" vue-id="46165bae-1" class="data-v-48b24254" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast vue-id="{{('46165bae-2')+','+('46165bae-1')}}" data-ref="toast" class="data-v-48b24254 vue-ref" bind:__l="__l"></u-toast><view class="data-v-48b24254"><u-navbar vue-id="{{('46165bae-3')+','+('46165bae-1')}}" title="信息" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-48b24254" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-48b24254"><u-form vue-id="{{('46165bae-4')+','+('46165bae-1')}}" model="{{form}}" labelWidth="140" data-ref="uForm" class="data-v-48b24254 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-48b24254"><u-form-item vue-id="{{('46165bae-5')+','+('46165bae-4')}}" required="{{true}}" borderBottom="{{true}}" prop="memberId" label="会员" class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-48b24254" style="{{'color:'+(form.memberId?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.memberName||'请选择')+''}}</view><u-picker vue-id="{{('46165bae-6')+','+('46165bae-5')}}" show="{{memberId}}" columns="{{memberIdcolumns}}" keyName="nickName" value="{{form.memberId}}" data-event-opts="{{[['^cancel',[['e2']]],['^confirm',[['confirmmemberId']]],['^input',[['__set_model',['$0','memberId','$event',[]],['form']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-48b24254" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('46165bae-7')+','+('46165bae-4')}}" required="{{true}}" borderBottom="{{true}}" prop="ic" label="ic卡号(10进制) " class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('46165bae-8')+','+('46165bae-7')}}" inputAlign="right" type="number" border="none" placeholder="请输入ic卡号" value="{{form.ic}}" data-event-opts="{{[['^input',[['__set_model',['$0','ic','$event',[]],['form']]]]]}}" class="data-v-48b24254" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('46165bae-9')+','+('46165bae-4')}}" required="{{true}}" borderBottom="{{true}}" prop="type" label="黑白名单 " class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-48b24254" style="{{'color:'+(form.type?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.typename||'请选择状态')+''}}</view><u-picker vue-id="{{('46165bae-10')+','+('46165bae-9')}}" show="{{type}}" columns="{{columns}}" keyName="label" value="{{form.type}}" data-event-opts="{{[['^cancel',[['e4']]],['^confirm',[['confirmCoach']]],['^input',[['__set_model',['$0','type','$event',[]],['form']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-48b24254" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('46165bae-11')+','+('46165bae-4')}}" required="{{true}}" borderBottom="{{true}}" prop="startTime" label="推荐会月卡开始时间" class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-48b24254" style="{{'color:'+(form.startTime?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.startTime||'请选择开始时间')+''}}</view><u-datetime-picker vue-id="{{('46165bae-12')+','+('46165bae-11')}}" mode="datetime" show="{{startTime}}" format="yyyy-MM-dd" closeOnClickOverlay="{{true}}" value="{{form.startTimes}}" data-event-opts="{{[['^close',[['e6']]],['^cancel',[['e7']]],['^confirm',[['startTimecof']]],['^input',[['__set_model',['$0','startTimes','$event',[]],['form']]]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-48b24254" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('46165bae-13')+','+('46165bae-4')}}" required="{{true}}" borderBottom="{{true}}" prop="endTime" label="推荐会员卡到期时间" class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e8',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-48b24254" style="{{'color:'+(form.endTime?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.endTime||'请选择结束时间')+''}}</view><u-datetime-picker vue-id="{{('46165bae-14')+','+('46165bae-13')}}" mode="datetime" show="{{endTime}}" closeOnClickOverlay="{{true}}" value="{{form.endTimes}}" data-event-opts="{{[['^close',[['e9']]],['^cancel',[['e10']]],['^confirm',[['endTimecof']]],['^input',[['__set_model',['$0','endTimes','$event',[]],['form']]]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-48b24254" bind:__l="__l"></u-datetime-picker></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-48b24254"><u-form-item vue-id="{{('46165bae-15')+','+('46165bae-4')}}" required="{{true}}" prop="imgUrl" labelPosition="top" label="人脸图片" class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-48b24254"><view data-event-opts="{{[['tap',[['chooseBackground',['$event']]]]]}}" class="upload-img-box data-v-48b24254" catchtap="__e"><block wx:if="{{!form.imgUrl}}"><u-icon vue-id="{{('46165bae-16')+','+('46165bae-15')}}" name="camera" size="40" color="#ddd" class="data-v-48b24254" bind:__l="__l"></u-icon></block><block wx:else><view class="u-relative w-100 h-100 data-v-48b24254"><image class="w-100 data-v-48b24254" src="{{form.imgUrl}}" mode="widthFix" data-event-opts="{{[['tap',[['previewBackground',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delBackground',['$event']]]]]}}" hidden="{{!(form.imgUrl)}}" class="u-absolute u-p-10 data-v-48b24254" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('46165bae-17')+','+('46165bae-15')}}" name="close" color="#fff" size="13" class="data-v-48b24254" bind:__l="__l"></u-icon></view></view></block></view></view></u-form-item></view></u-form></view><block wx:if="{{type!='view'}}"><view class="bottom-blk bg-fff w-100 u-p-40 data-v-48b24254"><u-button vue-id="{{('46165bae-18')+','+('46165bae-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" loading="{{disabled}}" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['createGroupCourse']]]]}}" bind:click="__e" class="data-v-48b24254" bind:__l="__l" vue-slots="{{['default']}}">{{''+(type=='add'?'创建':'保存')+''}}</u-button></view></block></view></theme-wrap>