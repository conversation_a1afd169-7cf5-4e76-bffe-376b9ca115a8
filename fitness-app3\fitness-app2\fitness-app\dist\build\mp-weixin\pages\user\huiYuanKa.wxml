<theme-wrap scoped-slots-compiler="augmented" vue-id="1eec1376-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('1eec1376-2')+','+('1eec1376-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="我的会员卡" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toHuiYuanKa',['$0'],[[['huiYuanKaList','',index,'id']]]]]]]}}" class="w-100 border-16 u-m-b-20 u-p-r-40 u-p-l-40 bg-fff" bindtap="__e"><view class="huiyuan-item u-border-bottom w-100 u-flex u-row-start u-col-start bg-fff u-p-b-30 u-p-t-30"><view class="u-flex-6 u-relative"><view class="u-relative overflow-hidden" style="line-height:0;"><image class="w-100 border-16" style="height:280rpx;" src="{{item.f0}}" mode="widthFix"></image><view class="u-font-28 w-100 font-bold fc-fff u-absolute u-text-center expired-blk">{{'有效期：'+item.$orig.validDays+' 天'}}</view></view></view><view class="u-flex-8 u-flex-col u-p-l-30"><view class="u-font-36 font-bold u-m-b-20 u-line-1">{{''+item.$orig.cardName+''}}</view><view class="u-m-b-10 u-font-28 u-tips-color"><text>价格：<text class="u-m-l-10 u-m-r-10">{{item.$orig.cardPrice+''}}</text>元</text></view><view class="u-m-b-10 u-font-28 u-tips-color"><text>适用场馆：<text class="u-m-l-10 u-m-r-10">{{item.$orig.shopName+''}}</text></text></view></view></view><view class="u-p-t-30 u-p-b-30 u-flex u-col-center u-row-between w-00"><view class="u-font-30 font-bold">{{"购买时间："+item.$orig.createTime}}</view><view data-event-opts="{{[['tap',[['showHeTong',['$0'],[[['huiYuanKaList','',index,'contract']]]]]]]}}" class="u-p-t-10 u-p-b-10 u-p-l-16 u-p-r-16 border-8 lbc u-font-26 font-bold fc-fff" bindtap="__e">查看合同</view></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width"></image><view class="u-p-t-10 u-font-30 u-tips-color">暂无会员卡</view></view></block><u-popup vue-id="{{('1eec1376-3')+','+('1eec1376-1')}}" show="{{showContractModal}}" mode="center" safeAreaInsetBottom="{{false}}" round="16" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-p-40" style="height:75vh;width:85vw;overflow:scroll;"><u-parse class="content-text" vue-id="{{('1eec1376-4')+','+('1eec1376-3')}}" content="{{contract}}" selectable="{{true}}" tagStyle="{{parseTagStyle}}" bind:__l="__l"></u-parse></view></u-popup></view></view></theme-wrap>