(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/huiYuanKa/detail"],{1191:function(n,e,a){"use strict";var t=a(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var o=r(a(68466));function r(n){return n&&n.__esModule?n:{default:n}}e["default"]={data:function(){return{memberCardId:"",list:[],loading:!1,disabled:!1,checkboxValue1:!1,is_xuFei:!0,showContractModal:!1,shake:!1,contract:"\n      亲爱的朋友，欢迎来到场馆。在您符合入会条件，阅读并签署本协议后，就可以成为我们场馆的会员。在您享受运动指导和健身乐趣的同时，也请遵守您签署的入会协议。\n\n      本场馆采用会员制服务模式，只有取得会员资格方能享受本健身馆各项专业健身服务。会员办理会籍并首次预约课程后，视为会员已阅读、知晓本健身馆所有关于训练的规则与警示，并承诺遵守相关规定。\n      本协议所称“会员”，是指年满18周岁以上至60周岁以下（不含本数），身体健康并向健身馆缴纳了全部会费，同意并遵守本健身馆会员章程以及其他有关规定者。会员声明其具有完全民事行为能力，健康状况良好，无重大疾病，适合本健身馆各类活动或使用本健身馆体育设施之运动，均可申请成为本健身馆会员。年满16周岁以上至18周岁以下（不含本数）的青少年可申请办理会员，但需由其监护人作为法定代理人代为办理入会手续。\n\n      凡申请入会之申请人必须无重大疾病。如患有以下疾病及情况的，不适合参加本健身馆，包括但不限于：\n      1. 糖尿病、传染性疾病、严重肺气肿、哮喘、冠心病、红斑性狼疮、癫痫或精神分裂症等等；\n      2. 心脏病、血压疾病；\n      3. 患有重大疾病或手术后尚在恢复期；\n      4. 其他不适合于本健身馆及参与健身运动的各项运动项目。\n      会员申请入会时，必须对自身身体状况有充分了解，并对所有参与本健身馆的运动类别和项目有一定的认识，对本健身馆设施、设备也进行了充分了解，确认自身身体状况完全符合上述之规定，也明确知晓这是成为会员资格的前提条件。\n\n      会员卡取得方式\n      1. 在健身馆填写真实、有效、全面的个人资料，办理入会手续并缴纳会费后取得会员资格。\n      2. 通过登陆官方约课软件，注册个人账号，填写真实、有效、全面的个人资料并缴纳会费后取得会员资格。\n\n      会员卡管理\n      1. 健身馆采用实名制作为会员专属电子健身卡，用于线上预约课程以及入场训练凭证。\n      2. 电子健身卡仅供会员本人使用。本健身馆所有会籍（包括团购券）均有有效期限，有效期内如需退款，场馆将在扣除一定手续费后予以退回剩余价值，如剩余价值不足以支付手续费，场馆有权拒绝退款。\n      3. 任何未经健身馆同意并办理变更手续的会员私自向非会员转让会籍资格的行为均无效，健身馆将不予接待，并取消其会籍资格。\n      4. 严重违反健身馆规章制度者，健身馆有权取消其会籍资格。\n\n      会员权利\n      1. 依照所持卡的类别参加健身馆提供的相应健身课程和健身馆对该类持卡会员作出的全部服务承诺。\n      2. 优先参加健身馆举办的各项活动，优先享受健身馆制定的各项优惠。\n      3. 针对健身馆的服务，会员有提出批评、投诉及改进建议的权利。\n      4. 会籍卡不允许私下转让，若消费者因居住地变化、身体健康等客观原因需要转让会籍卡的，需与门店进行协商处理。\n\n      会员义务\n      1. 如实向健身馆提供个人信息资料，并在资料发生变动后及时通知健身馆。\n      2. 严禁携带十六岁以下儿童进入健身区域，对于擅自进入健身区域造成伤害或损失的，健身馆有权终止其资格，并保留追究其法律责任的权利。\n      3. 严禁在健身馆内吸烟、吐痰、乱丢垃圾。\n      4. 为了您的健身安全，请穿着运动服饰和运动鞋参加运动。请不要穿着不得体的服饰进行运动，否则工作人员有权劝离并取消当日运动权利。\n      5. 运动前严禁饮酒或饮用含酒精类饮料，因违反本条规定造成的人身伤害等意外情况，健身馆有权终止其资格，并保留追究其法律责任的权利。\n      6. 会员应自觉爱惜合理使用室内各项设施、设备，使用后须放归原位，如有损坏须照价赔偿。\n      7. 为了保证其他会员的权益，严禁心肺功能疾病、脊椎病、皮肤病及一切传染病患者进入健身馆训练，有以上疾病的患者隐瞒病情取得会员资格的，健身馆有权终止其资格，并保留追究其法律责任的权利。\n      8. 本健身馆原则上不接受60岁以上老人入会，能出具真实有效的健康证明者除外。如因隐瞒或错报个人健康信息取得会员资格的，健身馆有权终止其资格，并保留追究其法律责任的权利。\n      9. 禁止会员在健身馆内销售任何商品，不得参与任何营利性健身指导，违反本条规定的，健身馆有权取消其会员资格。\n      10. 严禁在健身馆内大声喧哗，打架斗殴，使用污秽语言以及一切违法活动。\n      11. 严禁携带宠物进入健身馆训练区域。\n\n      权利保留\n      1. 健身馆营业时间将在微信公众号上标明或在健身馆显著位置公示，会员须遵守该营业时间，非营业时间恕不接待。\n      2. 因国家政策或者法律法规的规定，健身馆有权合理修改营业时间并在店内公示，恕不另行通知会员，该公示即视为通知。\n      3. 因经营管理需要，健身馆有权调整、增减部分项目，该行为不视为违约，且在店内公示后即视为通知，无需另行报告给会员个人。\n      4. 因器械、设备（设施）检修、维护，健身馆有权在某一时间段对某一项目或某类项目采取停用或限用措施。\n      5. 其他出于会员安全及集体利益的需要，健身馆有权采取必要措施以恢复经营秩序。\n\n      安全提示\n      1. 会员在开始训练前，应先按照教练员指导，做必要的热身练习，以免受伤。\n      2. 过度锻炼及违规锻炼均有受伤的可能，所以会员在运动前应对自己的身体情况进行判断，并保持运动强度和时间的适当。\n      3. 健身馆非医疗机构，也无医疗人员，健身馆不能给会员提供任何有关医疗、医学方面的检查、注意和建议服务。\n      4. 对违反本承诺书约定从而造成物品丢失及人员伤亡的，本健身馆保留追究其法律责任的权利。\n      5. 健身馆内任何运动项目及器械设施均有严格的操作方法和程序，请务必在专业教练员的指导下进行操作。\n      6. 会员进行任何单项或器械的操作均应在知晓操作方法及注意事项后进行，同时充分注意自身的身体状况及承受能力。\n\n      储物柜管理\n      1. 储物柜分为公共储物柜和专属储物柜两种。\n      2. 公共储物柜用于会员临时存放衣物，须在健身馆当日营业时间结束前使用完毕，否则工作人员有权清理并对柜内物品不承担保管责任。\n      3. 专属储物柜的使用需另行办理会员专属储物柜租用手续；一经办理，该储物柜由该会员个人专属使用。\n      4. 专属储物柜租用到期后，使用人须在三日内办理续租手续。逾期未续租，健身馆有权予以清理，并不承担保管责任，同时将该专属储物柜纳入公共储物柜进行管理或分配给新的使用人。\n      5. 无论是公共储物柜还是专属储物柜，均需遵守下述管理制度：\n      6. 贵重物品严禁存放在储物柜内，如有遗失，健身馆不承担任何责任；\n      7. 储物柜内不得存放易燃、易爆及带有污染、腐蚀、放射性等任何具备危险品特性的物品，否则引起的一切后果，由存放人及物品权利人承担；\n      8. 储物柜内不得存放毒品、枪支等任何违反法律规定及国家政策、法律禁止或限制流通的物品；\n      9. 储物柜内不得存放动植物及其标本；\n\n      更衣及洗浴区域管理\n      1. 该区域只供更衣和洗浴使用，只有会员才能进入该区域。\n      2. 该区域绝对禁止任何形式的拍照、摄像以及录音。\n      3. 该区域禁止吸烟、禁止洗晒衣物。\n      4. 会员请自带毛巾、拖鞋及洗漱用品并保持室内清洁卫生。\n      5. 请节约用水，自觉控制淋浴时间。\n      6. 请妥善保管好私人物品，如有遗失，责任自负。\n      7. 请爱惜室内各项设施，如损坏照价赔偿。\n\n      免责条款\n      出现下列情形的，健身馆不予承担任何责任：\n      1. 遇不可抗力（如战争、自然灾害等）造成健身馆营业终止或会员会籍不能继续，致使会员遭受损失的。\n      2. 会员所受损害是因其自身故意或过失造成的。\n      3. 会员所受损害是健身馆工作人员以外的任何第三方的故意或过失行为导致的。\n      4. 非会员不听劝阻，擅自进入或强行进入会员区域造成损害的，由其自身或致害方承担责任。\n      5. 受害方严重违反健身馆制定的规章制度所造成损害的。\n      6. 未交由健身馆保管而由会员或会员随同人员个人保管的贵重物品发生毁损、灭失、遗失的。\n      7. 因会员资料或个人信息发生变动未及时通知健身馆，从而造成损失或会员权利受限的。\n      8. 未听从健身馆工作人员指导，擅自使用或违反操作规程使用器械、设备造成自身受伤的，由自身负责；造成他人受损或健身馆财产毁损的，其本人应承担全部赔偿责任，健身馆保留追究其法律责任的权利。\n      9. 因会员自身行为不当或会员之间的争议产生的人身和财产损失，健身馆不承担责任。\n\n      特别声明\n      1. 健身馆对本承诺书约定的内容有最终解释权，同时为更好地服务会员之需要，健身馆有权对相关内容进行修改，且修改后的条款自通知会员或在健身馆显著位置公示后，即对全体会员产生约束力。\n      2. 会员在进入健身房之后，健身馆将会拥有摄影，摄像的权利，会员由此产生的肖像权及其派生权利归健身馆所有。\n\n      会员承诺\n         本人保证所提供的入会资料及个人信息真实有效。本人身体健康且没有本承诺书约定的不适合进行运动的疾病。本人已阅读、理解并同意上述各条文。\n\n      ",tips:"",detail:{cardName:"会员卡（月卡）",cardImg:"",cardExpired:"30",cardPrice:"100",cardRawPrice:"199"},salerId:""}},onShareAppMessage:function(){return{title:"会员卡 ".concat(this.detail.cardName||""),path:"/pages/huiYuanKa/detail?id="+this.memberCardId+"&salerId="+this.salerId,imageUrl:"/static/images/share/share.jpg"}},watch:{shake:function(n){var e=this;n&&(t.vibrateLong(),setTimeout((function(){e.shake=!1}),500))}},onLoad:function(n){this.salerId=n.salerId||"",this.memberCardId=n.id,this.loadData()},onShow:function(){},methods:{pay:function(){var n=this;if(this.disabled=!0,!this.checkboxValue1)return this.shake=!0,void(this.disabled=!1);o.default.postCardPayMent({data:{memberCardId:this.memberCardId,salerId:this.salerId},method:"POST"}).then((function(e){200==e.code&&(console.log(e),t.requestPayment({provider:"weixin",nonceStr:e.data.prepay.nonceStr,package:e.data.prepay.packageVal,paySign:e.data.prepay.paySign,signType:e.data.prepay.signType,timeStamp:e.data.prepay.timeStamp+"",success:function(e){console.log(e),setTimeout((function(){n.$u.toast("支付成功！"),t.navigateBack()}),2e3)},fail:function(e){console.log(e),n.$u.toast("支付失败！"),n.disabled=!1}}))}))},loadData:function(){var n=this;o.default.getCardDetails({memberCardId:this.memberCardId}).then((function(e){200==e.code&&(n.detail=e.data,n.tips='<p><span style="color: #95a5a6;">有效期：</span>'+(e.data.validDays<=0?"无限期":e.data.validDays+"天1")+'<p><span style="color: #95a5a6;">门店：</span>'+e.data.shopName+'</p>\n              <p><span style="color: #95a5a6;">使用限制：</span>购买后立即开卡</p>\n              <p><span style="color: #95a5a6;">适用门店：</span>'+e.data.shopName+'</p>\n              <p><span style="color: #95a5a6;">适用课程：</span>团课，私教等</p>')}))},rawPrice:function(n){if(n>0){var e=Math.floor(n/.8);return e<n?n:e}return n}}}},29107:function(n,e,a){"use strict";a.r(e),a.d(e,{__esModule:function(){return i.__esModule},default:function(){return f}});var t,o={uNavbar:function(){return Promise.all([a.e("common/vendor"),a.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(a.bind(a,66372))},uIcon:function(){return Promise.all([a.e("common/vendor"),a.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(a.bind(a,78278))},uParse:function(){return Promise.all([a.e("common/vendor"),a.e("node-modules/uview-ui/components/u-parse/u-parse")]).then(a.bind(a,69833))},uButton:function(){return Promise.all([a.e("common/vendor"),a.e("node-modules/uview-ui/components/u-button/u-button")]).then(a.bind(a,65610))},uPopup:function(){return Promise.all([a.e("common/vendor"),a.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(a.bind(a,85432))}},r=function(){var n=this,e=n.$createElement,a=(n._self._c,n.$hasSSP("d531f4f0-1")),t=a?{color:n.$getSSP("d531f4f0-1","content")["buttonTextColor"]}:null,o=a?n.$getSSP("d531f4f0-1","content"):null,r=a?n._f("Img")(n.detail.cover):null,u=a?n.rawPrice(n.detail.cardPrice):null,i=a&&n.checkboxValue1?n.$getSSP("d531f4f0-1","content"):null,d=a?n.$getSSP("d531f4f0-1","content"):null,s=a?n.$getSSP("d531f4f0-1","content"):null;n._isMounted||(n.e0=function(e){n.checkboxValue1=!n.checkboxValue1},n.e1=function(e){e.stopPropagation(),n.showContractModal=!0},n.e2=function(e){n.showContractModal=!1}),n.$mp.data=Object.assign({},{$root:{m0:a,a0:t,m1:o,f0:r,m2:u,m3:i,m4:d,m5:s}})},u=[],i=a(1191),d=i["default"],s=a(38596),c=a.n(s),l=(c(),a(18535)),p=(0,l["default"])(d,r,u,!1,null,null,null,!1,o,t),f=p.exports},38596:function(){},41990:function(n,e,a){"use strict";var t=a(51372)["default"],o=a(81715)["createPage"];a(96910);u(a(923));var r=u(a(29107));function u(n){return n&&n.__esModule?n:{default:n}}t.__webpack_require_UNI_MP_PLUGIN__=a,o(r.default)}},function(n){var e=function(e){return n(n.s=e)};n.O(0,["common/vendor"],(function(){return e(41990)}));n.O()}]);