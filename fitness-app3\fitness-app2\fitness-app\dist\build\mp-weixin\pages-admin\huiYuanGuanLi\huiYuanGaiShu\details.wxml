<theme-wrap scoped-slots-compiler="augmented" vue-id="3673aed4-1" class="data-v-1bd9b302" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-1bd9b302" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('3673aed4-2')+','+('3673aed4-1')}}" title="企业缴费查询" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-1bd9b302" bind:__l="__l"></u-navbar><view class="searchView data-v-1bd9b302" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('3673aed4-3')+','+('3673aed4-1')}}" showAction="{{false}}" placeholder="请输入用户名称" animation="{{true}}" data-event-opts="{{[['^search',[['search']]]]}}" bind:search="__e" class="data-v-1bd9b302" bind:__l="__l"></u-search></view><scroll-view class="scrollView data-v-1bd9b302" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:for="{{showList}}" wx:for-item="list" wx:for-index="index" wx:key="*this"><view class="userList data-v-1bd9b302"><view class="user_avatar data-v-1bd9b302"><u-avatar vue-id="{{('3673aed4-4-'+index)+','+('3673aed4-1')}}" size="80rpx" src="{{src}}" shape="circle" class="data-v-1bd9b302" bind:__l="__l"></u-avatar></view><view data-event-opts="{{[['tap',[['addHuiYuan',['$0'],[[['showList','n',n]]]]]]]}}" class="user_textView data-v-1bd9b302" bindtap="__e"><text class="data-v-1bd9b302">晴天雨绵</text><view class="user_tag data-v-1bd9b302"><view class="tag data-v-1bd9b302">月卡（预售）</view><view class="tag data-v-1bd9b302">年会员</view></view></view><view class="user_rightView data-v-1bd9b302"><u-icon vue-id="{{('3673aed4-5-'+index)+','+('3673aed4-1')}}" name="arrow-right" class="data-v-1bd9b302" bind:__l="__l"></u-icon></view></view></block></scroll-view></view></theme-wrap>