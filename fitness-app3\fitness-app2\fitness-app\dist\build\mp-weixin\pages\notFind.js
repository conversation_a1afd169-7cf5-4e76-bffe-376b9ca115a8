(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/notFind"],{47717:function(){},69137:function(e,n,t){"use strict";var u=t(51372)["default"],o=t(81715)["createPage"];t(96910);a(t(923));var r=a(t(93020));function a(e){return e&&e.__esModule?e:{default:e}}u.__webpack_require_UNI_MP_PLUGIN__=t,o(r.default)},80662:function(e,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;n["default"]={data:function(){return{}},onLoad:function(){},methods:{}}},93020:function(e,n,t){"use strict";var u;t.r(n),t.d(n,{__esModule:function(){return c.__esModule},default:function(){return _}});var o,r=function(){var e=this,n=e.$createElement,u=(e._self._c,t(70195));e.$mp.data=Object.assign({},{$root:{m0:u}})},a=[],c=t(80662),f=c["default"],l=t(47717),i=t.n(l),s=(i(),t(18535)),d=(0,s["default"])(f,r,a,!1,null,null,null,!1,u,o),_=d.exports}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],(function(){return n(69137)}));e.O()}]);