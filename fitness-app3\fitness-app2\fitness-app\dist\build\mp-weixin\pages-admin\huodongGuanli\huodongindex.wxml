<theme-wrap scoped-slots-compiler="augmented" vue-id="73f69fd2-1" class="data-v-3f772f48" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-3f772f48"><u-navbar vue-id="{{('73f69fd2-2')+','+('73f69fd2-1')}}" title="活动" titleStyle="{{$root.a0}}" bgColor="#298ec3" leftIconColor="{{$root.m1['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-3f772f48" bind:__l="__l" vue-slots="{{['center']}}"><view class="joins data-v-3f772f48" slot="center" data-event-opts="{{[['tap',[['onShareAppMessage',['$event']]]]]}}" bindtap="__e"></view></u-navbar></view><view class="box data-v-3f772f48"><view class="bg data-v-3f772f48"><image style="width:100%;display:block;" mode="widthFix" src="{{data.poster}}" class="data-v-3f772f48"></image></view><view class="con data-v-3f772f48" style="{{'background-image:'+('url('+data.background+')')+';'}}"><view class="title data-v-3f772f48">{{''+(data.remark||'')+''}}</view><view class="huodongtime data-v-3f772f48">活动开始<view style="margin-left:30rpx;" class="data-v-3f772f48"><u-count-down vue-id="{{('73f69fd2-3')+','+('73f69fd2-1')}}" time="{{timeDifference}}" format="DD:HH:mm:ss" autoStart="{{true}}" millisecond="{{true}}" data-ref="countDown" data-event-opts="{{[['^change',[['onChange']]]]}}" bind:change="__e" class="data-v-3f772f48 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="time data-v-3f772f48"><view class="bordercl data-v-3f772f48">{{timeData.days}}</view><view style="color:red;font-weight:700;margin-left:10rpx;" class="data-v-3f772f48">天</view><view class="bordercl data-v-3f772f48">{{timeData.hours>=10?timeData.hours:'0'+timeData.hours}}</view><view style="color:red;font-weight:700;margin-left:10rpx;" class="data-v-3f772f48">:</view><view class="bordercl data-v-3f772f48">{{timeData.minutes}}</view><view style="color:red;font-weight:700;margin-left:10rpx;" class="data-v-3f772f48">:</view><view class="bordercl data-v-3f772f48">{{timeData.seconds}}</view></view></u-count-down></view></view><view class="userinfo data-v-3f772f48"><view class="infotext data-v-3f772f48"><view class="poi data-v-3f772f48">{{getActivityRecordInfos.readNum}}</view><view class="data-v-3f772f48">已浏览</view></view><view class="infotext poi data-v-3f772f48"><view class="poi data-v-3f772f48">{{getActivityRecordInfos.enterNum}}</view><view class="data-v-3f772f48">已报名</view></view><view class="infotext poi data-v-3f772f48"><view class="poi data-v-3f772f48">{{getActivityRecordInfos.shareNum}}</view><view class="data-v-3f772f48">已分享</view></view></view><view class="shopinfo data-v-3f772f48"><view class="shopinfotop data-v-3f772f48"><view class="shopimg data-v-3f772f48"><image style="width:100%;height:100%;" src="{{data.poster}}" class="data-v-3f772f48"></image></view><view class="shoptopright data-v-3f772f48"><view class="shoptit data-v-3f772f48">{{data.name||''}}</view><view class="shopspecialPrice data-v-3f772f48">拼团价<label style="font-size:50rpx;font-weight:800;" class="_span data-v-3f772f48">{{"￥"+data.specialPrice}}</label></view><view class="shopsourcePrice data-v-3f772f48"><view class="data-v-3f772f48">{{"原价"+data.sourcePrice}}</view></view></view></view><view data-event-opts="{{[['tap',[['pay',['$event']]]]]}}" class="shopbtn data-v-3f772f48" bindtap="__e">立即开团</view></view><view class="lunbo data-v-3f772f48"><view class="luobouser data-v-3f772f48"><label style="font-size:40rpx;color:red;font-weight:700;" class="_span data-v-3f772f48">{{getEnablePtListlen}}</label>人发出邀请，去参加他们的团</view><swiper class="swiper data-v-3f772f48" circular="{{true}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" vertical="{{true}}"><block wx:for="{{EnablePtList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-3f772f48"><block wx:for="{{item}}" wx:for-item="value" wx:for-index="__i0__" wx:key="*this"><view class="infouser data-v-3f772f48"><view class="lefts data-v-3f772f48"><view class="imgsa data-v-3f772f48"><image style="width:100%;height:100%;" src="{{value.avatar?value.avatar:'/static/images/default/head1.png'}}" class="data-v-3f772f48"></image></view>{{''+value.nickName+''}}</view><view class="rights data-v-3f772f48">{{"当前"+value.currentNum+"人，还差"+value.deficitNum+'人成团、'}}<view data-event-opts="{{[['tap',[['gow',['$event']]]]]}}" class="gow data-v-3f772f48" bindtap="__e">去拼团</view></view></view></block></swiper-item></block></swiper></view></view></view><block wx:for="{{imgs}}" wx:for-item="item" wx:for-index="__i1__"><image class="itemimg data-v-3f772f48" style="width:100%;display:block;" mode="widthFix" src="{{item}}"></image></block><view class="orderlist data-v-3f772f48"><view class="ordertext data-v-3f772f48">最新订单记录</view><swiper class="swiper data-v-3f772f48" circular="{{true}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" vertical="{{true}}"><block wx:for="{{getNewOrderLists}}" wx:for-item="item" wx:for-index="__i2__"><swiper-item class="data-v-3f772f48"><block wx:for="{{item}}" wx:for-item="value" wx:for-index="__i3__"><view class="infouser data-v-3f772f48"><view class="lefts data-v-3f772f48"><view class="imgsa data-v-3f772f48"><image style="width:100%;height:100%;" src="{{value.avatar?value.avatar:'/static/images/default/head1.png'}}" class="data-v-3f772f48"></image></view><view style="font-size:26rpx;" class="data-v-3f772f48"><view class="data-v-3f772f48">{{value.nickName}}</view><view class="data-v-3f772f48">2025-03-21</view></view></view><view class="rights data-v-3f772f48">{{"已经支付"+value.amount+'元'}}</view></view></block></swiper-item></block></swiper></view><view class="botbtn data-v-3f772f48"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="shouyi data-v-3f772f48" bindtap="__e"><view class="data-v-3f772f48"><u-icon vue-id="{{('73f69fd2-4')+','+('73f69fd2-1')}}" name="integral-fill" color="#abadb0f2" size="20" class="data-v-3f772f48" bind:__l="__l"></u-icon></view><view style="width:100%;text-align:center;" class="data-v-3f772f48">收益中心</view></view><view data-event-opts="{{[['tap',[['pay',['$event']]]]]}}" class="kaituan data-v-3f772f48" bindtap="__e"><view class="textli data-v-3f772f48">立即开团</view></view></view><u-popup vue-id="{{('73f69fd2-5')+','+('73f69fd2-1')}}" show="{{modelshow}}" mode="center" round="{{40}}" safeAreaInsetTop="{{false}}" data-event-opts="{{[['^close',[['cloces']]],['^open',[['open']]]]}}" bind:close="__e" bind:open="__e" class="data-v-3f772f48" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-3f772f48"><view class="texts data-v-3f772f48">邀好友 赢红包</view><view class="texts data-v-3f772f48" style="font-size:30rpx;">想要更多佣金，请联系商家</view><view class="modelcon data-v-3f772f48"><view style="display:flex;margin-bottom:40rpx;padding-top:40rpx;align-items:center;justify-content:center;" class="data-v-3f772f48"><image style="width:60rpx;height:60rpx;" src="/static/images/default/head1.png" class="data-v-3f772f48"></image><view class="data-v-3f772f48">{{wxUserInfo.memberName+"的推广记录"}}</view></view><view class="yiji data-v-3f772f48"><view class="data-v-3f772f48">商品</view><view class="data-v-3f772f48">购买人</view><view class="data-v-3f772f48">奖金类型</view><view class="data-v-3f772f48">佣金</view><view class="data-v-3f772f48">发放状态</view></view><view class="yijicon data-v-3f772f48"><block wx:for="{{getCommissionList}}" wx:for-item="i" wx:for-index="__i4__"><view class="yiji data-v-3f772f48"><view class="textset data-v-3f772f48">{{i.activityName}}</view><view class="textset data-v-3f772f48">{{i.nickName}}</view><view class="textset data-v-3f772f48">{{i.commissionType}}</view><view class="textset data-v-3f772f48">{{i.amount}}</view><view class="textset data-v-3f772f48">{{i.grantStatus==1?'已发放':'未发放'}}</view></view></block></view></view><view class="modelbtn data-v-3f772f48"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="guanzhu data-v-3f772f48" bindtap="__e">关注公众号</view><view class="yao data-v-3f772f48"><button class="sharebtns data-v-3f772f48" open-type="share">邀请好友赚佣金</button></view></view><view data-event-opts="{{[['tap',[['cloces',['$event']]]]]}}" class="cloce data-v-3f772f48" bindtap="__e"><u-icon vue-id="{{('73f69fd2-6')+','+('73f69fd2-5')}}" size="30" color="#e1e1e1" name="close-circle" class="data-v-3f772f48" bind:__l="__l"></u-icon></view></view></u-popup><u-popup vue-id="{{('73f69fd2-7')+','+('73f69fd2-1')}}" show="{{showQrcode}}" mode="center" safeAreaInsetBottom="{{false}}" round="{{20}}" data-event-opts="{{[['^close',[['onOfficialClose']]]]}}" bind:close="__e" class="data-v-3f772f48" bind:__l="__l" vue-slots="{{['default']}}"><official-qrcode vue-id="{{('73f69fd2-8')+','+('73f69fd2-7')}}" class="data-v-3f772f48" bind:__l="__l"></official-qrcode></u-popup></view></theme-wrap>