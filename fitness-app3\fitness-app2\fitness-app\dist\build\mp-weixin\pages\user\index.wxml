<theme-wrap vue-id="5c2bbbc2-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content"><u-no-network vue-id="{{('5c2bbbc2-2')+','+('5c2bbbc2-1')}}" bind:__l="__l"></u-no-network><view class="container u-p-t-40 u-p-b-40"><view class="u-flex w-100 u-p-r-40 u-p-l-40 u-col-center u-p-t-80 u-m-b-40"><view data-event-opts="{{[['tap',[['goEdit',['$event']]]]]}}" class="u-flex-1 u-flex-col u-row-center u-col-center" bindtap="__e"><view class="u-m-b-20" style="width:150rpx;height:150rpx;overflow:hidden;border-radius:50%;"><block wx:if="{{user.avatar_url}}"><image class="w-100" mode="widthFix" src="{{$root.f0}}" alt></image></block><block wx:else><block wx:if="{{user.sex=='0'}}"><image class="w-100" mode="widthFix" src="/static/images/default/head1.png" alt></image></block><block wx:else><block wx:if="{{user.sex=='1'}}"><image class="w-100" mode="widthFix" src="/static/images/default/head2.png" alt></image></block><block wx:else><image class="w-100" mode="widthFix" src="/static/images/default/head.png" alt></image></block></block></block></view><view class="u-flex u-row-center w-100"><block wx:if="{{isLogin}}"><view>{{user.nickName||'微信用户'}}</view></block><block wx:else><view><view data-event-opts="{{[['tap',[['goAuthorize',['$event']]]]]}}" class="border-32 font-bold u-font-26 u-p-t-12 u-p-b-12 u-p-r-26 u-p-l-26" style="border:1px solid;" catchtap="__e">用户登录</view></view></block></view></view></view><view class="card"><view class="w-100 u-flex u-row-between"><view class="u-font-34 font-bold">场馆服务</view></view><u-grid vue-id="{{('5c2bbbc2-3')+','+('5c2bbbc2-1')}}" border="{{false}}" data-event-opts="{{[['^click',[['clickGrid',['$event','1']]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="baseListItem" wx:for-index="baseListIndex" wx:key="baseListIndex"><u-grid-item vue-id="{{('5c2bbbc2-4-'+baseListIndex)+','+('5c2bbbc2-3')}}" bind:__l="__l" vue-slots="{{['default']}}"><u-icon vue-id="{{('5c2bbbc2-5-'+baseListIndex)+','+('5c2bbbc2-4-'+baseListIndex)}}" customStyle="{{baseListItem.a0}}" name="{{baseListItem.$orig.name}}" size="{{32}}" bind:__l="__l"></u-icon><text class="grid-text u-tips-color u-font-26">{{baseListItem.$orig.title}}</text></u-grid-item></block></u-grid></view><view class="card"><view class="w-100 u-flex u-row-between"><view class="u-font-34 font-bold">个人中心</view></view><u-grid vue-id="{{('5c2bbbc2-6')+','+('5c2bbbc2-1')}}" border="{{false}}" data-event-opts="{{[['^click',[['clickGrid',['$event','2']]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l1}}" wx:for-item="baseListItem" wx:for-index="baseListIndex" wx:key="baseListIndex"><u-grid-item vue-id="{{('5c2bbbc2-7-'+baseListIndex)+','+('5c2bbbc2-6')}}" bind:__l="__l" vue-slots="{{['default']}}"><u-icon vue-id="{{('5c2bbbc2-8-'+baseListIndex)+','+('5c2bbbc2-7-'+baseListIndex)}}" customStyle="{{baseListItem.a1}}" name="{{baseListItem.$orig.name}}" size="{{34}}" bind:__l="__l"></u-icon><text class="grid-text u-tips-color u-font-26">{{baseListItem.$orig.title}}</text></u-grid-item></block></u-grid></view></view><u-popup vue-id="{{('5c2bbbc2-9')+','+('5c2bbbc2-1')}}" safeAreaInsetBottom="{{false}}" closeOnClickOverlay="{{true}}" mode="center" bgColor="transparent" show="{{popupShow}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view><image style="width:64vw;" src="/static/icon/coding.png" mode="widthFix"></image><view class="u-font-40 font-bold fc-fff u-text-center">功能开发中...</view></view></u-popup><zw-tab-bar vue-id="{{('5c2bbbc2-10')+','+('5c2bbbc2-1')}}" selIdx="{{4}}" bigIdx="{{2}}" bind:__l="__l"></zw-tab-bar></view></theme-wrap>