(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-modal/u-modal"],{22862:function(n,e,o){"use strict";o.r(e),o.d(e,{__esModule:function(){return a.__esModule},default:function(){return f}});var u,i={uPopup:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(o.bind(o,85432))},uLine:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-line/u-line")]).then(o.bind(o,84916))},uLoadingIcon:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(o.bind(o,94597))}},t=function(){var n=this,e=n.$createElement,o=(n._self._c,{borderRadius:"6px",overflow:"hidden",marginTop:"-"+n.$u.addUnit(n.negativeTop)}),u=n.$u.addUnit(n.width);n.$mp.data=Object.assign({},{$root:{a0:o,g0:u}})},l=[],a=o(94073),c=a["default"],d=o(43132),s=o.n(d),r=(s(),o(18535)),m=(0,r["default"])(c,t,l,!1,null,"c1fe7cea",null,!1,i,u),f=m.exports},43132:function(){},94073:function(n,e,o){"use strict";var u=o(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=t(o(21207));function t(n){return n&&n.__esModule?n:{default:n}}e["default"]={name:"u-modal",mixins:[u.$u.mpMixin,u.$u.mixin,i.default],data:function(){return{loading:!1}},watch:{show:function(n){n&&this.loading&&(this.loading=!1)}},methods:{confirmHandler:function(){this.asyncClose&&(this.loading=!0),this.$emit("confirm")},cancelHandler:function(){this.$emit("cancel")},clickHandler:function(){this.closeOnClickOverlay&&this.$emit("close")}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-modal/u-modal-create-component"],{},function(n){n("81715")["createComponent"](n(22862))}]);