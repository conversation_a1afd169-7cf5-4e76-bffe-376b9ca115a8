<theme-wrap scoped-slots-compiler="augmented" vue-id="f4c5914e-1" class="data-v-7a732ddb" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-7a732ddb" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('f4c5914e-2')+','+('f4c5914e-1')}}" title="课程详情" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-7a732ddb" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-7a732ddb"><view class="formView data-v-7a732ddb"><view class="formTextarea border-8 data-v-7a732ddb"><view class="textareaTitle u-flex data-v-7a732ddb"><view class="u-flex-1 data-v-7a732ddb">课程图标:</view><u-icon vue-id="{{('f4c5914e-3')+','+('f4c5914e-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseLogo']]]]}}" bind:click="__e" class="data-v-7a732ddb" bind:__l="__l"></u-icon></view><view class="formLogo data-v-7a732ddb"><u--image vue-id="{{('f4c5914e-4')+','+('f4c5914e-1')}}" showLoading="{{true}}" src="{{$root.f0}}" width="240rpx" height="240rpx" radius="4" data-event-opts="{{[['^click',[['clickLogo']]]]}}" bind:click="__e" class="data-v-7a732ddb" bind:__l="__l"></u--image></view></view><view class="formList data-v-7a732ddb"><view class="data-v-7a732ddb">课程名称:</view><u--input bind:input="__e" vue-id="{{('f4c5914e-5')+','+('f4c5914e-1')}}" border="{{false}}" value="{{formList.courseName}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseName','$event',[]],['formList']]]]]}}" class="data-v-7a732ddb" bind:__l="__l"></u--input></view><view class="formList data-v-7a732ddb"><view class="data-v-7a732ddb">课程时长（分）:</view><u--input bind:input="__e" vue-id="{{('f4c5914e-6')+','+('f4c5914e-1')}}" border="{{false}}" value="{{formList.courseDuration}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseDuration','$event',[]],['formList']]]]]}}" class="data-v-7a732ddb" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="formList data-v-7a732ddb" bindtap="__e"><view class="data-v-7a732ddb">付费方式:</view><u--input bind:input="__e" vue-id="{{('f4c5914e-7')+','+('f4c5914e-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择付费方式" value="{{cardTypeName}}" data-event-opts="{{[['^input',[['__set_model',['','cardTypeName','$event',[]]]]]]}}" class="data-v-7a732ddb" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="formList data-v-7a732ddb" bindtap="__e"><view class="data-v-7a732ddb">课程类型:</view><u--input bind:input="__e" vue-id="{{('f4c5914e-8')+','+('f4c5914e-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择课程类型" value="{{courseTypeName}}" data-event-opts="{{[['^input',[['__set_model',['','courseTypeName','$event',[]]]]]]}}" class="data-v-7a732ddb" bind:__l="__l"></u--input></view></view></view><u-picker vue-id="{{('f4c5914e-9')+','+('f4c5914e-1')}}" show="{{showType}}" columns="{{actions}}" keyName="dictLabel" data-event-opts="{{[['^confirm',[['typeSelect']]],['^cancel',[['e3']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-7a732ddb" bind:__l="__l"></u-picker><u-picker vue-id="{{('f4c5914e-10')+','+('f4c5914e-1')}}" show="{{showLX}}" columns="{{actionsLX}}" keyName="courseTypeName" data-event-opts="{{[['^confirm',[['courseSelect']]],['^cancel',[['e4']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-7a732ddb" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-7a732ddb"><view data-event-opts="{{[['tap',[['deleteTrainer']]]]}}" class="moreBtn data-v-7a732ddb" style="{{'color:'+($root.m3['buttonLightBgColor'])+';'+('border-color:'+($root.m4['buttonLightBgColor'])+';')}}" bindtap="__e">删除课程</view><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="addHuiYuan data-v-7a732ddb" style="{{'background:'+($root.m5['buttonLightBgColor'])+';'+('color:'+($root.m6['buttonTextColor'])+';')+('border-color:'+($root.m7['buttonLightBgColor'])+';')}}" bindtap="__e">修改课程</view></view></view></theme-wrap>