{"version": 3, "file": "pages/changGuan/index.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACtBA,IAAAA,SAAA,GAAAC,mBAAA;AAGA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AAAA,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAEA;EACAC,UAAA;IACAC,MAAA,EAAAA;EACA;EACAC,KAAA;IACAC,aAAA;MACAC,IAAA,EAAAC,MAAA;MACAP,OAAA;IACA;IACAQ,eAAA;MACAF,IAAA,EAAAC,MAAA;MACAP,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,CAAA;MACAC,CAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,WAAA;IACA,KAAAC,QAAA;EACA;EACAC,MAAA,WAAAA,OAAA;EACAC,OAAA;IACA;IACAH,WAAA,WAAAA,YAAA;MACA;MACA,IAAAI,IAAA;MACAC,GAAA,CAAAC,WAAA;QACAjB,IAAA;QACAkB,OAAA,WAAAA,QAAAC,GAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;UACAJ,IAAA,CAAAP,CAAA,GAAAW,GAAA,CAAAG,SAAA;UACAP,IAAA,CAAAN,CAAA,GAAAU,GAAA,CAAAI,QAAA;UACA;UACAR,IAAA,CAAAS,WAAA;QACA;QACAC,IAAA,WAAAA,KAAAC,KAAA;UACAN,OAAA,CAAAC,GAAA,OAAAK,KAAA;QACA;QACAC,QAAA,WAAAA,SAAA;UACAX,GAAA,CAAAY,WAAA;QACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACAd,GAAA,CAAAe,cAAA;QACAb,OAAA,WAAAA,QAAAC,GAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;UACAC,OAAA,CAAAC,GAAA,SAAAF,GAAA,CAAAG,SAAA;UACAF,OAAA,CAAAC,GAAA,SAAAF,GAAA,CAAAI,QAAA;UACAH,OAAA,CAAAC,GAAA,WAAAF,GAAA,CAAAd,OAAA;UACAe,OAAA,CAAAC,GAAA,SAAAF,GAAA,CAAAa,IAAA;UACAF,KAAA,CAAAG,EAAA,GAAAd,GAAA,CAAAG,SAAA;UACAQ,KAAA,CAAArB,CAAA,GAAAU,GAAA,CAAAI,QAAA;UACAO,KAAA,CAAAzB,OAAA,GAAAc,GAAA,CAAAd,OAAA;UACAyB,KAAA,CAAAN,WAAA;QACA;MACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAU,MAAA;MACAC,YAAA,CAAAC,sBAAA;QACAjC,IAAA;UACAkC,QAAA;UACAC,SAAA;UACAhB,SAAA,OAAAd,CAAA;UAAA;UACAe,QAAA,OAAAd,CAAA;QACA;MACA,GAAA8B,IAAA,WAAApB,GAAA;QACAH,GAAA,CAAAY,WAAA;QACA,IAAAT,GAAA,CAAAqB,IAAA;UACA,IAAAC,IAAA,GAAAtB,GAAA,CAAAuB,IAAA;UACAR,MAAA,CAAA3B,SAAA,GAAAkC,IAAA;QACA;MACA,GAAAE,KAAA,WAAAC,GAAA;QACA5B,GAAA,CAAAY,WAAA;MACA;IACA;IACAhB,QAAA,WAAAA,SAAA;IACAiC,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAArD,mBAAA,GAAAsD,IAAA,UAAAC,SAAA;QAAA,OAAAvD,mBAAA,GAAAwD,IAAA,UAAAC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;YAAA;cAAAF,SAAA,CAAAE,IAAA;cAAA,OACAvC,GAAA,CAAAwC,KAAA,oBAAAT,MAAA,CAAAxC,SAAA,CAAAuC,KAAA;YAAA;cACAC,MAAA,CAAAU,SAAA,cAAAT,iBAAA,cAAArD,mBAAA,GAAAsD,IAAA,UAAAS,QAAA;gBAAA,OAAA/D,mBAAA,GAAAwD,IAAA,UAAAQ,SAAAC,QAAA;kBAAA,kBAAAA,QAAA,CAAAN,IAAA,GAAAM,QAAA,CAAAL,IAAA;oBAAA;sBAAAK,QAAA,CAAAL,IAAA;sBAAA,OACAvC,GAAA,CAAA6C,YAAA;oBAAA;oBAAA;sBAAA,OAAAD,QAAA,CAAAE,IAAA;kBAAA;gBAAA,GAAAJ,OAAA;cAAA,CACA;YAAA;YAAA;cAAA,OAAAL,SAAA,CAAAS,IAAA;UAAA;QAAA,GAAAZ,QAAA;MAAA;IACA;EACA;AACA;;;;;;;;;;AC7IA;;;;;;;;;;;;;;;ACAA7D,mBAAA;AAGA,IAAA0E,IAAA,GAAAxE,sBAAA,CAAAF,mBAAA;AACA,IAAA2E,MAAA,GAAAzE,sBAAA,CAAAF,mBAAA;AAA8C,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH9C;AACAyE,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLkG;AAClH;AACA,CAAyD;AACL;AACpD,CAAkE;;;AAGlE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBkf,CAAC,+DAAe,ydAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,i4BAAG,EAAC", "sources": ["webpack:///./src/pages/changGuan/index.vue?5915", "uni-app:///src/pages/changGuan/index.vue", "webpack:///./src/pages/changGuan/index.vue?b664", "uni-app:///src/main.js", "webpack:///./src/pages/changGuan/index.vue?e221", "webpack:///./src/pages/changGuan/index.vue?7569", "webpack:///./src/pages/changGuan/index.vue?95f6", "webpack:///./src/pages/changGuan/index.vue?737e"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notice-bar/u-notice-bar\" */ \"uview-ui/components/u-notice-bar/u-notice-bar.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"ceae42c8-1\")\n  var m1 = m0 ? _vm.$getSSP(\"ceae42c8-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"ceae42c8-1\", \"content\") : null\n  var l0 = m0\n    ? _vm.__map(_vm.changGuan, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var f0 = _vm._f(\"Img\")(item.logo)\n        var g0 = (item.distance / 1000).toFixed(2)\n        return {\n          $orig: $orig,\n          f0: f0,\n          g0: g0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <themeWrap>\n    <template #content=\"{ navBarColor, buttonTextColor }\">\n      <view class=\"chang-guan-list\">\n        <navBar :buttonBgColor=\"navBarColor\" :buttonTextColor=\"buttonTextColor\"></navBar>\n        <view class=\"u-p-40\">\n          <u-notice-bar :text=\"text1\"></u-notice-bar>\n          <view class=\"formList\" @click=\"choseAdvice\">\n            <view>位置:</view>\n            <u--input :border=\"false\" disabled v-model=\"address\"></u--input>\n          </view>\n          <view class=\"changguan-item-blk u-relative overflow-hidden u-m-b-40 border-24\"\n            v-for=\"(item, index) in changGuan\" :key=\"index\" @click=\"changeChangGuan(index)\">\n            <view class=\"w-100 placeholder overflow-hidden\" style=\"aspect-ratio: 2; line-height: 0\">\n              <image :src=\"item.logo | Img\" mode=\"widthFix\" class=\"w-100\" />\n            </view>\n            <view class=\"u-absolute u-p-30 w-100\" style=\"\n                bottom: 0;\n                left: 0;\n                background-color: rgba(255, 255, 255, 0.6);\n              \">\n              <view class=\"u-font-40 font-bold u-line u-m-b-20 w-100 u-text-center\">\n                {{ item.shopName }}\n              </view>\n              <view class=\"u-flex w-100 u-font-30\" style=\"color: #555\">\n                <view class=\"u-flex-5 u-p-r-20 u-line-1\">\n                  {{ item.address }}\n                </view>\n                <view class=\"u-flex-1\"> {{ (item.distance / 1000).toFixed(2) }}km </view>\n              </view>\n            </view>\n            <view class=\"u-absolute\" style=\"top: 20rpx; right: 20rpx\">\n              <!-- <u-icon name=\"heart\" size=\"28\" color=\"#fff\"></u-icon> -->\n            </view>\n          </view>\n        </view>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\n  import {\n    APPINFO\n  } from \"@/common/constant\";\n  import api from \"@/common/api\";\n  import navBar from \"./nav-bar\";\n  export default {\n    components: {\n      navBar,\n    },\n    props: {\n      buttonBgColor: {\n        type: String,\n        default: \"#fff\",\n      },\n      buttonTextColor: {\n        type: String,\n        default: \"#000\",\n      },\n    },\n    data() {\n      return {\n        text1: '如果没有健身房，尝试重新选择一下当前位置，查看其他地方的健身房哦~',\n        address: '当前位置',\n        loading: true,\n        changGuan: [],\n        x: '',\n        y: '',\n      };\n    },\n    mounted() {\n      // 查询场馆管理列表\n      this.positioning();\n      this.loadData();\n    },\n    onShow() {},\n    methods: {\n      // 获取定位\n      positioning() {\n        //点击允许后--就一直会进入成功授权的回调 就可以使用获取的方法了\n        let that = this;\n        uni.getLocation({\n          type: 'wgs84',\n          success: function(res) {\n            console.log(res);\n            that.x = res.longitude\n            that.y = res.latitude\n            // 获取场馆列表\n            that.getShopList();\n          },\n          fail(error) {\n            console.log('失败', error)\n          },\n          complete() {\n            uni.hideLoading()\n          }\n        })\n      },\n      // 重新选择位置\n      choseAdvice() {\n        uni.chooseLocation({\n          success: (res) => {\n            console.log(res);\n            console.log('经度：' + res.longitude);\n            console.log('纬度：' + res.latitude);\n            console.log('详细地址：' + res.address);\n            console.log('名称：' + res.name);\n            this.fx = res.longitude;\n            this.y = res.latitude;\n            this.address = res.address;\n            this.getShopList()\n          }\n        })\n      },\n      getShopList() {\n        api.getShopListForDistance({\n          data: {\n            distance: 999999999999,\n            companyId: 1,\n            longitude: this.x, // 经度\n            latitude: this.y, // 维度\n          },\n        }).then((res) => {\n          uni.hideLoading()\n          if (res.code == 200) {\n            let temp = res.rows;\n            this.changGuan = temp;\n          }\n        }).catch((err) => {\n          uni.hideLoading()\n        })\n      },\n      loadData() {},\n      async changeChangGuan(index) {\n        await uni.$emit('changeChangGuan', this.changGuan[index])\n        this.$nextTick(async () => {\n          await uni.navigateBack()\n        })\n      }\n    },\n  };\n</script>\n\n<style lang=\"scss\">\n  .formList {\n    background-color: #fafafa;\n    display: flex;\n    align-items: center;\n    flex-direction: row;\n    box-sizing: border-box;\n    padding: 10rpx 30rpx;\n    font-size: 30rpx;\n    color: #303133;\n    align-items: center;\n    border: 2rpx solid #d6d7d9;\n    margin-bottom: 20rpx;\n  }\n  .chang-guan-list {\n    background: #fff;\n    height: 100vh;\n    width: 100vw;\n    max-height: 100vh;\n    overflow: scroll;\n    position: fixed;\n    z-index: 10;\n    top: 0;\n    left: 0;\n  }\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/changGuan/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=06aad0ac&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/changGuan/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=06aad0ac&\""], "names": ["_constant", "require", "_api", "_interopRequireDefault", "e", "__esModule", "default", "_regeneratorRuntime", "components", "navBar", "props", "buttonBgColor", "type", "String", "buttonTextColor", "data", "text1", "address", "loading", "chang<PERSON>uan", "x", "y", "mounted", "positioning", "loadData", "onShow", "methods", "that", "uni", "getLocation", "success", "res", "console", "log", "longitude", "latitude", "getShopList", "fail", "error", "complete", "hideLoading", "choseAdvice", "_this", "chooseLocation", "name", "fx", "_this2", "api", "getShopListForDistance", "distance", "companyId", "then", "code", "temp", "rows", "catch", "err", "changeChangGuan", "index", "_this3", "_asyncToGenerator", "mark", "_callee2", "wrap", "_callee2$", "_context2", "prev", "next", "$emit", "$nextTick", "_callee", "_callee$", "_context", "navigateBack", "stop", "_vue", "_index", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}