<theme-wrap scoped-slots-compiler="augmented" vue-id="3618ab06-1" class="data-v-f2d58922" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-f2d58922" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('3618ab06-2')+','+('3618ab06-1')}}" title="员工权限设置" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-f2d58922" bind:__l="__l"></u-navbar><view class="u-m-t-30 u-m-l-30 u-font-32 bold data-v-f2d58922">权限人员</view><view class="container u-p-t-40 u-p-b-40 data-v-f2d58922"><view class="formView data-v-f2d58922"><view class="formList data-v-f2d58922"><view class="data-v-f2d58922">角色名称:</view><u--input bind:input="__e" vue-id="{{('3618ab06-3')+','+('3618ab06-1')}}" border="{{false}}" value="{{formList.roleName}}" data-event-opts="{{[['^input',[['__set_model',['$0','roleName','$event',[]],['formList']]]]]}}" class="data-v-f2d58922" bind:__l="__l"></u--input></view></view></view><view class="u-m-t-30 u-m-l-30 u-font-32 bold data-v-f2d58922">会员管理权限</view><view class="container u-p-t-40 u-p-b-40 data-v-f2d58922"><view class="contView u-p-r-30 u-p-l-30 w-100 border-16 bg-fff data-v-f2d58922"><u-checkbox-group vue-id="{{('3618ab06-4')+','+('3618ab06-1')}}" borderBottom="{{true}}" placement="column" iconPlacement="right" value="{{checkboxValue7}}" data-event-opts="{{[['^change',[['checkboxChange']]],['^input',[['__set_model',['','checkboxValue7','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-f2d58922" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{checkboxList7}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-checkbox vue-id="{{('3618ab06-5-'+index)+','+('3618ab06-4')}}" customStyle="{{({marginBottom:'16px'})}}" label="{{item.name}}" name="{{item.name}}" class="data-v-f2d58922" bind:__l="__l"></u-checkbox></block></u-checkbox-group></view></view><view class="bottonBtn u-flex data-v-f2d58922"><view data-event-opts="{{[['tap',[['deleteRole']]]]}}" class="moreBtn data-v-f2d58922" style="{{'color:'+($root.m3['buttonLightBgColor'])+';'+('border-color:'+($root.m4['buttonLightBgColor'])+';')}}" bindtap="__e">删除角色</view><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="addHuiYuan data-v-f2d58922" style="{{'background:'+($root.m5['buttonLightBgColor'])+';'+('color:'+($root.m6['buttonTextColor'])+';')+('border-color:'+($root.m7['buttonLightBgColor'])+';')}}" bindtap="__e">修改角色</view></view></view></theme-wrap>