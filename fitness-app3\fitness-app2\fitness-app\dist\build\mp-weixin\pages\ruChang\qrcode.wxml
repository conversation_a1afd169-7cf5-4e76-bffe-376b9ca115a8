<theme-wrap scoped-slots-compiler="augmented" vue-id="bc804e3c-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('bc804e3c-2')+','+('bc804e3c-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="入场二维码" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><block wx:if="{{!loading&&!memberId}}"><view class="u-p-t-40 u-p-r-40 u-p-l-40 ltc u-text-center w-100"><u-icon vue-id="{{('bc804e3c-3')+','+('bc804e3c-1')}}" name="error-circle" color="{{$root.m2['buttonLightBgColor']}}" labelPos="right" labelColor="{{$root.m3['buttonLightBgColor']}}" size="21" labelSize="16" space="10" label="非会员无法使用此功能，请购买会员卡。" bind:__l="__l"></u-icon></view></block><view class="content u-p-t-80 u-flex u-row-center u-col-center"><view class="u-flex-col u-m-t-80 u-row-center u-col-center"><view class="bg-fff u-p-20 border-24" style="box-sizing:border-box;"><view class="placeholder" style="line-height:0;width:70vw;height:70vw;"><image hidden="{{!(!loading)}}" style="width:100%;height:100%;" src="{{qrcodeUrl}}" mode="aspectFit"></image></view></view><navigator class="u-font-36 font-bold fc-fff lbc u-m-t-80 w-100 u-text-center u-p-t-20 u-p-b-20 border-24" url="{{'/pages/huiYuanKa/index?id='+shopId}}">购买会员卡</navigator></view></view></view></theme-wrap>