{"version": 3, "file": "pages/index/index.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AC6GA,IAAAA,SAAA,GAAAC,mBAAA;AAGA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AAAA,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,gBAAAH,CAAA,EAAAI,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAE,cAAA,CAAAF,CAAA,MAAAJ,CAAA,GAAAO,MAAA,CAAAC,cAAA,CAAAR,CAAA,EAAAI,CAAA,IAAAK,KAAA,EAAAJ,CAAA,EAAAK,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAAZ,CAAA,CAAAI,CAAA,IAAAC,CAAA,EAAAL,CAAA;AAAA,SAAAM,eAAAD,CAAA,QAAAQ,CAAA,GAAAC,YAAA,CAAAT,CAAA,gCAAAU,OAAA,CAAAF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAT,CAAA,EAAAD,CAAA,oBAAAW,OAAA,CAAAV,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAL,CAAA,GAAAK,CAAA,CAAAW,MAAA,CAAAC,WAAA,kBAAAjB,CAAA,QAAAa,CAAA,GAAAb,CAAA,CAAAkB,IAAA,CAAAb,CAAA,EAAAD,CAAA,gCAAAW,OAAA,CAAAF,CAAA,UAAAA,CAAA,YAAAM,SAAA,yEAAAf,CAAA,GAAAgB,MAAA,GAAAC,MAAA,EAAAhB,CAAA;AAAA,SAAAiB,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAIA;EACAC,IAAA;EACAC,UAAA;IACAC,OAAA,EAAAA,OAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,QAAA;MAAA;MACAC,MAAA;MACAC,CAAA;MACAC,CAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,OAAA;MACAC,MAAA;QACAC,aAAA;MACA;MACA;MACAC,MAAA;MACAC,YAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IAAA,IAAAC,KAAA;IACA;IACAC,YAAA,CACAC,SAAA;MACAjB,IAAA;MACAkB,OAAA;IACA,GACAC,IAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,CAAApB,IAAA,CAAAqB,MAAA;QACAN,KAAA,CAAAN,OAAA;MACA;QACAa,GAAA,CAAAC,cAAA,eAAAH,GAAA,CAAApB,IAAA;QACAe,KAAA,CAAAN,OAAA;MACA;IACA;IACA;IACAa,GAAA,CAAAE,WAAA;MACAC,IAAA;MACAC,KAAA;IACA;IACA,KAAAC,WAAA;EAEA;EACAC,YAAA,WAAAA,aAAAxD,CAAA;IACA,KAAAyC,YAAA,GAAAzC,CAAA,CAAAyD,SAAA;EACA;EACAC,MAAA,WAAAA,OAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAtC,mBAAA,GAAAuC,IAAA,UAAAC,QAAA;MAAA,IAAAC,WAAA;MAAA,OAAAzC,mBAAA,GAAA0C,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAC,OAAA,CAAAC,GAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAxB,YAAA;cACAhB,IAAA;gBACA2C,OAAA;gBACAC,QAAA;cACA;YACA;UAAA;YALAT,WAAA,GAAAG,QAAA,CAAAO,IAAA;YAMAd,MAAA,CAAA9B,WAAA,GAAAkC,WAAA,CAAAW,IAAA;YACA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAS,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EACA;EACAhB,OAAA,EAAA3C,eAAA,CAAAA,eAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAyE,qBAAA,WAAAA,sBAAA;MACA;MACA,SAAAC,KAAA,CAAAnD,SAAA;QACA,KAAAmD,KAAA,CAAAnD,SAAA,CAAAoD,SAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA/E,CAAA;MACA,KAAAwC,MAAA,GAAAxC,CAAA,CAAAgF,cAAA,IAAAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAAlF,CAAA;MACA,IAAAmF,QAAA,GAAAnF,CAAA,CAAAgF,cAAA,IAAAC,OAAA;MACA,IAAAE,QAAA,QAAA3C,MAAA,eAAAC,YAAA;QACA,KAAAoC,KAAA,CAAAnD,SAAA,CAAAoD,SAAA;MACA;IACA;IACAM,UAAA,WAAAA,WAAApF,CAAA;MACA;IAAA,CACA;IACAqF,IAAA,WAAAA,KAAAC,IAAA;MACApC,GAAA,CAAAqC,UAAA;QACAC,GAAA,gDAAAC,MAAA,CAAAH,IAAA,CAAAI,EAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,QAAAA,GAAA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MACA;IACA;IACAC,4BAAA,WAAAA,6BAAA;MACA,IAAAC,IAAA;MACAC,EAAA,CAAAC,SAAA;QACA1C,KAAA;QACA2C,OAAA;QACAC,WAAA;QACAC,UAAA;QACAC,OAAA,WAAAA,QAAApD,GAAA;UACA,IAAAA,GAAA,CAAAqD,OAAA;YACAnD,GAAA,CAAAoD,WAAA;cACAF,OAAA,WAAAA,QAAApD,GAAA;gBACAqB,OAAA,CAAAC,GAAA,WAAAtB,GAAA;gBACA,IAAAA,GAAA,CAAAuD,WAAA;kBACAlC,OAAA,CAAAC,GAAA;kBACAwB,IAAA,CAAAU,WAAA;kBACA;gBACA;kBACAnC,OAAA,CAAAC,GAAA;kBACAwB,IAAA,CAAAD,4BAAA;gBACA;cACA;cACAY,IAAA,WAAAA,KAAAC,GAAA;gBACArC,OAAA,CAAAC,GAAA,WAAAoC,GAAA;cACA;YACA;UACA;QACA;MACA;IAEA;IACA;IACAnD,WAAA,WAAAA,YAAA;MACA,IAAAuC,IAAA;MACA5C,GAAA,CAAAK,WAAA;QACAoD,IAAA;QACAC,OAAA;QACAR,OAAA,WAAAA,QAAApD,GAAA;UACAqB,OAAA,CAAAC,GAAA,CAAAtB,GAAA,CAAA6D,SAAA,EAAA7D,GAAA,CAAA8D,QAAA;UACAhB,IAAA,CAAAU,WAAA;QACA;QACAC,IAAA,WAAAA,KAAAC,GAAA;UACArC,OAAA,CAAAC,GAAA,CAAAoC,GAAA;UAEAZ,IAAA,CAAAD,4BAAA;QAEA;MACA;MACA;MACA3C,GAAA,CAAA6D,UAAA;QACAX,OAAA,WAAAA,QAAApD,GAAA;UACA;UACA,KAAAA,GAAA,CAAAuD,WAAA;YACA;YACArD,GAAA,CAAA8D,SAAA;cACAC,KAAA;cACAb,OAAA,WAAAA,QAAA;gBACAN,IAAA,CAAAU,WAAA;cACA;YACA;UACA;YACAV,IAAA,CAAAU,WAAA;UACA;QACA;QACAC,IAAA,WAAAA,KAAA;UACAvD,GAAA,CAAAgE,WAAA;QACA;MACA;IACA;IACA;IACAV,WAAA,WAAAA,YAAA;MACA;MACA,IAAAV,IAAA;MACA5C,GAAA,CAAAK,WAAA;QACAoD,IAAA;QACAP,OAAA,WAAAA,QAAApD,GAAA;UACA8C,IAAA,CAAA9D,CAAA,GAAAgB,GAAA,CAAA6D,SAAA;UACAf,IAAA,CAAA7D,CAAA,GAAAe,GAAA,CAAA8D,QAAA;UACA;UACAhB,IAAA,CAAAqB,WAAA;QACA;QACAV,IAAA,WAAAA,KAAAW,KAAA;UACA/C,OAAA,CAAAC,GAAA,OAAA8C,KAAA;QACA;QACAC,QAAA,WAAAA,SAAA;UACAnE,GAAA,CAAAgE,WAAA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAG,MAAA;MACApE,GAAA,CAAAC,cAAA,mBAAAnB,CAAA;MACAkB,GAAA,CAAAC,cAAA,kBAAAlB,CAAA;MACAW,YAAA,CACA2E,sBAAA;QACA3F,IAAA;UACA4F,QAAA;UACAC,SAAA;UACAZ,SAAA,OAAA7E,CAAA;UAAA;UACA8E,QAAA,OAAA7E,CAAA;QACA;MACA,GACAc,IAAA,WAAAC,GAAA;QACAqB,OAAA,CAAAC,GAAA;QACA;QACA;QACA;QACA,IAAAtB,GAAA,CAAA0E,IAAA;UACA,IAAA1E,GAAA,CAAA0B,IAAA,CAAAzB,MAAA;YACAqE,MAAA,CAAAxF,QAAA,GAAAkB,GAAA,CAAA0B,IAAA;YACA4C,MAAA,CAAAvF,MAAA,GAAAiB,GAAA,CAAA0B,IAAA,IAAA3C,MAAA;YACAmB,GAAA,CAAAC,cAAA,cAAAH,GAAA,CAAA0B,IAAA,IAAA3C,MAAA;YACAmB,GAAA,CAAAC,cAAA,gBAAAH,GAAA,CAAA0B,IAAA,IAAAiD,QAAA;YACAL,MAAA,CAAAM,aAAA;YACA,IAAA5E,GAAA,CAAA0B,IAAA,CAAAzB,MAAA;cACAoB,OAAA,CAAAC,GAAA;cACAgD,MAAA,CAAAzC,KAAA,CAAAnD,SAAA,CAAAoD,SAAA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;IACA8C,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACAjF,YAAA,CACAgF,aAAA;QACA7F,MAAA,OAAAA;MACA,GACAgB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA0E,IAAA;UACArD,OAAA,CAAAC,GAAA,UAAAtB,GAAA,CAAApB,IAAA;UACA,IAAAoB,GAAA,CAAApB,IAAA,CAAAkG,UAAA;YAAA,IAAAC,oBAAA;YACA,IAAAC,IAAA,IAAAD,oBAAA,GAAA/E,GAAA,CAAApB,IAAA,CAAAkG,UAAA,cAAAC,oBAAA,uBAAAA,oBAAA,CAAAE,KAAA;YACA,IAAA/F,WAAA;YACA,SAAArB,CAAA,MAAAA,CAAA,GAAAmH,IAAA,CAAA/E,MAAA,EAAApC,CAAA;cACAqB,WAAA,CAAAgG,IAAA;gBACAC,GAAA,EAAAN,MAAA,CAAAO,UAAA,GAAAJ,IAAA,CAAAnH,CAAA;cACA;YACA;YACAgH,MAAA,CAAA3F,WAAA,GAAAA,WAAA;YACAmC,OAAA,CAAAC,GAAA,CAAAuD,MAAA,CAAA3F,WAAA;UACA;UACA2F,MAAA,CAAAvF,MAAA,GAAAU,GAAA,CAAApB,IAAA;UACAsB,GAAA,CAAAC,cAAA,cAAA0E,MAAA,CAAAvF,MAAA,CAAAmF,SAAA;UACAI,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAAS,YAAA;QACA;MACA,GACAC,KAAA,WAAA7B,GAAA;IACA;IACA;IACA2B,WAAA,WAAAA,YAAA;MAAA,IAAAG,MAAA;MACA5F,YAAA,CACA6F,eAAA;QACA1G,MAAA,OAAAA;MACA,GACAgB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA0E,IAAA;UACA;UACA,IAAAgB,MAAA,GAAA1F,GAAA,CAAA0B,IAAA,CAAAiE,MAAA,WAAAC,SAAA,EAAAtD,IAAA,EAAAuD,MAAA,EAAAC,KAAA;YACA;YACA;YACA;YACA;YACA,IAAAC,KAAA,GAAAP,MAAA,CAAA7C,cAAA,CAAAL,IAAA,CAAA0D,QAAA;YACA,IAAAD,KAAA;YACA,IAAAH,SAAA,CAAAG,KAAA;cACAH,SAAA,CAAAG,KAAA,EAAAb,IAAA,CAAA5C,IAAA;YACA;cACAsD,SAAA,CAAAG,KAAA,KAAAzD,IAAA;YACA;YACA,OAAAsD,SAAA;UACA;UACAvE,OAAA,CAAAC,GAAA,CAAAoE,MAAA;UACA,IAAAV,IAAA;UACA,SAAAiB,GAAA,IAAAP,MAAA;YACAV,IAAA,CAAAE,IAAA;cACAgB,IAAA,EAAAR,MAAA,CAAAO,GAAA;cACAE,SAAA,EAAAF;YACA;UACA;UACA5E,OAAA,CAAAC,GAAA,CAAA0D,IAAA;UACAQ,MAAA,CAAArG,aAAA,GAAA6F,IAAA;QACA;MACA;IACA;IACA;IACAM,YAAA,WAAAA,aAAA;MAAA,IAAAc,MAAA;MACAxG,YAAA,CACA0F,YAAA;QACA1G,IAAA;UACAG,MAAA,OAAAA,MAAA;UACA0F,SAAA;QACA;MACA,GACA1E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA0E,IAAA;UACA0B,MAAA,CAAAhH,YAAA,GAAAY,GAAA,CAAA0B,IAAA;QACA;MACA;IACA;IACA;IACA2E,YAAA,WAAAA,aAAA;MACAnG,GAAA,CAAAqC,UAAA;QACAC,GAAA;MACA;IACA;IACA;IACA8D,SAAA,WAAAA,UAAA;MACApG,GAAA,CAAAqC,UAAA;QACAC,GAAA;MACA;IACA;IACA+D,WAAA,WAAAA,YAAA7D,EAAA;MACAxC,GAAA,CAAAqC,UAAA;QACAC,GAAA,gCAAAC,MAAA,CAAAC,EAAA;MACA;IACA;IACA;IACA8D,QAAA,WAAAA,SAAA5D,GAAA;MACA,IAAAA,GAAA;QACA;QACA,IAAA6D,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAA/D,GAAA;QACA,IAAA6D,GAAA,GAAA7D,GAAA;UACA,OAAAA,GAAA;QACA;UACA,OAAA6D,GAAA;QACA;MACA;QACA,OAAA7D,GAAA;MACA;IACA;IACAgE,eAAA,WAAAA,gBAAA;MACA1G,GAAA,CAAAE,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACA,IAAAwC,IAAA;MACA5C,GAAA,CAAA6D,UAAA;QACAX,OAAA,WAAAA,QAAApD,GAAA;UACAqB,OAAA,CAAAC,GAAA,CAAAtB,GAAA;UACA;UACA,KAAAA,GAAA,CAAAuD,WAAA;YACA;YACArD,GAAA,CAAA8D,SAAA;cACAC,KAAA;cACAb,OAAA,WAAAA,QAAA;gBACAlD,GAAA,CAAAgE,WAAA;gBACAhE,GAAA,CAAAqC,UAAA;kBACAC,GAAA;gBACA;cACA;YACA;UACA;YACAtC,GAAA,CAAAgE,WAAA;YACAhE,GAAA,CAAAqC,UAAA;cACAC,GAAA;YACA;UACA;QACA;QACAiB,IAAA,WAAAA,KAAA;UACAvD,GAAA,CAAAgE,WAAA;UACApB,IAAA,CAAA+D,EAAA,CAAAC,KAAA;QACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAzE,IAAA;MACApC,GAAA,CAAAqC,UAAA;QACAC,GAAA,+BAAAC,MAAA,CAAAH,IAAA,CAAA0E,QAAA,YAAAvE,MAAA,CAAAH,IAAA,CAAA2E,QAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACAnE,EAAA,CAAAoE,YAAA;QACArD,QAAA,OAAAxE,MAAA,CAAAwE,QAAA;QACAD,SAAA,OAAAvE,MAAA,CAAAuE,SAAA;QACAuD,KAAA;MACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACAtE,EAAA,CAAAuE,aAAA;QACAC,WAAA,OAAAjI,MAAA,CAAAkI;MACA;IACA;IACAC,YAAA,WAAAA,aAAAzK,CAAA;MACAqE,OAAA,CAAAC,GAAA,CAAAtE,CAAA;MACAkD,GAAA,CAAAuH,YAAA;QACAC,IAAA,OAAAxI,WAAA;QACAyI,OAAA,EAAA3K;MACA;IACA;IACA;IACA4K,eAAA,WAAAA,gBAAA7B,KAAA;MACA,KAAAlE,KAAA,CAAAnD,SAAA,CAAAmJ,SAAA;MACA,KAAAhG,KAAA,CAAAiG,MAAA,CAAAC,IAAA;QACAC,OAAA;QACArE,IAAA;QACAsE,QAAA;MACA;MACA,IAAA3F,IAAA,QAAAxD,QAAA,CAAAiH,KAAA;MACA7F,GAAA,CAAAC,cAAA,cAAAmC,IAAA,CAAAvD,MAAA;MACAmB,GAAA,CAAAC,cAAA,gBAAAmC,IAAA,CAAAqC,QAAA;MACA,KAAA5F,MAAA,GAAAuD,IAAA,CAAAvD,MAAA;MACA,KAAA6F,aAAA;IACA;EAAA,4BAAA7C,aACA/E,CAAA;IACA,KAAAwC,MAAA,GAAAxC,CAAA,CAAAgF,cAAA,IAAAC,OAAA;EACA,4BAAAC,YACAlF,CAAA;IACA,IAAAmF,QAAA,GAAAnF,CAAA,CAAAgF,cAAA,IAAAC,OAAA;IACA,IAAAE,QAAA,QAAA3C,MAAA,eAAAC,YAAA;MACA,KAAAoC,KAAA,CAAAnD,SAAA,CAAAoD,SAAA;IACA;EACA;AAEA;;;;;;;;;;ACnoBA;;;;;;;;;;;;;;;ACAAjF,mBAAA;AAGA,IAAAqL,IAAA,GAAAnL,sBAAA,CAAAF,mBAAA;AACA,IAAAsL,MAAA,GAAApL,sBAAA,CAAAF,mBAAA;AAA0C,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH1C;AACA+F,EAAE,CAACqF,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLkG;AAClH;AACA,CAAyD;AACL;AACpD,CAAkE;;;AAGlE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBkf,CAAC,+DAAe,ydAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,i4BAAG,EAAC", "sources": ["webpack:///./src/pages/index/index.vue?7537", "uni-app:///src/pages/index/index.vue", "webpack:///./src/pages/index/index.vue?14f8", "uni-app:///src/main.js", "webpack:///./src/pages/index/index.vue?84f1", "webpack:///./src/pages/index/index.vue?fb26", "webpack:///./src/pages/index/index.vue?be9e", "webpack:///./src/pages/index/index.vue?6623"], "sourcesContent": ["var components\ntry {\n  components = {\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-line/u-line\" */ \"uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n    uReadMore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-read-more/u-read-more\" */ \"uview-ui/components/u-read-more/u-read-more.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"8dd740cc-1\")\n  var m1 = m0 && !!_vm.isAdmin ? _vm.$getSSP(\"8dd740cc-1\", \"content\") : null\n  var m2 = m0 && !!_vm.isAdmin ? _vm.$getSSP(\"8dd740cc-1\", \"content\") : null\n  var f0 = m0 ? _vm._f(\"Img\")(_vm.detail.logo) : null\n  var g0 = m0 ? _vm.infoImgList.length : null\n  var g1 = m0 && g0 ? _vm.infoImgList.length : null\n  var f1 = m0 && g0 && !(g1 > 1) ? _vm._f(\"Img\")(_vm.infoImgList[0]) : null\n  var m3 = m0 ? _vm.$getSSP(\"8dd740cc-1\", \"content\") : null\n  var m4 = m0 ? _vm.$getSSP(\"8dd740cc-1\", \"content\") : null\n  var l0 = m0\n    ? _vm.__map(_vm.jiaoLianList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var f2 = item.coachAvatar ? _vm._f(\"Img\")(item.coachAvatar) : null\n        return {\n          $orig: $orig,\n          f2: f2,\n        }\n      })\n    : null\n  var l2 = m0\n    ? _vm.__map(_vm.huiYuanKaList, function (i, index) {\n        var $orig = _vm.__get_orig(i)\n        var l1 = _vm.__map(i.list, function (lit, idx) {\n          var $orig = _vm.__get_orig(lit)\n          var f3 = _vm._f(\"Img\")(lit.cover)\n          return {\n            $orig: $orig,\n            f3: f3,\n          }\n        })\n        return {\n          $orig: $orig,\n          l1: l1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        f0: f0,\n        g0: g0,\n        g1: g1,\n        f1: f1,\n        m3: m3,\n        m4: m4,\n        l0: l0,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n\t<themeWrap>\n\t\t<template #content=\"{ navBarColor, buttonTextColor, buttonLightBgColor }\">\n\t\t\t<!-- 主页navbar -->\n\t\t\t<u-toast ref=\"uToast\" />\n\t\t\t<!-- <u-navbar title=\"⬇️\" :titleStyle=\"{ color: buttonTextColor }\" :bgColor=\"navBarColor\" :placeholder=\"true\"\n\t\t\t\t:border=\"false\" :safeAreaInsetTop=\"true\">\n\t\t\t\t<view class=\"u-nav-slot\" slot=\"left\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-border font-bold border-32 u-p-r-20 u-p-l-20 u-p-t-4 u-p-b-4 u-flex u-row-center u-col-center\">\n\t\t\t\t\t\t<template v-if=\"!isAdmin\">\n\t\t\t\t\t\t\t<view @click=\"toQrcodePage\" class=\"u-flex u-p-t-4 u-p-b-4 u-row-center u-col-center w-100\">\n\t\t\t\t\t\t\t\t<image src=\"@/static/images/icons/qrcode-white.png\" mode=\"widthFix\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 26rpx; height: 26rpx\" lazy-load />\n\t\t\t\t\t\t\t\t<view class=\"fc-fff u-font-24 u-m-l-10\">签到</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n<template v-else>\n\t\t\t\t\t\t\t<u-icon name=\"list\" @click=\"gotoAdmin\" :color=\"buttonTextColor\"\n\t\t\t\t\t\t\t\t:labelColor=\"buttonTextColor\" size=\"21\" labelPos=\"right\"></u-icon>\n\t\t\t\t\t\t\t<u-line direction=\"column\" :hairline=\"false\" length=\"16\" margin=\"0 8px\"></u-line>\n\t\t\t\t\t\t\t<view @click=\"toQrcodePage\" class=\"u-flex u-p-t-4 u-p-b-4 u-row-center u-col-center w-100\">\n\t\t\t\t\t\t\t\t<image src=\"@/static/images/icons/qrcode-white.png\" mode=\"widthFix\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 30rpx; height: 30rpx\" lazy-load />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n</view>\n</view>\n</u-navbar> -->\n\t\t\t<view class=\"container\" style=\"padding-bottom: 130rpx;\" \n\t\t\t\t@touchstart=\"onTouchStart\" \n\t\t\t\t@touchmove=\"onTouchMove\" \n\t\t\t\t@touchend=\"onTouchEnd\"\n\t\t\t\t@touchcancel=\"onTouchEnd\">\n\t\t\t\t<view class=\"nav-bar\">\n\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"left u-border font-bold border-32 u-p-r-20 u-p-l-20 u-p-t-4 u-p-b-4 u-flex u-row-center u-col-center\">\n\t\t\t\t\t\t<template v-if=\"!isAdmin\">\n\t\t\t\t\t\t\t<view @click=\"toQrcodePage\" class=\"u-flex u-p-t-4 u-p-b-4 u-row-center u-col-center w-100\">\n\t\t\t\t\t\t\t\t<image src=\"@/static/images/icons/qrcode-white.png\" mode=\"widthFix\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 26rpx; height: 26rpx\" lazy-load />\n\t\t\t\t\t\t\t\t<view class=\"fc-fff u-font-24 u-m-l-10\">签到</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t<u-icon name=\"list\" @click=\"gotoAdmin\" :color=\"buttonTextColor\"\n\t\t\t\t\t\t\t\t:labelColor=\"buttonTextColor\" size=\"21\" labelPos=\"right\"></u-icon>\n\t\t\t\t\t\t\t<u-line direction=\"column\" :hairline=\"false\" length=\"16\" margin=\"0 8px\"></u-line>\n\t\t\t\t\t\t\t<view @click=\"toQrcodePage\" class=\"u-flex u-p-t-4 u-p-b-4 u-row-center u-col-center w-100\">\n\t\t\t\t\t\t\t\t<image src=\"@/static/images/icons/qrcode-white.png\" mode=\"widthFix\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 30rpx; height: 30rpx\" lazy-load />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"title\" @click=\"showChangGuanSelector\">\n\t\t\t\t\t\t<u-icon name=\"arrow-down\" size=\"24\" color=\"#fff\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right\"></view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 场馆信息 -->\n\t\t\t\t<block alt=\"场馆信息\">\n\t\t\t\t\t<!-- <image :src=\"infoImgList[0].src\" mode=\"widthFix\" class=\"w-100 bgImg\"></image> -->\n\t\t\t\t\t<view class=\"u-relative\">\n\t\t\t\t\t\t<!-- <view\n              style=\"height: 260px; filter: blur(3px);background-size: cover;background-position: center;background-repeat: no-repeat;\"\n              :style=\"{'background-image': 'url('+infoImgList[0].src+')'}\"></view> -->\n\t\t\t\t\t\t<image style=\"height: 560rpx; width:100%; filter: blur(3px);\" :src=\"infoImgList[0].src\" lazy-load></image>\n\t\t\t\t\t\t<view style=\"height: 560rpx; top: 0; left: 0;padding:40rpx;padding-top: 150rpx;\"\n\t\t\t\t\t\t\tclass=\"w-100 u-m-b-20 u-absolute\">\n\t\t\t\t\t\t\t<view class=\"u-flex-3 \">\n\t\t\t\t\t\t\t\t<view class=\"img-wrap flex-0 u-m-b-20\" style=\"\n                    aspect-ratio: 1;\n                    border-radius: 50%;\n                    overflow: hidden;\n                    line-height: 0;\n                    width: 180rpx;\n                    margin: auto;\n                  \">\n\t\t\t\t\t\t\t\t\t<image :src=\"detail.logo | Img\" mode=\"widthFix\" class=\"w-100 flex-0\"\n\t\t\t\t\t\t\t\t\t\tstyle=\"height: 160rpx; border-radius: 50%\" lazy-load />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"u-font-38 font-bold u-line-1 u-text-center\" style=\"color: white;\">\n\t\t\t\t\t\t\t\t{{ detail.shopName || \"\" }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"u-flex u-col-start w-100 u-p-t-30\">\n\t\t\t\t\t\t\t\t<view class=\"desc-blk\" v-if=\"detail.shopIntroduce\">\n\t\t\t\t\t\t\t\t\t<u-read-more showHeight=\"60\" toggle>\n\t\t\t\t\t\t\t\t\t\t<view class=\"u-font-28 u-tips-color\" style=\"color: white;\">\n\t\t\t\t\t\t\t\t\t\t\t{{ detail.shopIntroduce || \"暂无简介\" }}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</u-read-more>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<!-- 轮播图 -->\n\t\t\t\t<view class=\"w-100 bg-fff u-p-30\">\n\t\t\t\t\t<template v-if=\"infoImgList.length\">\n\t\t\t\t\t\t<u-swiper v-if=\"infoImgList.length > 1\" :list=\"infoImgList\" bgColor=\"transparent\"\n\t\t\t\t\t\t\tpreviousMargin=\"80\" nextMargin=\"80\" circular indicator @click=\"previewImage\"\n\t\t\t\t\t\t\t:autoplay=\"true\" radius=\"5\" keyName=\"src\"></u-swiper>\n\t\t\t\t\t\t<view class=\"w-100\" v-else>\n\t\t\t\t\t\t\t<image :src=\"infoImgList[0] | Img\" mode=\"widthFix\" class=\"w-100\" lazy-load></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</template>\n\t\t\t\t</view>\n\t\t\t\t<!--tu -->\n\t\t\t\t<view class=\"u-flex bg-fff u-p-30\">\n\t\t\t\t\t<view @click=\"openMap\" class=\"u-flex u-flex-1\">\n\t\t\t\t\t\t<u-icon name=\"map\" :color=\"buttonLightBgColor\" size=\"20\"></u-icon>\n\t\t\t\t\t\t<view class=\"u-line-1 u-p-l-10\">{{ detail.address }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"width: 1px; height: 16px; margin: auto; background-color: #666;margin-right: 20rpx;\">\n\t\t\t\t\t</view>\n\t\t\t\t\t<view @click=\"callPhone\" class=\"u-flex\" style=\"width: 40px;\">\n\t\t\t\t\t\t<u-icon name=\"phone\" :color=\"buttonLightBgColor\" size=\"32\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 教练信息 -->\n\t\t\t\t<block alt=\"教练团队\">\n\t\t\t\t\t<view class=\"u-m-t-20 u-p-t-40 u-p-b-40 u-flex w-100 u-row-between u-p-r-30 u-p-l-30 bg-fff\">\n\t\t\t\t\t\t<text class=\"font-bold u-font-36\">教练团队</text>\n\t\t\t\t\t\t<navigator :url=\"'/pages/yu-yue/index'\" open-type=\"switchTab\" hover-class=\"none\">\n\t\t\t\t\t\t\t<u-icon label=\"查看更多\" name=\"arrow-right\" labelPos=\"left\" size=\"15\" labelSize=\"15\"\n\t\t\t\t\t\t\t\tcolor=\"#999\"></u-icon>\n\t\t\t\t\t\t</navigator>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"w-100 border-16 u-m-b-20 u-flex u-p-r-30 u-p-l-30 bg-fff u-p-b-30\"\n\t\t\t\t\t\tstyle=\"overflow: scroll\">\n\t\t\t\t\t\t<view @click=\"toJiaoLianDetail(item)\" style=\" background-color: #f3f3f3;\"\n\t\t\t\t\t\t\tclass=\"jiaolian-item flex-0 u-flex-col u-row-center u-col-center border-16 u-m-r-30\"\n\t\t\t\t\t\t\tv-for=\"(item, index) in jiaoLianList\" :key=\"index\">\n\t\t\t\t\t\t\t<view style=\"\n                  width: 160rpx;\n                  height: 160rpx;\n                  border-radius: 50%;\n                  overflow: hidden;\n                \">\n\t\t\t\t\t\t\t\t<image v-if=\"item.coachAvatar\" :src=\"item.coachAvatar | Img\" class=\"w-100\"\n\t\t\t\t\t\t\t\t\tstyle=\"height: 160rpx\" lazy-load />\n\t\t\t\t\t\t\t\t<image v-else src=\"@/static/images/default/coach.jpg\" class=\"w-100\"\n\t\t\t\t\t\t\t\t\tstyle=\"height: 160rpx\" lazy-load />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"u-p-t-20 u-p-b-10 u-font-34 font-bold u-line-1 u-text-center\">\n\t\t\t\t\t\t\t\t{{ item.nickName }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"u-tips-color u-font-28 u-line-2 u-text-center\" style=\"height: 2.8rem;\">\n\t\t\t\t\t\t\t\t{{ item.aphorism || '暂无格言' }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block alt=\"会员卡\">\n\t\t\t\t\t<view v-for=\"(i, index) in huiYuanKaList\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"u-p-t-40 u-p-b-40 u-flex w-100 u-row-between u-p-r-30 u-p-l-30 bg-fff\">\n\t\t\t\t\t\t\t<text class=\"font-bold u-font-36\">{{ i.type_text }}</text>\n\t\t\t\t\t\t\t<navigator :url=\"'/pages/huiYuanKa/index?id=' + shopId\" open-type=\"navigate\"\n\t\t\t\t\t\t\t\thover-class=\"none\">\n\t\t\t\t\t\t\t\t<u-icon label=\"查看更多\" name=\"arrow-right\" labelPos=\"left\" size=\"15\" labelSize=\"15\"\n\t\t\t\t\t\t\t\t\tcolor=\"#999\"></u-icon>\n\t\t\t\t\t\t\t</navigator>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"border-16 u-m-b-20 u-flex u-p-r-30 u-p-l-30 bg-fff u-p-b-30\"\n\t\t\t\t\t\t\tstyle=\"overflow: scroll\">\n\t\t\t\t\t\t\t<view class=\"huiyuan-item u-flex u-row-start u-col-start bg-fff u-p-b-30 u-p-t-30\"\n\t\t\t\t\t\t\t\tv-for=\"(lit, idx) in i.list\" :key=\"idx\" @click=\"toHuiYuanKa(lit.memberCardId)\">\n\t\t\t\t\t\t\t\t<view class=\"u-flex-6 u-relative\">\n\t\t\t\t\t\t\t\t\t<view class=\"u-relative overflow-hidden\" style=\"line-height: 0\">\n\t\t\t\t\t\t\t\t\t\t<!-- ../../static/images/card/cardLogo.jpg -->\n\t\t\t\t\t\t\t\t\t\t<image :src=\"lit.cover | Img\" class=\"border-16\" mode=\"widthFix\"\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"width: 300rpx; height: 160rpx;\" lazy-load />\n\t\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\t\tclass=\"u-font-28 w-100 font-bold fc-fff u-absolute u-text-center expired-blk\">\n\t\t\t\t\t\t\t\t\t\t\t有效期：{{ lit.validDays <= 0 ? '无限期' : lit.validDays + '天' }} </view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"u-flex-8 u-flex-col u-p-l-30\" style=\"width: 330rpx;\">\n\t\t\t\t\t\t\t\t\t<view class=\"u-font-36 font-bold u-m-b-20 u-line-1\">\n\t\t\t\t\t\t\t\t\t\t{{ lit.cardName }}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"u-m-b-10 u-font-28\">\n\t\t\t\t\t\t\t\t\t\t<text style=\"text-decoration: line-through; color: #666\">\n\t\t\t\t\t\t\t\t\t\t\t原价：<text class=\"u-m-l-10 u-m-r-10\">{{ rawPrice(lit.cardPrice) }}\n\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t元\n\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"ltc\">\n\t\t\t\t\t\t\t\t\t\t\t价格：<text\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"u-font-40 u-m-l-10 u-m-r-10 font-bold\">{{lit.cardPrice}}</text>\n\t\t\t\t\t\t\t\t\t\t\t元\n\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<view class=\"huodonglist\">\n\t\t\t\t<view v-for=\"item in huodonglist\" style=\"margin-top: 30rpx;\" @click=\"jump(item)\">\n\t\t\t\t\t<image src=\"@/static/images/icons/gift.jpg\" mode=\"widthFix\" class=\"shake\" lazy-load />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<changGuan ref=\"changGuan\" :shopList=\"shopList\" @changeChangGuan=\"changeChangGuan\"></changGuan>\n\t\t\t<zwTabBar :selIdx=\"0\" :bigIdx=\"2\"></zwTabBar>\n\t\t\t<!-- <privacy></privacy> -->\n\t\t</template>\n\t</themeWrap>\n</template>\n\n<script>\nimport {\n\tAPPINFO\n} from \"@/common/constant\";\nimport api from \"@/common/api\";\nimport privacy from \"../../components/privacy-popup/privacy-popup.vue\";\nimport changGuan from \"@/components/changGuan\";\nimport zwTabBar from \"@/components/zw-tabbar/zw-tabbar.vue\";\nexport default {\n\tname: \"home\",\n\tcomponents: {\n\t\tprivacy,\n\t\tchangGuan,\n\t\tzwTabBar\n\t},\n\tdata() {\n\t\treturn {\n\t\t\thuodonglist: [],\n\t\t\tshopList: [], //场馆列表\n\t\t\tshopId: \"\",\n\t\t\tx: \"\",\n\t\t\ty: \"\",\n\t\t\tinfoImgList: [],\n\t\t\thuiYuanKaList: [],\n\t\t\tjiaoLianList: [],\n\t\t\tisAdmin: false,\n\t\t\tdetail: {\n\t\t\t\tshopIntroduce: \"\",\n\t\t\t},\n\t\t\t// 滑动页面相关\n\t\t\tstartY: 0,\n\t\t\tscrollHeight: 0\n\t\t};\n\t},\n\tonLoad() {\n\t\t// 根据用户获取路由权限\n\t\tapi\n\t\t\t.getRouter({\n\t\t\t\tdata: {},\n\t\t\t\tmethods: \"POST\",\n\t\t\t})\n\t\t\t.then((res) => {\n\t\t\t\tif (res.data.length == 0) {\n\t\t\t\t\tthis.isAdmin = false;\n\t\t\t\t} else {\n\t\t\t\t\tuni.setStorageSync(\"userRouter\", res.data);\n\t\t\t\t\tthis.isAdmin = true;\n\t\t\t\t}\n\t\t\t});\n\t\t// 获取用户位置\n\t\tuni.showLoading({\n\t\t\tmask: true,\n\t\t\ttitle: \"正在加载中……\",\n\t\t});\n\t\tthis.getLocation();\n\n\t},\n\tonPageScroll(e) {\n\t\tthis.scrollHeight = e.scrollTop;\n\t},\n\tasync onShow() {\n\t\tconsole.log(1231231)\n\t\tlet getActivity = await api['getActivity']({\n\t\t\tdata: {\n\t\t\t\tpageNum: 1,\n\t\t\t\tpageSize: 999,\n\t\t\t},\n\t\t})\n\t\tthis.huodonglist = getActivity.rows\n\t\t// this.getLocation();\n\t},\n\tmethods: {\n\t// \tonTouchMove(e) {\n    //     const currentY = e.changedTouches[0].clientY;\n    //     if (currentY - this.startY > 200 && this.scrollHeight == 0) {\n    //         this.$refs.changGuan.clickshow();\n    //     }\n    // },\n    // 在这里添加 showChangGuanSelector 方法\n    showChangGuanSelector() {\n        // 打开场馆选择弹窗\n        if (this.$refs.changGuan) {\n            this.$refs.changGuan.clickshow();\n        }\n    },\n\t    onTouchStart(e) {\n        this.startY = e.changedTouches[0].clientY;\n    },\n    onTouchMove(e) {\n        const currentY = e.changedTouches[0].clientY;\n        if (currentY - this.startY > 200 && this.scrollHeight == 0) {\n            this.$refs.changGuan.clickshow();\n        }\n    },\n    onTouchEnd(e) {\n        // 这里可以根据需要添加逻辑，目前为空即可防止报错\n    },\n\t\tjump(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages-admin/huodongGuanli/huodongindex?id=${item.id}`,\n\t\t\t});\n\t\t},\n\t\treturnCardName(val) {\n\t\t\tswitch (val) {\n\t\t\t\tcase '1':\n\t\t\t\t\treturn '会员卡'\n\t\t\t\t\tbreak;\n\t\t\t\tcase '2':\n\t\t\t\t\treturn '私教课'\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\treturn '其他类型'\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\tshowRefuseLocationPermission() {\n\t\t\tconst that = this;\n\t\t\twx.showModal({\n\t\t\t\ttitle: \"提示\",\n\t\t\t\tcontent: \"需要打开定位设置，否则影响体验\",\n\t\t\t\tconfirmText: \"前往设置\",\n\t\t\t\tshowCancel: false,\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tconsole.log(\"打开设置成功\", res);\n\t\t\t\t\t\t\t\tif (res.authSetting['scope.userLocation']) {\n\t\t\t\t\t\t\t\t\tconsole.log('成功授权userLocation')\n\t\t\t\t\t\t\t\t\tthat.positioning();\n\t\t\t\t\t\t\t\t\t// that.getUserLocation()\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tconsole.log('用户未授权userLocation')\n\t\t\t\t\t\t\t\t\tthat.showRefuseLocationPermission()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.log(\"打开设置失败\", err)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\n\t\t},\n\t\t// 判断是否有权限获取\n\t\tgetLocation() {\n\t\t\tlet that = this;\n\t\t\tuni.getLocation({\n\t\t\t\ttype: 'gcj02',\n\t\t\t\tgeocode: true,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log(res.longitude, res.latitude, 'resLocation', '')\n\t\t\t\t\tthat.positioning();\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.log(err)\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tthat.showRefuseLocationPermission()\n\t\t\t\t\t// #endif\n\t\t\t\t},\n\t\t\t});\n\t\t\treturn\n\t\t\tuni.getSetting({\n\t\t\t\tsuccess(res) {\n\t\t\t\t\t// 如果没有授权\n\t\t\t\t\tif (!res.authSetting[\"scope.userLocation\"]) {\n\t\t\t\t\t\t// 则拉起授权窗口\n\t\t\t\t\t\tuni.authorize({\n\t\t\t\t\t\t\tscope: \"scope.userLocation\",\n\t\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\t\tthat.positioning();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.positioning();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\t// 获取定位\n\t\tpositioning() {\n\t\t\t//点击允许后--就一直会进入成功授权的回调 就可以使用获取的方法了\n\t\t\tlet that = this;\n\t\t\tuni.getLocation({\n\t\t\t\ttype: \"wgs84\",\n\t\t\t\tsuccess: function (res) {\n\t\t\t\t\tthat.x = res.longitude;\n\t\t\t\t\tthat.y = res.latitude;\n\t\t\t\t\t// 获取场馆列表\n\t\t\t\t\tthat.getShopList();\n\t\t\t\t},\n\t\t\t\tfail(error) {\n\t\t\t\t\tconsole.log(\"失败\", error);\n\t\t\t\t},\n\t\t\t\tcomplete() {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\tgetShopList() {\n\t\t\tuni.setStorageSync(\"longitude\", this.x);\n\t\t\tuni.setStorageSync(\"latitude\", this.y);\n\t\t\tapi\n\t\t\t\t.getShopListForDistance({\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tdistance: 100000,\n\t\t\t\t\t\tcompanyId: 1,\n\t\t\t\t\t\tlongitude: this.x, // 经度\n\t\t\t\t\t\tlatitude: this.y, // 维度\n\t\t\t\t\t},\n\t\t\t\t})\n\t\t\t\t.then((res) => {\n\t\t\t\t\tconsole.log(\"获取场馆列表\")\n\t\t\t\t\t// if (this.shopId != \"\") {\n\t\t\t\t\t// \tthis.getShopDetail();\n\t\t\t\t\t// } else {\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tif (res.rows.length > 0) {\n\t\t\t\t\t\t\tthis.shopList = res.rows\n\t\t\t\t\t\t\tthis.shopId = res.rows[0].shopId;\n\t\t\t\t\t\t\tuni.setStorageSync(\"nowShopId\", res.rows[0].shopId);\n\t\t\t\t\t\t\tuni.setStorageSync(\"nowShopName\", res.rows[0].shopName);\n\t\t\t\t\t\t\tthis.getShopDetail();\n\t\t\t\t\t\t\tif (res.rows.length > 1) {\n\t\t\t\t\t\t\t\tconsole.log('获取到多个场馆,打开选择场馆')\n\t\t\t\t\t\t\t\tthis.$refs.changGuan.clickshow();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// }\n\t\t\t\t});\n\t\t},\n\t\t// 获取场馆详情\n\t\tgetShopDetail() {\n\t\t\tapi\n\t\t\t\t.getShopDetail({\n\t\t\t\t\tshopId: this.shopId,\n\t\t\t\t})\n\t\t\t\t.then((res) => {\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tconsole.log('场馆详情：', res.data)\n\t\t\t\t\t\tif (res.data.shopImages) {\n\t\t\t\t\t\t\tlet temp = res.data.shopImages?.split(\",\");\n\t\t\t\t\t\t\tlet infoImgList = [];\n\t\t\t\t\t\t\tfor (var i = 0; i < temp.length; i++) {\n\t\t\t\t\t\t\t\tinfoImgList.push({\n\t\t\t\t\t\t\t\t\tsrc: this.$serverUrl + temp[i],\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.infoImgList = infoImgList;\n\t\t\t\t\t\t\tconsole.log(this.infoImgList, \"轮播图\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.detail = res.data;\n\t\t\t\t\t\tuni.setStorageSync('companyId', this.detail.companyId)\n\t\t\t\t\t\tthis.getCardList();\n\t\t\t\t\t\tthis.getcoachList();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.catch((err) => { });\n\t\t},\n\t\t// 获取场馆会员卡列表\n\t\tgetCardList() {\n\t\t\tapi\n\t\t\t\t.getUserCardList({\n\t\t\t\t\tshopId: this.shopId,\n\t\t\t\t})\n\t\t\t\t.then((res) => {\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\t// this.huiYuanKaList = res.rows;\n\t\t\t\t\t\tlet result = res.rows.reduce((initArray, item, xindex, array) => {\n\t\t\t\t\t\t\t//initArray 初始值 或者是上一次调用返回的值\n\t\t\t\t\t\t\t//item 数组中当前被处理的元素\n\t\t\t\t\t\t\t//xindex 当前元素的下标\n\t\t\t\t\t\t\t//array 被reduce的数组  即上面代码中的数组a\n\t\t\t\t\t\t\tlet index = this.returnCardName(item.cardType);\n\t\t\t\t\t\t\tif (index == '1') { }\n\t\t\t\t\t\t\tif (initArray[index]) {\n\t\t\t\t\t\t\t\tinitArray[index].push(item)\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tinitArray[index] = [item]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn initArray\n\t\t\t\t\t\t}, []);\n\t\t\t\t\t\tconsole.log(result)\n\t\t\t\t\t\tlet temp = []\n\t\t\t\t\t\tfor (let key in result) {\n\t\t\t\t\t\t\ttemp.push({\n\t\t\t\t\t\t\t\tlist: result[key],\n\t\t\t\t\t\t\t\ttype_text: key\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(temp);\n\t\t\t\t\t\tthis.huiYuanKaList = temp\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t},\n\t\t// 获取场馆教练列表\n\t\tgetcoachList() {\n\t\t\tapi\n\t\t\t\t.getcoachList({\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tshopId: this.shopId,\n\t\t\t\t\t\tcompanyId: 1,\n\t\t\t\t\t},\n\t\t\t\t})\n\t\t\t\t.then((res) => {\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tthis.jiaoLianList = res.rows;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t},\n\t\t// 签到\n\t\ttoQrcodePage() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: \"/pages/ruChang/qrcode\",\n\t\t\t});\n\t\t},\n\t\t// 跳转管理端\n\t\tgotoAdmin() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: \"/pages-admin/admin/index/index\",\n\t\t\t});\n\t\t},\n\t\ttoHuiYuanKa(id) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/huiYuanKa/detail?id=${id}`,\n\t\t\t});\n\t\t},\n\t\t// 计算原价\n\t\trawPrice(val) {\n\t\t\tif (val > 0) {\n\t\t\t\t// 打八折\n\t\t\t\tlet num = Math.floor(val / 0.8);\n\t\t\t\tif (num < val) {\n\t\t\t\t\treturn val;\n\t\t\t\t} else {\n\t\t\t\t\treturn num;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treturn val;\n\t\t\t}\n\t\t},\n\t\ttoChangGuanPage() {\n\t\t\tuni.showLoading({\n\t\t\t\tmask: true,\n\t\t\t\ttitle: \"加载中……\",\n\t\t\t});\n\t\t\tlet that = this;\n\t\t\tuni.getSetting({\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t// 如果没有授权\n\t\t\t\t\tif (!res.authSetting[\"scope.userLocation\"]) {\n\t\t\t\t\t\t// 则拉起授权窗口\n\t\t\t\t\t\tuni.authorize({\n\t\t\t\t\t\t\tscope: \"scope.userLocation\",\n\t\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: \"/pages/changGuan/index\",\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: \"/pages/changGuan/index\",\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthat.$u.toast(\"请先给予定位权限！\");\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\ttoJiaoLianDetail(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/jiaoLian/detail?id=${item.memberId}&name=${item.nickName}`,\n\t\t\t});\n\t\t},\n\t\topenMap() {\n\t\t\twx.openLocation({\n\t\t\t\tlatitude: this.detail.latitude,\n\t\t\t\tlongitude: this.detail.longitude,\n\t\t\t\tscale: 18,\n\t\t\t});\n\t\t},\n\t\tcallPhone() {\n\t\t\twx.makePhoneCall({\n\t\t\t\tphoneNumber: this.detail.phone,\n\t\t\t});\n\t\t},\n\t\tpreviewImage(e) {\n\t\t\tconsole.log(e);\n\t\t\tuni.previewImage({\n\t\t\t\turls: this.infoImgList,\n\t\t\t\tcurrent: e,\n\t\t\t});\n\t\t},\n\t\t// 切换场馆\n\t\tchangeChangGuan(index) {\n\t\t\tthis.$refs.changGuan.clickhide();\n\t\t\tthis.$refs.uToast.show({\n\t\t\t\tmessage: \"切换场馆成功\",\n\t\t\t\ttype: \"success\",\n\t\t\t\tduration: 1000,\n\t\t\t});\n\t\t\tconst item = this.shopList[index]\n\t\t\tuni.setStorageSync(\"nowShopId\", item.shopId);\n\t\t\tuni.setStorageSync(\"nowShopName\", item.shopName);\n\t\t\tthis.shopId = item.shopId;\n\t\t\tthis.getShopDetail();\n\t\t},\n\t\tonTouchStart(e) {\n\t\t\tthis.startY = e.changedTouches[0].clientY;\n\t\t},\n\t\tonTouchMove(e) {\n\t\t\tconst currentY = e.changedTouches[0].clientY;\n\t\t\tif (currentY - this.startY > 200 && this.scrollHeight == 0) {\n\t\t\t\tthis.$refs.changGuan.clickshow();\n\t\t\t}\n\t\t},\n\t},\n};\n</script>\n\n<style lang=\"scss\">\n.shake {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tanimation: shakeAnimation 3s ease-in-out infinite;\n}\n\n/* 定义左右摇摆的动画 */\n@keyframes shakeAnimation {\n\t0% {\n\t\ttransform: translateX(0);\n\t}\n\n\t25% {\n\t\ttransform: translateX(-5px);\n\t\t/* 向左移动10px */\n\t}\n\n\t50% {\n\t\ttransform: translateX(5px);\n\t\t/* 向右移动10px */\n\t}\n\n\t75% {\n\t\ttransform: translateX(-5px);\n\t\t/* 向左移动10px */\n\t}\n\n\t100% {\n\t\ttransform: translateX(0);\n\t\t/* 回到原位 */\n\t}\n}\n\n.huodonglist {\n\twidth: 80rpx;\n\tposition: fixed;\n\tright: 30rpx;\n\tbottom: 20vh;\n\tborder-radius: 50%;\n}\n\n.container {\n\tpadding-left: 0;\n\tpadding-right: 0;\n\n}\n\n.jiaolian-item {\n\twidth: 240rpx;\n\tflex-shrink: 0;\n\tflex-grow: 0;\n\tpadding: 30rpx 10rpx 0 10rpx;\n}\n\n.huiyuan-item {\n\tborder-top: 1px solid #e1e1e1;\n\n\t&:not(:last-of-type) {\n\t\tmargin-right: 20rpx;\n\t}\n}\n\n.expired-blk {\n\tbottom: 0;\n\tbackground: linear-gradient(to bottom, transparent -10%, rgba(0, 0, 0, 0.8));\n\theight: 70rpx;\n\tborder-radius: 0 0 16rpx 16rpx;\n\tline-height: 70rpx;\n}\n\n.nav-bar {\n\tposition: absolute;\n\ttop: 100rpx;\n\twidth: 100%;\n\tpadding: 0 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tz-index: 10;\n\n\t.left {\n\t\tbackground: rgba(255, 255, 255, 0.3);\n\t}\n\n\t.title {\n\t\tanimation: moveArrow 2s ease-in-out infinite;\n\t}\n\n\t.right {\n\t\twidth: 150rpx;\n\t}\n}\n\n/* 定义动画关键帧 */\n@keyframes moveArrow {\n\n\t0%,\n\t100% {\n\t\ttransform: translateY(-6rpx);\n\t}\n\n\t50% {\n\t\ttransform: translateY(6rpx);\n\t}\n}\n</style>", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\""], "names": ["_constant", "require", "_api", "_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "i", "_toPrimitive", "_typeof", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_regeneratorRuntime", "name", "components", "privacy", "chang<PERSON>uan", "zwTabBar", "data", "huodong<PERSON>", "shopList", "shopId", "x", "y", "infoImgList", "huiYuanKaList", "jiaoLianList", "isAdmin", "detail", "shopIntroduce", "startY", "scrollHeight", "onLoad", "_this", "api", "getRouter", "methods", "then", "res", "length", "uni", "setStorageSync", "showLoading", "mask", "title", "getLocation", "onPageScroll", "scrollTop", "onShow", "_this2", "_asyncToGenerator", "mark", "_callee", "getActivity", "wrap", "_callee$", "_context", "prev", "next", "console", "log", "pageNum", "pageSize", "sent", "rows", "stop", "showChangGuanSelector", "$refs", "clickshow", "onTouchStart", "changedTouches", "clientY", "onTouchMove", "currentY", "onTouchEnd", "jump", "item", "navigateTo", "url", "concat", "id", "returnCardName", "val", "showRefuseLocationPermission", "that", "wx", "showModal", "content", "confirmText", "showCancel", "success", "confirm", "openSetting", "authSetting", "positioning", "fail", "err", "type", "geocode", "longitude", "latitude", "getSetting", "authorize", "scope", "hideLoading", "getShopList", "error", "complete", "_this3", "getShopListForDistance", "distance", "companyId", "code", "shopName", "getShopDetail", "_this4", "shopImages", "_res$data$shopImages", "temp", "split", "push", "src", "$serverUrl", "getCardList", "getcoachList", "catch", "_this5", "getUserCardList", "result", "reduce", "initArray", "xindex", "array", "index", "cardType", "key", "list", "type_text", "_this6", "toQrcodePage", "goto<PERSON><PERSON><PERSON>", "toHuiYuanKa", "rawPrice", "num", "Math", "floor", "toChangGuanPage", "$u", "toast", "toJiaoLianDetail", "memberId", "nick<PERSON><PERSON>", "openMap", "openLocation", "scale", "callPhone", "makePhoneCall", "phoneNumber", "phone", "previewImage", "urls", "current", "changeChangGuan", "clickhide", "uToast", "show", "message", "duration", "_vue", "_index", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}