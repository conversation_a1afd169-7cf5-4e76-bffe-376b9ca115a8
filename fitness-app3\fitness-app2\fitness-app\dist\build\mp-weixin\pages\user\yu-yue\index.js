(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/user/yu-yue/index"],{43667:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return r.__esModule},default:function(){return h}});var o,a={uNavbar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(n.bind(n,66372))},uSubsection:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-subsection/u-subsection")]).then(n.bind(n,7988))},uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(n,43399))},uLoadmore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(n.bind(n,67727))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("6b1ae7e0-1")),o=n?{color:t.$getSSP("6b1ae7e0-1","content")["buttonTextColor"]}:null,a=n?t.$getSSP("6b1ae7e0-1","content"):null,i=n?t.$getSSP("6b1ae7e0-1","content"):null,u=n?t.$getSSP("6b1ae7e0-1","content"):null,r=n?t.$getSSP("6b1ae7e0-1","content"):null,s=n&&!t.currentType?{"font-weight":"bold"}:null,c=n&&!t.currentType?t.$getSSP("6b1ae7e0-1","content"):null,l=n?t.list.length:null,d=n&&l?t.__map(t.list,(function(e,n){var o=t.__get_orig(e),a=t.returnStatusName(e.status);return{$orig:o,m6:a}})):null,g=n&&!l?!t.list.length&&!t.loading:null,h=n?t.list.length:null,f=n&&h?t.__map(t.list,(function(e,n){var o=t.__get_orig(e),a=Number(e.attendance||0),i=Number(e.remainder||0);return{$orig:o,m7:a,m8:i}})):null,m=n&&!h?!t.list.length&&!t.loading:null,b=n?t.__map(Array(3),(function(e,n){var o=t.__get_orig(e),a=Array(3);return{$orig:o,l2:a}})):null;t.$mp.data=Object.assign({},{$root:{m0:n,a0:o,m1:a,m2:i,m3:u,m4:r,a1:s,m5:c,g0:l,l0:d,g1:g,g2:h,l1:f,g3:m,l3:b}})},u=[],r=n(88741),s=r["default"],c=n(54212),l=n.n(c),d=(l(),n(18535)),g=(0,d["default"])(s,i,u,!1,null,null,null,!1,a,o),h=g.exports},54212:function(){},71536:function(t,e,n){"use strict";var o=n(51372)["default"],a=n(81715)["createPage"];n(96910);u(n(923));var i=u(n(43667));function u(t){return t&&t.__esModule?t:{default:t}}o.__webpack_require_UNI_MP_PLUGIN__=n,a(i.default)},88741:function(t,e,n){"use strict";var o=n(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=i(n(68466));function i(t){return t&&t.__esModule?t:{default:t}}function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function r(t,e,n){return(e=s(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function s(t){var e=c(t,"string");return"symbol"==u(e)?e:e+""}function c(t,e){if("object"!=u(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=u(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["default"]={data:function(){return r(r(r(r(r(r(r(r(r(r({currentType:0,list:[],typeList:["私教预约","团课预约"],currentTab:0,isJiaoLian:0,tabList:[{name:"全部",id:""},{name:"预约中",id:0},{name:"已预约",id:1},{name:"已签到",id:2},{name:"已取消",id:3}]},"list",[]),"currentPage",1),"totalPages",1),"loading",!1),"loadStatus","loadmore"),"userId",""),"coachId",""),"isCoach",!1),"status",""),"isAdmin",!1)},onPullDownRefresh:function(){this.currentPage=1,this.loadData(),this.$nextTick((function(){o.stopPullDownRefresh()}))},onLoad:function(t){this.isJiaoLian=+(null===t||void 0===t?void 0:t.isJiaoLian)||0,this.currentTab=+(null===t||void 0===t?void 0:t.index)||0,this.status=this.tabList[this.currentTab].id,this.loading=!0;var e=o.getStorageSync("userRoles");1==e.includes("shop_1_coach")?(this.isCoach=!0,this.coachId=o.getStorageSync("wxUserInfo").memberId,this.userId=""):(this.isCoach=!1,this.userId=o.getStorageSync("wxUserInfo").memberId,this.coachId=""),1==e.includes("shop_1_admin")&&(this.isAdmin=!0,console.log("admin")),this.loadData()},onShow:function(){},methods:r(r(r(r(r(r({changeCurrentType:function(t){this.currentType=t,this.currentPage=1,this.totalPages=1,this.loadData()},clickTab:function(t){var e=t.id;t.index;this.status=e,this.currentPage=1,this.loadData()},cancelAgree:function(t){var e=this;o.showModal({title:"确认",content:"是否取消预约？",success:function(n){n.confirm&&a.default.getMyBookingCancel({data:{memberBookingIds:t}}).then((function(t){200==t.code&&(o.showToast({title:"取消预约成功",icon:"none"}),e.currentPage=1,e.loadData())}))}})},cancelForce:function(t){var e=this;"0"!=t.superCancelStatus?"1"!=t.superCancelStatus&&("2"!=t.superCancelStatus?o.showModal({title:"确认",content:"您的预约教练已经同意，强制取消需要管理员审核",success:function(n){n.confirm&&a.default.getMyBookingCancelForce({data:{memberBookingIds:t.memberBookingId}}).then((function(t){200==t.code&&(o.showToast({title:"取消预约申请成功",icon:"none"}),e.currentPage=1,e.loadData())}))}}):o.showToast({title:"被拒绝后无法重新提交"})):o.showToast({title:"请勿重复提交"})},agree:function(t){var e=this;o.showModal({title:"确认",content:"是否确认预约？",success:function(n){n.confirm&&a.default.getMyBookingAgree({data:{memberBookingIds:t}}).then((function(t){200==t.code&&(o.showToast({title:"确认预约成功",icon:"none"}),e.currentPage=1,e.loadData())}))}})},userAgree:function(t){var e=this;o.showModal({title:"确认",content:"是否确认预约？",success:function(n){n.confirm&&a.default.getMyBookingUserAgree({data:{memberBookingIds:t}}).then((function(t){200==t.code&&(o.showToast({title:"确认预约成功",icon:"none"}),e.currentPage=1,e.loadData())}))}})},getGroupList:function(){var t=this,e=this.isCoach?a.default.getTuanKeList:a.default.getGroupCourseBookingListMember;e({data:{coachId:this.coachId||"",userId:this.userId||"",bookingStatus:this.status,pageNum:this.currentPage,pageSize:20}}).then((function(e){1==t.currentPage?t.list=e.rows:t.list=t.list.concat(e.rows),t.totalPages=Math.floor(e.total/20)+1,t.$nextTick((function(){o.hideToast()}))}))},confirmQianDao:function(t){var e=this;a.default.getMyBookingSignIn({data:{memberBookingId:t}}).then((function(t){200==t.code&&(o.showToast({title:"签到成功",icon:"none"}),e.currentPage=1,e.loadData())}))},getCoachList:function(){this.isCoach?this.getCoachList():this.getMyList()},loadData:function(){this.currentType?this.getGroupList():this.isCoach?this.getCoachList():this.getMyList()},loadClick:function(){"loadmore"===this.loadStatus&&this.loadData()}},"getCoachList",(function(){var t=this;console.log("am I coach",this.isCoach),a.default.getMyBookingCoachMy({data:{status:this.status,pageNo:this.currentPage,pageSize:20,sort:"bookingTime desc"}}).then((function(e){t.list=e.rows,t.loadStatus=20*(t.currentPage-1)+(t.list.length||0)<e.total?"loadmore":"nomore","loadmore"===t.loadStatus&&t.currentPage++,t.$nextTick((function(){t.loading=!1}))})).catch((function(e){t.loadStatus="loadmore"}))})),"getMyList",(function(){var t=this;console.log("am I guest",this.isCoach),this.loadStatus="loading",a.default.getMyBooking({data:{status:this.status,pageNo:this.currentPage,pageSize:20,sort:"bookingTime desc"}}).then((function(e){t.list=e.rows,t.loadStatus=20*(t.currentPage-1)+(t.list.length||0)<e.total?"loadmore":"nomore","loadmore"===t.loadStatus&&t.currentPage++,t.$nextTick((function(){t.loading=!1})).catch((function(e){t.loadStatus="loadmore"}))}))})),"returnStatusName",(function(t){var e="";switch(t){case"0":e="预约中";break;case"1":e="已预约";break;case"2":e="已签到";break;case"3":e="已取消";break;case"4":e="已失效";break;default:break}return e})),"handleManage",(function(){o.navigateTo({url:"/pages/user/yu-yue/yu-yue-manage"})})),"groupCancelAgree",(function(t,e){var n=this;o.showModal({title:"确认",content:"是否取消预约？",success:function(i){i.confirm&&a.default.getMyGroupBookingCancel({data:{groupCourseId:t,groupCourseBookingId:e,status:"0"}}).then((function(t){200==t.code&&(o.showToast({title:"预约已取消，如需退款请公众号发送【我要退款】",icon:"none"}),n.currentPage=1,n.loadData())}))}})})),"groupConfirmQianDao",(function(t){var e=this;a.default.getMyGroupBookingSignIn({data:{groupCourseId:t}}).then((function(t){200==t.code&&(o.showToast({title:"签到成功",icon:"none"}),e.currentPage=1,e.loadData())}))}))}}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(71536)}));t.O()}]);