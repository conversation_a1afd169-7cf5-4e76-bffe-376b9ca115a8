(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom"],{16649:function(){},26397:function(e,t,u){"use strict";var n;u.r(t),u.d(t,{__esModule:function(){return i.__esModule},default:function(){return m}});var o,a=function(){var e=this,t=e.$createElement,u=(e._self._c,e.__get_style([e.style]));e.$mp.data=Object.assign({},{$root:{s0:u}})},s=[],i=u(31877),l=i["default"],c=u(16649),f=u.n(c),r=(f(),u(18535)),d=(0,r["default"])(l,a,s,!1,null,"2dbaa680",null,!1,n,o),m=d.exports},31877:function(e,t,u){"use strict";var n=u(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var o=a(u(97213));function a(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-safe-bottom",mixins:[n.$u.mpMixin,n.$u.mixin,o.default],data:function(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style:function(){var e={};return n.$u.deepMerge(e,n.$u.addStyle(this.customStyle))}},mounted:function(){}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component"],{},function(e){e("81715")["createComponent"](e(26397))}]);