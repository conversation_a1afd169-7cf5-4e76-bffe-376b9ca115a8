(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["layout/theme-wrap","pages-admin/changGuanGuanLi/xinZengChangGuan/index"],{12556:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var o=n(45013);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(Object(n),!0).forEach((function(e){u(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function u(t,e,n){return(e=c(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t){var e=s(t,"string");return"symbol"==r(e)?e:e+""}function s(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["default"]={computed:a({},(0,o.mapGetters)(["themeConfig"])),props:{isTab:{type:Boolean,default:!1}},mounted:function(){console.log(this.themeConfig)}}},19830:function(t,e,n){"use strict";var o=n(51372)["default"],r=n(81715)["createPage"];n(96910);a(n(923));var i=a(n(20006));function a(t){return t&&t.__esModule?t:{default:t}}o.__webpack_require_UNI_MP_PLUGIN__=n,r(i.default)},20006:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return p}});var o,r={uNavbar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(n.bind(n,66372))},"u-Input":function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u--input/u--input")]).then(n.bind(n,59242))},uPicker:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-picker/u-picker")]).then(n.bind(n,82125))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(n,78278))},"u-Image":function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u--image/u--image")]).then(n.bind(n,84027))},"u-Textarea":function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u--textarea/u--textarea")]).then(n.bind(n,72270))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("28105e16-1")),o=n?{color:t.$getSSP("28105e16-1","content")["navBarTextColor"]}:null,r=n?t.$getSSP("28105e16-1","content"):null,i=n?t.$getSSP("28105e16-1","content"):null,a=n?t._f("Img")(t.formList.logo):null,u=n?t.__map(t.returnIMgList(t.formList.shopImages),(function(e,n){var o=t.__get_orig(e),r=t._f("Img")(e);return{$orig:o,f1:r}})):null,c=n?t.$getSSP("28105e16-1","content"):null,s=n?t.$getSSP("28105e16-1","content"):null,l=n?t.$getSSP("28105e16-1","content"):null;t._isMounted||(t.e0=function(e){return t.uni.navigateBack()},t.e1=function(e){t.showProvince=!0},t.e2=function(e){t.showCity=!0}),t.$mp.data=Object.assign({},{$root:{m0:n,a0:o,m1:r,m2:i,f0:a,l0:u,m3:c,m4:s,m5:l}})},a=[],u=n(65061),c=u["default"],s=n(35698),l=n.n(s),f=(l(),n(18535)),h=(0,f["default"])(c,i,a,!1,null,"24220ef0",null,!1,r,o),p=h.exports},31051:function(t,e,n){"use strict";var o;n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return p}});var r,i=function(){var t=this,e=t.$createElement;t._self._c;t.$initSSP(),"augmented"===t.$scope.data.scopedSlotsCompiler&&t.$setSSP("content",{logo:t.themeConfig.logo,bgColor:t.themeConfig.baseBgColor,color:t.themeConfig.baseColor,buttonBgColor:t.themeConfig.buttonBgColor,buttonTextColor:t.themeConfig.buttonTextColor,buttonLightBgColor:t.themeConfig.buttonLightBgColor,navBarColor:t.themeConfig.navBarColor,navBarTextColor:t.themeConfig.navBarTextColor,couponColor:t.themeConfig.couponColor}),t.$callSSP()},a=[],u=n(12556),c=u["default"],s=n(69601),l=n.n(s),f=(l(),n(18535)),h=(0,f["default"])(c,i,a,!1,null,"5334cd47",null,!1,o,r),p=h.exports},35698:function(){},65061:function(t,e,n){"use strict";var o=n(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;n(77020);var r=i(n(68466));i(n(31051));function i(t){return t&&t.__esModule?t:{default:t}}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},n=Object.prototype,o=n.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function h(t,e,n,o){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new N(o||[]);return r(a,"_invoke",{value:j(t,n,u)}),a}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var m="suspendedStart",d="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function L(){}var P={};f(P,c,(function(){return this}));var _=Object.getPrototypeOf,S=_&&_(_(T([])));S&&S!==n&&o.call(S,c)&&(P=S);var x=L.prototype=b.prototype=Object.create(P);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(r,i,u,c){var s=p(t[r],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==a(f)&&o.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):e.resolve(f).then((function(t){l.value=t,u(l)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var i;r(this,"_invoke",{value:function(t,o){function r(){return new e((function(e,r){n(t,o,e,r)}))}return i=i?i.then(r,r):r()}})}function j(e,n,o){var r=m;return function(i,a){if(r===g)throw Error("Generator is already running");if(r===v){if("throw"===i)throw a;return{value:t,done:!0}}for(o.method=i,o.arg=a;;){var u=o.delegate;if(u){var c=k(u,o);if(c){if(c===y)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===m)throw r=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=g;var s=p(e,n,o);if("normal"===s.type){if(r=o.done?v:d,s.arg===y)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(r=v,o.method="throw",o.arg=s.arg)}}}function k(e,n){var o=n.method,r=e.iterator[o];if(r===t)return n.delegate=null,"throw"===o&&e.iterator.return&&(n.method="return",n.arg=t,k(e,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var i=p(r,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function n(){for(;++r<e.length;)if(o.call(e,r))return n.value=e[r],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=L,r(x,"constructor",{value:L,configurable:!0}),r(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},O(C.prototype),f(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new C(h(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(x),f(x,l,"Generator"),f(x,c,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=T,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(o,r){return u.type="throw",u.arg=e,n.next=o,r&&(n.method="next",n.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),s=o.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),I(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;I(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,o){return this.delegate={iterator:T(e),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function c(t,e,n,o,r,i,a){try{var u=t[i](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(o,r)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){c(i,o,r,a,u,"next",t)}function u(t){c(i,o,r,a,u,"throw",t)}a(void 0)}))}}function l(t,e,n){return(e=f(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t){var e=h(t,"string");return"symbol"==a(e)?e:e+""}function h(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=a(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["default"]={data:function(){return{selectOption:[],showProvince:!1,formList:l({companyId:1,shopName:"",shopImages:[],province:"",city:"",address:"",phone:"",wxpayMchid:"",logo:"",shopIntroduce:""},"shopImages",[]),provinceList:[],provinceName:"",cityList:[],cityName:"",showCity:!1}},onLoad:function(t){var e=this;r.default.getProvince({data:{}}).then((function(t){if(console.log(t.data[0],"省份选择器"),200==t.code){e.selectOption=t.data;var n=t.data[0],o=[];for(var r in n)o.push({label:n[r],id:r});e.provinceList=[o]}}))},methods:{returnIMgList:function(t){try{return t.split(",")}catch(e){return[]}},changePicker:function(t){console.log(t),this.showProvince=!1,this.formList.province=t.value[0].id,this.provinceName=t.value[0].label,this.formList.city="",this.cityName="";var e=this.selectOption[t.value[0].id],n=[];for(var o in e)n.push({label:e[o],id:o});this.cityList=[n]},changeCity:function(t){this.showCity=!1,this.formList.city=t.value[0].id,this.cityName=t.value[0].label},choseAdvice:function(){var t=this;o.chooseLocation({success:function(e){console.log(e),t.formList.longitude=e.longitude,t.formList.latitude=e.latitude,t.formList.address=e.address}})},choseLogo:function(){var t=this,e=o.getStorageSync("token");o.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album"],success:function(n){console.log(n),o.showLoading({mask:!0,title:"正在上传中……请稍后"});var r=n.tempFilePaths;o.uploadFile({url:t.$serverUrl+"/shop/shop/upload/logo",filePath:r[0],name:"logo",header:{Authorization:e},success:function(e){console.log(e.data);var n=JSON.parse(e.data);t.formList.logo=n.imgUrl},fail:function(t){console.log(t)},complete:function(){o.hideLoading()}})}})},choseImage:function(){var t=this,e=this.formList.shopImages;e.length>=0&&(e=6-e),this.formList.shopImages=[],o.chooseImage({count:5,sizeType:["original","compressed"],sourceType:["album"],success:function(){var e=s(u().mark((function e(n){var o,r;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log(n),o=0;case 2:if(!(o<n.tempFilePaths.length)){e.next=9;break}return r=n.tempFilePaths[o],e.next=6,t.upLoadImage(r);case 6:o++,e.next=2;break;case 9:case"end":return e.stop()}}),e)})));function n(t){return e.apply(this,arguments)}return n}()})},upLoadImage:function(t){var e=this;return s(u().mark((function n(){var r;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:o.showLoading({mask:!0,title:"正在上传中……"}),r=o.getStorageSync("token"),o.uploadFile({url:e.$serverUrl+"/shop/shop/upload/image",filePath:t,name:"image",header:{Authorization:r},success:function(t){var n=JSON.parse(t.data);""==e.formList.shopImages?e.formList.shopImages=n.imgUrl:e.formList.shopImages=e.formList.shopImages+","+n.imgUrl,o.hideLoading()},fail:function(t){console.log(t)},complete:function(){o.hideLoading()}});case 3:case"end":return n.stop()}}),n)})))()},clickLogo:function(){console.log(1232);var t=this.src;o.previewImage({urls:[t],longPressActions:{success:function(t){console.log(t)},fail:function(t){console.log(t.errMsg)}}})},confirm:function(){var t=this;if(""!=this.formList.shopName){o.showLoading({mask:!0,title:"新增场馆中，请稍后……"});var e=Object.assign({},this.formList);r.default.addShopShop({data:e,method:"POST"}).then((function(e){o.hideLoading(),200==e.code&&(t.$u.toast("新增成功！"),setTimeout((function(){o.navigateBack()}),2e3))})).catch((function(t){o.hideLoading()}))}else this.$u.toast("场馆名称不能为空")}}}},69601:function(){}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(19830)}));t.O()}]);