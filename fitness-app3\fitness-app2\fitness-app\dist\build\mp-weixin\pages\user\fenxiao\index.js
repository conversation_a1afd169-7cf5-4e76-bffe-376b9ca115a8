"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/user/fenxiao/index"],{14137:function(e,n,u){var t;u.r(n),u.d(n,{__esModule:function(){return f.__esModule},default:function(){return i}});var r,a=function(){var e=this,n=e.$createElement;e._self._c},l=[],f=u(79847),o=f["default"],c=u(18535),_=(0,c["default"])(o,a,l,!1,null,null,null,!1,t,r),i=_.exports},34364:function(e,n,u){var t=u(51372)["default"],r=u(81715)["createPage"];u(96910);l(u(923));var a=l(u(14137));function l(e){return e&&e.__esModule?e:{default:e}}t.__webpack_require_UNI_MP_PLUGIN__=u,r(a.default)},79847:function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;n["default"]={}}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],(function(){return n(34364)}));e.O()}]);