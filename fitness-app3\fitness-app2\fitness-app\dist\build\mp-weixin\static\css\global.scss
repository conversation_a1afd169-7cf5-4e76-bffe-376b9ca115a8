*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    background-color: #f3f3f3;
    --animate-duration: 1s;
    --animate-delay: 1s;
    --animate-repeat: 1;
}

body.pages-u-avatar-cropper-u-avatar-cropper {
    background-color: #000;
}

.loading {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    width: 100%;
    padding-left: 30upx;
    padding-right: 30upx;
}
// 表单自定义样式

// 表单laber 单元格title
.u-form-item__body__left__content__label
{
  font-size: 30upx !important;
  font-weight: bold !important;
}
.u-form-item__body__right__message {
  margin-left: 0 !important;
}

// 输入框input
.u-input__content__field-wrapper__field {
  font-size: 30upx !important;
  text-align: right !important;
}

.u-radio-group,.u-checkbox-group,.u-textarea {
  margin-top: 16upx;
}

// 单元格 cell
.u-cell__body {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.u-cell--clickable {
  background-color: #FFF !important;
}
.u-cell__title-text {
  color: #000;
  font-weight: bold;
}
.u-icon__icon--info {
  color: #000 !important;
  width: 24rpx !important;
}


// 自定义颜色
.fc-999 {
  color: #999;
}
.fc-fff{
  color: #fff;
}
.fw-bold,.font-bold {
  font-weight: bold;
}

// uview1.*内置样式
.u-relative,
.u-rela {
    position: relative;
}

.bottom-blk{
  bottom:0;
  left:0;
  position: fixed;
  z-index: 999;
  box-shadow: 0 0 20rpx rgba(0,0,0,.1);
}
.bottom-placeholder{
  padding-bottom: 200rpx;
}
.u-absolute,
.u-abso {
    position: absolute;
}

// nvue不能用标签命名样式，不能放在微信组件中，否则微信开发工具会报警告，无法使用标签名当做选择器

/* #ifndef APP-NVUE */

image {
    display: inline-block;
}

// 在weex，也即nvue中，所有元素默认为border-box
view,
text {
    box-sizing: border-box;
}


/* #endif */

.u-font-xs {
    font-size: 22rpx;
}

.u-font-sm {
    font-size: 26rpx;
}

.u-font-md {
    font-size: 28rpx;
}

.u-font-lg {
    font-size: 30rpx;
}

.u-font-xl {
    font-size: 34rpx;
}
.flex-0{
  flex-shrink: 0;
  flex-grow: 0;
}

.u-flex {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
}
.text-no-wrap{
  white-space: nowrap;
}
.u-flex-wrap {
    flex-wrap: wrap;
}

.u-flex-nowrap {
    flex-wrap: nowrap;
}

.u-col-center {
    align-items: center;
}

.u-col-top,
.u-col-start {
    align-items: flex-start;
}

.u-col-bottom,
.u-col-end {
    align-items: flex-end;
}

.u-row-center {
    justify-content: center;
}

.u-row-left,
.u-row-start {
    justify-content: flex-start;
}

.u-row-right,
.u-row-end {
    justify-content: flex-end;
}

.u-row-between {
    justify-content: space-between;
}

.u-row-around {
    justify-content: space-around;
}

.u-text-left {
    text-align: left;
}

.u-text-center {
    text-align: center;
}

.u-text-right {
    text-align: right;
}

.u-flex-col {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
}

// 定义flex等分
@for $i from 0 through 12 {
    .u-flex-#{$i} {
        flex: $i;
    }
}

// 定义字体(px)单位，小于20都为px单位字体
@for $i from 9 to 20 {
    .u-font-#{$i} {
        font-size: $i + px;
    }
}

// 定义字体(rpx)单位，大于或等于20的都为rpx单位字体
@for $i from 20 through 40 {
    .u-font-#{$i} {
        font-size: $i + rpx;
    }
}

// 定义内外边距，历遍1-80
@for $i from 0 through 80 {
    // 只要双数和能被5除尽的数
    @if $i % 2==0 or $i % 5==0 {
        // 得出：u-margin-30或者u-m-30
        .u-margin-#{$i},
        .u-m-#{$i} {
            margin: $i + rpx !important;
        }
        // 得出：u-padding-30或者u-p-30
        .u-padding-#{$i},
        .u-p-#{$i} {
            padding: $i + rpx !important;
        }
        @each $short,
        $long in l left,
        t top,
        r right,
        b bottom {
            // 缩写版，结果如： u-m-l-30
            // 定义外边距
            .u-m-#{$short}-#{$i} {
                margin-#{$long}: $i + rpx !important;
            }
            // 定义内边距
            .u-p-#{$short}-#{$i} {
                padding-#{$long}: $i + rpx !important;
            }
            // 完整版，结果如：u-margin-left-30
            // 定义外边距
            .u-margin-#{$long}-#{$i} {
                margin-#{$long}: $i + rpx !important;
            }
            // 定义内边距
            .u-padding-#{$long}-#{$i} {
                padding-#{$long}: $i + rpx !important;
            }
        }
    }
}

.h-100 {
    height: 100%;
}

.w-100 {
    width: 100%;
}
.bg-fff{
  background-color: #fff;
}
// 骨架占位样式
.placeholder {
    min-height: 30rpx;
    background: linear-gradient(90deg, #f1f2f4 25%, #e6e6e6 37%, #f1f2f4 50%);
    background-size: 400% 100%;
    border-radius: 3px;
    animation: skeleton 1.8s ease infinite;
}

@keyframes skeleton {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}
.overflow-hidden{
  overflow: hidden;
}
@for $i from 1 through 5 {
    .placeholder-h-#{$i} {
      @extend .placeholder;
        height: $i*20%;
    }
    .placeholder-w-#{$i} {
      @extend .placeholder;
      width: $i*20%;
  }
  .border-#{$i*8} {
    border-radius: $i*8rpx;
  }
}
.placeholder-h-100{
  @extend .placeholder;
  min-height: 100%;
}
.placeholder-w-100{
  @extend .placeholder;
  min-width: 100%;
}
/* 文字超出截断隐藏 */

.clamp {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

.clamp-2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.clamp-3 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}

.shake {
  animation: shake 0.5s;
}
@keyframes shake {
  0% {
    transform: translate(0, 0);
  }
  10% {
    transform: translate(-5px, 0);
  }
  20% {
    transform: translate(5px, 0);
  }
  30% {
    transform: translate(-5px, 0);
  }
  40% {
    transform: translate(5px, 0);
  }
  50% {
    transform: translate(-5px, 0);
  }
  60% {
    transform: translate(5px, 0);
  }
  70% {
    transform: translate(-5px, 0);
  }
  80% {
    transform: translate(5px, 0);
  }
  90% {
    transform: translate(-5px, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
