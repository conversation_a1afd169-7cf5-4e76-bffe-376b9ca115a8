<theme-wrap vue-id="66389f61-1" bind:__l="__l" vue-slots="{{['content']}}"><view class="u-flex-col u-row-center u-col-center u-p-t-80" slot="content"><image class="u-m-t-80" style="width:220rpx;height:220rpx;" src="/static/images/icons/success.png" mode="widthFix"></image><view class="u-p-t-30 u-p-b-30 font-bold u-font-40">预约成功!</view><view class="u-flex u-row-center u-col-center u-p-t-40 u-font-30 font-bold"><navigator class="u-m-r-60 u-p-t-14 u-p-b-14 u-p-r-28 u-p-l-28 border-8" style="border:1px solid;" url="/pages/index/index" open-type="switchTab">返回首页</navigator><navigator class="u-m-l-60 u-p-t-14 u-p-b-14 u-p-r-28 u-p-l-28 border-8 fc-fff" style="border:1px solid;background-color:#000;" url="/pages/user/yu-yue/index" open-type="redirectTo">我的预约</navigator></view></view></theme-wrap>