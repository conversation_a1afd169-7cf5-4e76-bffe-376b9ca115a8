(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-loading-icon/u-loading-icon"],{13791:function(){},32871:function(e,n,t){"use strict";var i=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var o=u(t(35464));function u(e){return e&&e.__esModule?e:{default:e}}n["default"]={name:"u-loading-icon",mixins:[i.$u.mpMixin,i.$u.mixin,o.default],data:function(){return{array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}},computed:{otherBorderColor:function(){var e=i.$u.colorGradient(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show:function(e){}},mounted:function(){this.init()},methods:{init:function(){setTimeout((function(){}),20)},addEventListenerToWebview:function(){var e=this,n=getCurrentPages(),t=n[n.length-1],i=t.$getAppWebview();i.addEventListener("hide",(function(){e.webviewHide=!0})),i.addEventListener("show",(function(){e.webviewHide=!1}))}}}},94597:function(e,n,t){"use strict";var i;t.r(n),t.d(n,{__esModule:function(){return l.__esModule},default:function(){return h}});var o,u=function(){var e=this,n=e.$createElement,t=(e._self._c,e.show?e.__get_style([e.$u.addStyle(e.customStyle)]):null),i=e.show&&!e.webviewHide?e.$u.addUnit(e.size):null,o=e.show&&!e.webviewHide?e.$u.addUnit(e.size):null,u=e.show&&e.text?e.$u.addUnit(e.textSize):null;e.$mp.data=Object.assign({},{$root:{s0:t,g0:i,g1:o,g2:u}})},a=[],l=t(32871),d=l["default"],s=t(13791),c=t.n(s),r=(c(),t(18535)),f=(0,r["default"])(d,u,a,!1,null,"5ebd6a54",null,!1,i,o),h=f.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-loading-icon/u-loading-icon-create-component"],{},function(e){e("81715")["createComponent"](e(94597))}]);