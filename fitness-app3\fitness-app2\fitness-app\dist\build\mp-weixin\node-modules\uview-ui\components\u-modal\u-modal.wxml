<u-popup vue-id="64f5513e-1" mode="center" zoom="{{zoom}}" show="{{show}}" customStyle="{{$root.a0}}" closeOnClickOverlay="{{closeOnClickOverlay}}" safeAreaInsetBottom="{{false}}" duration="{{duration}}" data-event-opts="{{[['^click',[['clickHandler']]]]}}" bind:click="__e" class="data-v-c1fe7cea" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-modal data-v-c1fe7cea" style="{{'width:'+($root.g0)+';'}}"><block wx:if="{{title}}"><text class="u-modal__title data-v-c1fe7cea">{{title}}</text></block><view class="u-modal__content data-v-c1fe7cea" style="{{'padding-top:'+((title?12:25)+'px')+';'}}"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><text class="u-modal__content__text data-v-c1fe7cea">{{content}}</text></block></view><block wx:if="{{$slots.confirmButton}}"><view class="u-modal__button-group--confirm-button data-v-c1fe7cea"><slot name="confirmButton"></slot></view></block><block wx:else><u-line vue-id="{{('64f5513e-2')+','+('64f5513e-1')}}" class="data-v-c1fe7cea" bind:__l="__l"></u-line><view class="u-modal__button-group data-v-c1fe7cea" style="{{'flex-direction:'+(buttonReverse?'row-reverse':'row')+';'}}"><block wx:if="{{showCancelButton}}"><view class="{{['u-modal__button-group__wrapper','u-modal__button-group__wrapper--cancel','data-v-c1fe7cea',showCancelButton&&!showConfirmButton&&'u-modal__button-group__wrapper--only-cancel']}}" hover-stay-time="{{150}}" hover-class="u-modal__button-group__wrapper--hover" data-event-opts="{{[['tap',[['cancelHandler',['$event']]]]]}}" bindtap="__e"><text class="u-modal__button-group__wrapper__text data-v-c1fe7cea" style="{{'color:'+(cancelColor)+';'}}">{{cancelText}}</text></view></block><block wx:if="{{showConfirmButton&&showCancelButton}}"><u-line vue-id="{{('64f5513e-3')+','+('64f5513e-1')}}" direction="column" class="data-v-c1fe7cea" bind:__l="__l"></u-line></block><block wx:if="{{showConfirmButton}}"><view class="{{['u-modal__button-group__wrapper','u-modal__button-group__wrapper--confirm','data-v-c1fe7cea',!showCancelButton&&showConfirmButton&&'u-modal__button-group__wrapper--only-confirm']}}" hover-stay-time="{{150}}" hover-class="u-modal__button-group__wrapper--hover" data-event-opts="{{[['tap',[['confirmHandler',['$event']]]]]}}" bindtap="__e"><block wx:if="{{loading}}"><u-loading-icon vue-id="{{('64f5513e-4')+','+('64f5513e-1')}}" class="data-v-c1fe7cea" bind:__l="__l"></u-loading-icon></block><block wx:else><text class="u-modal__button-group__wrapper__text data-v-c1fe7cea" style="{{'color:'+(confirmColor)+';'}}">{{confirmText}}</text></block></view></block></view></block></view></u-popup>