{"version": 3, "file": "pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,+VAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACxBA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAEA;EACAG,UAAA;IACAC,SAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IAAA,IAAAC,KAAA;IACAC,YAAA,CAAAC,cAAA;MACAL,IAAA;QACAM,SAAA;MACA;IACA,GAAAC,IAAA,WAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA,IAAAA,GAAA,CAAAG,IAAA;QACAR,KAAA,CAAAF,MAAA,GAAAO,GAAA,CAAAR,IAAA;MACA;IACA;EACA;EACAY,OAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,IAAA;MACAC,GAAA,CAAAC,UAAA;QACAC,GAAA,4DAAAC,IAAA,CAAAC,SAAA,CAAAL,IAAA;MACA;IACA;IACA;IACAM,OAAA,WAAAA,QAAA;MACAL,GAAA,CAAAC,UAAA;QACAC,GAAA;MACA;IACA;EACA;AACA;;;;;;;;;;ACnFA;;;;;;;;;;;;;;;ACAAvB,mBAAA;AAGA,IAAA2B,IAAA,GAAA5B,sBAAA,CAAAC,mBAAA;AACA,IAAA4B,MAAA,GAAA7B,sBAAA,CAAAC,mBAAA;AAAqE,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAHrE;AACA4B,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC;;;;;;;;;;;;;;;;;;;ACL8G;AAC9H;AACA,CAAyD;AACL;AACpD,CAA0F;;;AAG1F;AACsI;AACtI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBigB,CAAC,+DAAe,ydAAG,EAAC;;;;;;;;;;;;;;;;;ACA6e,CAAC,+DAAe,y5BAAG,EAAC", "sources": ["webpack:///./src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue?1700", "uni-app:///src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue", "webpack:///./src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue?17e9", "uni-app:///src/main.js", "webpack:///./src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue?2809", "webpack:///./src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue?ca23", "webpack:///./src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue?99f5", "webpack:///./src/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue?e23b"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"fcd13606-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"fcd13606-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"fcd13606-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"fcd13606-1\", \"content\") : null\n  var g0 = m0 ? _vm.qyList.length : null\n  var m3 = m0 && g0 > 0 ? _vm.$getSSP(\"fcd13606-1\", \"content\") : null\n  var m4 = m0 && g0 > 0 ? _vm.$getSSP(\"fcd13606-1\", \"content\") : null\n  var m5 = m0 && g0 > 0 ? _vm.$getSSP(\"fcd13606-1\", \"content\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <themeWrap>\n    <template #content=\"{ navBarColor, navBarTextColor,buttonTextColor,buttonLightBgColor }\">\n      <view>\n        <!-- 顶部菜单栏 -->\n        <u-navbar title=\"企业信息管理\" @rightClick=\"uni.navigateBack()\" :titleStyle=\"{ color: navBarTextColor }\"\n          :bgColor=\"navBarColor\" :leftIconColor=\"navBarTextColor\" :autoBack=\"true\" :placeholder=\"true\"\n          :safeAreaInsetTop=\"true\">\n        </u-navbar>\n        <view v-if=\"qyList.length > 0\">\n          <!-- 企业信息列表 -->\n          <view class=\"uList\" v-for=\"(list, idx) in qyList\" :key=\"idx\" @click=\"gotoDetails(list)\">\n            <!-- <view class=\"enterpriseImg\">\n              <u--image\n                :src=\"list.src\"\n                width=\"180rpx\"\n                height=\"180rpx\"\n                radius=\"8rpx\"\n              ></u--image>\n            </view> -->\n            <view class=\"enterpriseBody\">\n              <view class=\"enterpriseTitle bold\">{{ list.companyName }}</view>\n              <!-- <view class=\"enterpriseValue\">{{ list.introduce }}</view> -->\n              <view class=\"enterpriseBottom u-flex w-100\">\n                <view class=\"u-m-l-20 u-flex-1\">联系人：{{ list.contractName }}</view>\n                <view>联系电话：{{ list.contractPhone }}</view>\n              </view>\n            </view>\n          </view>\n          <!-- 额外白色占位区块 -->\n          <view class=\"whiteView\"></view>\n          <!-- 新增企业 -->\n          <view class=\"bottonBtn u-flex\">\n            <view class=\"confirmBtn\" @click=\"confirm()\"\n              :style=\"{'background': buttonLightBgColor, color: buttonTextColor, 'border-color': buttonLightBgColor}\">\n              新增企业</view>\n          </view>\n        </view>\n        <!-- 列表无数据 -->\n        <u-empty marginTop=\"150\" mode=\"list\" v-else></u-empty>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\n  import api from \"@/common/api\";\n  import themeWrap from \"../../../layout/theme-wrap.vue\";\n  export default {\n    components: {\n      themeWrap,\n    },\n    data() {\n      return {\n        qyList: [],\n      };\n    },\n    onShow() {\n      api.getCompanyList({\n        data: {\n          companyId: 1\n        }\n      }).then((res) => {\n        console.log(res);\n        if (res.code == 200) {\n          this.qyList = res.data;\n        }\n      })\n    },\n    methods: {\n      // 跳转企业详情\n      gotoDetails(list) {\n        uni.navigateTo({\n          url: \"/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/details?list=\" + JSON.stringify(list),\n        });\n      },\n      // 新增企业\n      confirm() {\n        uni.navigateTo({\n          url: \"/pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/add\",\n        });\n      }\n    },\n  };\n</script>\n\n<style scoped lang=\"scss\">\n  .uList {\n    height: 160rpx;\n    width: 750rpx;\n    border-bottom: 1px solid #efefef;\n    display: flex;\n\n    .enterpriseImg {\n      width: 180rpx;\n      height: 180rpx;\n      margin: 20rpx;\n    }\n\n    .enterpriseBody {\n      margin-left: 20rpx;\n      width: 730rpx;\n      display: flex;\n      flex-direction: column;\n      position: relative;\n\n\n      .enterpriseTitle {\n        margin-top: 20rpx;\n        font-size: 30rpx;\n        text-overflow: -o-ellipsis-lastline;\n        overflow: hidden; //溢出内容隐藏\n        text-overflow: ellipsis; //文本溢出部分用省略号表示\n      }\n\n      .enterpriseBottom {\n        font-size: 24rpx;\n        color: #666;\n        position: absolute;\n        right: 20rpx;\n        bottom: 20rpx;\n      }\n    }\n  }\n\n  .whiteView {\n    width: 750rpx;\n    height: 200rpx;\n  }\n\n  .bottonBtn {\n    height: 160rpx;\n    width: 750rpx;\n    position: fixed;\n    background-color: white;\n    bottom: 0;\n    border-top: 1px solid #e6e6e6;\n\n    .confirmBtn {\n      width: 600rpx;\n      height: 80rpx;\n      margin: 0 auto;\n      text-align: center;\n      line-height: 80rpx;\n      border: 1px solid;\n      border-radius: 40rpx;\n    }\n  }\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=dedd7dae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=dedd7dae&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dedd7dae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/index.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=dedd7dae&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=dedd7dae&scoped=true&lang=scss&\"", "export * from \"-!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=dedd7dae&scoped=true&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "components", "themeWrap", "data", "qyList", "onShow", "_this", "api", "getCompanyList", "companyId", "then", "res", "console", "log", "code", "methods", "gotoDetails", "list", "uni", "navigateTo", "url", "JSON", "stringify", "confirm", "_vue", "_index", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}