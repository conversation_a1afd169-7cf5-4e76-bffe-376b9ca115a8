(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["layout/theme-wrap","pages-admin/qiYeGuanLi/qiYeXinXiGuanLi/add"],{332:function(t,e,o){"use strict";var n=o(51372)["default"],r=o(81715)["createPage"];o(96910);i(o(923));var u=i(o(64426));function i(t){return t&&t.__esModule?t:{default:t}}n.__webpack_require_UNI_MP_PLUGIN__=o,r(u.default)},12556:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=o(45013);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function u(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,n)}return o}function i(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?u(Object(o),!0).forEach((function(e){a(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):u(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function a(t,e,o){return(e=c(e))in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function c(t){var e=l(t,"string");return"symbol"==r(e)?e:e+""}function l(t,e){if("object"!=r(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["default"]={computed:i({},(0,n.mapGetters)(["themeConfig"])),props:{isTab:{type:Boolean,default:!1}},mounted:function(){console.log(this.themeConfig)}}},31051:function(t,e,o){"use strict";var n;o.r(e),o.d(e,{__esModule:function(){return a.__esModule},default:function(){return m}});var r,u=function(){var t=this,e=t.$createElement;t._self._c;t.$initSSP(),"augmented"===t.$scope.data.scopedSlotsCompiler&&t.$setSSP("content",{logo:t.themeConfig.logo,bgColor:t.themeConfig.baseBgColor,color:t.themeConfig.baseColor,buttonBgColor:t.themeConfig.buttonBgColor,buttonTextColor:t.themeConfig.buttonTextColor,buttonLightBgColor:t.themeConfig.buttonLightBgColor,navBarColor:t.themeConfig.navBarColor,navBarTextColor:t.themeConfig.navBarTextColor,couponColor:t.themeConfig.couponColor}),t.$callSSP()},i=[],a=o(12556),c=a["default"],l=o(69601),s=o.n(l),f=(s(),o(18535)),d=(0,f["default"])(c,u,i,!1,null,"5334cd47",null,!1,n,r),m=d.exports},37601:function(){},64426:function(t,e,o){"use strict";o.r(e),o.d(e,{__esModule:function(){return a.__esModule},default:function(){return m}});var n,r={uNavbar:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(o.bind(o,66372))},"u-Input":function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u--input/u--input")]).then(o.bind(o,59242))}},u=function(){var t=this,e=t.$createElement,o=(t._self._c,t.$hasSSP("ded91a64-1")),n=o?{color:t.$getSSP("ded91a64-1","content")["navBarTextColor"]}:null,r=o?t.$getSSP("ded91a64-1","content"):null,u=o?t.$getSSP("ded91a64-1","content"):null,i=o?t.$getSSP("ded91a64-1","content"):null,a=o?t.$getSSP("ded91a64-1","content"):null,c=o?t.$getSSP("ded91a64-1","content"):null;t._isMounted||(t.e0=function(e){return t.uni.navigateBack()}),t.$mp.data=Object.assign({},{$root:{m0:o,a0:n,m1:r,m2:u,m3:i,m4:a,m5:c}})},i=[],a=o(95331),c=a["default"],l=o(37601),s=o.n(l),f=(s(),o(18535)),d=(0,f["default"])(c,u,i,!1,null,"4f3f2834",null,!1,r,n),m=d.exports},69601:function(){},95331:function(t,e,o){"use strict";var n=o(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var r=u(o(68466));u(o(31051));function u(t){return t&&t.__esModule?t:{default:t}}e["default"]={data:function(){return{formList:{companyId:1,companyName:"",contractName:"",address:"",contractPhone:""}}},onLoad:function(t){},methods:{choseAdvice:function(){var t=this;n.chooseLocation({success:function(e){console.log(e),console.log("经度："+e.longitude),console.log("纬度："+e.latitude),console.log("详细地址："+e.address),console.log("名称："+e.name),t.formList.address=e.address}})},confirm:function(){var t=this;""!=this.formList.companyName?""!=this.formList.contractName?""!=this.formList.address?""!=this.formList.contractPhone?r.default.getAddCompany({data:this.formList,method:"POST"}).then((function(e){200==e.code&&(t.$u.toast("新增成功！"),setTimeout((function(){n.navigateBack()}),2e3))})).catch((function(t){})):this.$u.toast("联系电话不能为空"):this.$u.toast("地址不能为空"):this.$u.toast("联系人不能为空"):this.$u.toast("单位名称不能为空")}}}}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(332)}));t.O()}]);