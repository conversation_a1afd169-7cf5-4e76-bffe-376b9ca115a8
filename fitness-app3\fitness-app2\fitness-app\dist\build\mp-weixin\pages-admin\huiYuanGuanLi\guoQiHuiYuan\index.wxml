<theme-wrap scoped-slots-compiler="augmented" vue-id="eba450e4-1" class="data-v-6744403a" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-6744403a" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('eba450e4-2')+','+('eba450e4-1')}}" title="过期会员管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-6744403a" bind:__l="__l"></u-navbar><view class="searchView data-v-6744403a" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('eba450e4-3')+','+('eba450e4-1')}}" showAction="{{false}}" placeholder="请输入用户昵称或手机号" animation="{{true}}" data-event-opts="{{[['^click',[['goSearch']]]]}}" bind:click="__e" class="data-v-6744403a" bind:__l="__l"></u-search></view><view class="tabsView u-p-r-20 data-v-6744403a"><u-tabs vue-id="{{('eba450e4-4')+','+('eba450e4-1')}}" activeStyle="{{$root.a1}}" lineColor="{{$root.m4['buttonLightBgColor']}}" list="{{list1}}" data-event-opts="{{[['^click',[['click']]]]}}" bind:click="__e" class="data-v-6744403a" bind:__l="__l" vue-slots="{{['right']}}"><view style="padding-left:4px;" slot="right" data-event-opts="{{[['tap',[['tabsRightClick',['$event']]]]]}}" bindtap="__e" class="data-v-6744403a"><u-icon vue-id="{{('eba450e4-5')+','+('eba450e4-4')}}" name="list" size="21" bold="{{true}}" class="data-v-6744403a" bind:__l="__l"></u-icon></view></u-tabs></view><u-gap vue-id="{{('eba450e4-6')+','+('eba450e4-1')}}" height="1" bgColor="#bbb" class="data-v-6744403a" bind:__l="__l"></u-gap><scroll-view class="scrollView data-v-6744403a" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:for="{{showList}}" wx:for-item="list" wx:for-index="index" wx:key="*this"><view class="userList data-v-6744403a"><view class="user_avatar data-v-6744403a"><u-avatar vue-id="{{('eba450e4-7-'+index)+','+('eba450e4-1')}}" size="80rpx" src="{{src}}" shape="circle" class="data-v-6744403a" bind:__l="__l"></u-avatar></view><view class="user_textView data-v-6744403a"><text class="data-v-6744403a">晴天雨绵</text><view class="user_tag data-v-6744403a"><view class="tag data-v-6744403a">月卡（预售）</view><view class="tag data-v-6744403a">年会员</view></view></view><view class="user_rightView data-v-6744403a"><view class="rightText data-v-6744403a">近期未跟进</view></view></view></block></scroll-view></view></theme-wrap>