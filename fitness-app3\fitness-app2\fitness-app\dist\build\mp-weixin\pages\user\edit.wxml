<theme-wrap scoped-slots-compiler="augmented" vue-id="96ebc1fe-1" bind:__l="__l" vue-slots="{{['content']}}"><view class="page" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('96ebc1fe-2')+','+('96ebc1fe-1')}}" title="个人信息" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" bind:__l="__l"></u-navbar><u-no-network vue-id="{{('96ebc1fe-3')+','+('96ebc1fe-1')}}" bind:__l="__l"></u-no-network><u-toast class="vue-ref" vue-id="{{('96ebc1fe-4')+','+('96ebc1fe-1')}}" data-ref="uToast" bind:__l="__l"></u-toast><u-notify class="vue-ref" vue-id="{{('96ebc1fe-5')+','+('96ebc1fe-1')}}" data-ref="uNotify" bind:__l="__l"></u-notify><view class="content container"><view><view class="u-flex u-row-center u-col-center"><view class="avatar-wrap u-relative bg-000"><button class="u-reset-button" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['chooseWxAvatar',['$event']]]]]}}" bindchooseavatar="__e"><view class="avatar u-relative"><image style="width:144rpx;max-height:144rpx;border-radius:16rpx;" src="{{form.avatar_url}}" mode="widthFix"></image><view class="u-absolute edit-icon"><u-icon vue-id="{{('96ebc1fe-6')+','+('96ebc1fe-1')}}" name="edit-pen" color="#fff" size="20" bind:__l="__l"></u-icon></view></view></button></view></view><u--form class="vue-ref" vue-id="{{('96ebc1fe-7')+','+('96ebc1fe-1')}}" labelPosition="left" model="{{form}}" labelWidth="80" errorType="toast" data-ref="Form" bind:__l="__l" vue-slots="{{['default']}}"><view class="card u-m-t-40 u-p-b-20"><u-form-item vue-id="{{('96ebc1fe-8')+','+('96ebc1fe-7')}}" label="昵称" prop="nickName" bind:__l="__l" vue-slots="{{['default']}}"><input style="text-align:right;font-size:30rpx;" type="nickname" maxlength="{{16}}" color="#000" placeholder="限2-20个字" data-event-opts="{{[['change',[['changeName',['$event']]]],['input',[['__set_model',['$0','nickName','$event',[]],['form']]]]]}}" value="{{form.nickName}}" bindchange="__e" bindinput="__e"/></u-form-item></view><view class="card"><u-form-item vue-id="{{('96ebc1fe-9')+','+('96ebc1fe-7')}}" label="手机" prop="phonenumber" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('96ebc1fe-10')+','+('96ebc1fe-9')}}" disabledColor="#ffffff" border="none" placeholder="请输入您的手机号码" value="{{form.phonenumber}}" data-event-opts="{{[['^input',[['__set_model',['$0','phonenumber','$event',[]],['form']]]]]}}" bind:__l="__l"></u--input></u-form-item></view><view class="card"><u-form-item vue-id="{{('96ebc1fe-11')+','+('96ebc1fe-7')}}" label="性别" prop="sex" bind:__l="__l" vue-slots="{{['default']}}"><u-radio-group bind:input="__e" vue-id="{{('96ebc1fe-12')+','+('96ebc1fe-11')}}" activeColor="#000" value="{{form.sex}}" data-event-opts="{{[['^input',[['__set_model',['$0','sex','$event',[]],['form']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{sexList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('96ebc1fe-13-'+index)+','+('96ebc1fe-12')}}" customStyle="{{({marginLeft:'10px'})}}" label="{{item.dictLabel}}" name="{{item.dictValue}}" bind:__l="__l"></u-radio></block></u-radio-group></u-form-item></view></u--form></view><view><u-button vue-id="{{('96ebc1fe-14')+','+('96ebc1fe-1')}}" type="primary" loading="{{btnLoading}}" size="large" color="#FFDB7F" customStyle="{{btnStyle}}" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">确认</u-button></view></view><u-datetime-picker vue-id="{{('96ebc1fe-15')+','+('96ebc1fe-1')}}" show="{{showBirthday}}" value="{{birthdate}}" maxDate="{{birthdate}}" minDate="{{minDate}}" mode="date" closeOnClickOverlay="{{true}}" data-event-opts="{{[['^confirm',[['birthdayConfirm']]],['^cancel',[['birthdayClose']]],['^close',[['birthdayClose']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:close="__e" bind:__l="__l"></u-datetime-picker></view></theme-wrap>