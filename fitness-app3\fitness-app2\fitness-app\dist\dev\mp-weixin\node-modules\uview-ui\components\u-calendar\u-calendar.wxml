<u-popup vue-id="5f1b9f66-1" show="{{show}}" mode="bottom" closeable="{{true}}" round="{{round}}" closeOnClickOverlay="{{closeOnClickOverlay}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-59e686e7" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-calendar data-v-59e686e7"><u-header vue-id="{{('5f1b9f66-2')+','+('5f1b9f66-1')}}" title="{{title}}" subtitle="{{subtitle}}" showSubtitle="{{showSubtitle}}" showTitle="{{showTitle}}" class="data-v-59e686e7" bind:__l="__l"></u-header><scroll-view style="{{'height:'+($root.g0)+';'}}" scroll-y="{{true}}" scroll-top="{{scrollTop}}" scrollIntoView="{{scrollIntoView}}" data-event-opts="{{[['scroll',[['onScroll',['$event']]]]]}}" bindscroll="__e" class="data-v-59e686e7"><u-month vue-id="{{('5f1b9f66-3')+','+('5f1b9f66-1')}}" color="{{color}}" rowHeight="{{rowHeight}}" showMark="{{showMark}}" months="{{months}}" mode="{{mode}}" maxCount="{{maxCount}}" startText="{{startText}}" endText="{{endText}}" defaultDate="{{defaultDate}}" minDate="{{innerMinDate}}" maxDate="{{innerMaxDate}}" maxMonth="{{monthNum}}" readonly="{{readonly}}" maxRange="{{maxRange}}" rangePrompt="{{rangePrompt}}" showRangePrompt="{{showRangePrompt}}" allowSameDay="{{allowSameDay}}" data-ref="month" data-event-opts="{{[['^monthSelected',[['monthSelected']]],['^updateMonthTop',[['updateMonthTop']]]]}}" bind:monthSelected="__e" bind:updateMonthTop="__e" class="data-v-59e686e7 vue-ref" bind:__l="__l"></u-month></scroll-view><block wx:if="{{showConfirm}}"><block wx:if="{{$slots.footer}}"><slot name="footer"></slot></block><block wx:else><view class="u-calendar__confirm data-v-59e686e7"><u-button vue-id="{{('5f1b9f66-4')+','+('5f1b9f66-1')}}" shape="circle" text="{{buttonDisabled?confirmDisabledText:confirmText}}" color="{{color}}" disabled="{{buttonDisabled}}" data-event-opts="{{[['^click',[['confirm']]]]}}" bind:click="__e" class="data-v-59e686e7" bind:__l="__l"></u-button></view></block></block></view></u-popup>