"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u--form/u--form"],{26763:function(e,t,n){var u;n.r(t),n.d(t,{__esModule:function(){return s.__esModule},default:function(){return f}});var o,i=function(){var e=this,t=e.$createElement;e._self._c},r=[],s=n(34809),a=s["default"],l=n(18535),c=(0,l["default"])(a,i,r,!1,null,null,null,!1,u,o),f=c.exports},34809:function(e,t,n){var u=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var o=i(n(28300));function i(e){return e&&e.__esModule?e:{default:e}}var r=function(){Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-form/u-form")]).then(function(){return resolve(n(9506))}.bind(null,n))["catch"](n.oe)};t["default"]={name:"u-form",mixins:[u.$u.mpMixin,o.default,u.$u.mixin],components:{uvForm:r},created:function(){this.children=[]},methods:{setRules:function(e){this.$refs.uForm.setRules(e)},validate:function(){return this.setMpData(),this.$refs.uForm.validate()},validateField:function(e,t,n){return this.setMpData(),this.$refs.uForm.validateField(e,t,n)},resetFields:function(){return this.setMpData(),this.$refs.uForm.resetFields()},clearValidate:function(e){return this.setMpData(),this.$refs.uForm.clearValidate(e)},setMpData:function(){this.$refs.uForm.children=this.children}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u--form/u--form-create-component"],{},function(e){e("81715")["createComponent"](e(26763))}]);