<theme-wrap scoped-slots-compiler="augmented" vue-id="3861e38c-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('3861e38c-2')+','+('3861e38c-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="训练建议" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40"><picker class="u-m-b-40" mode="selector" range="{{changGuanList}}" range-key="label" value="{{currentIndex}}" data-event-opts="{{[['change',[['changeChangGuan',['$event']]]]]}}" bindchange="__e"><view class="w-100 u-flex u-row-between u-col-center"><view class="{{['u-font-36','font-bold','u-line-1',(!currentChangGuan)?'u-tips-color':'']}}">{{currentChangGuan||"请选择场馆"}}</view><u-icon vue-id="{{('3861e38c-3')+','+('3861e38c-1')}}" name="arrow-right" size="21" color="#999999" bind:__l="__l"></u-icon></view></picker><view class="u-p-t-40 u-border-top"><view><block wx:if="{{$root.g0}}"><view class="u-tips-color u-flex u-row-end font-bold u-font-24 u-m-b-30">共<text class="u-m-l-10 u-m-r-10 u-font-30" style="color:#000;">{{amount}}</text>记录</view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-m-b-30 item-blk u-flex u-row-satrt u-col-start"><view class="u-flex-1 u-p-r-20"><image class="w-100 flex-0" style="height:60rpx;border-radius:50%;" src="{{item.avatar}}" mode="widthFix"></image><view class="u-m-t-10 u-text-center u-font-28 font-bold u-line-1">{{''+item.nickname+''}}</view><view class="u-m-t-10 u-tips-color u-font-26">{{''+item.created_at+''}}</view></view><view class="u-p-20 border-16 u-m-t-40 bg-fff u-flex-3 u-relative"><view class="w-100 flex-0 u-relative u-border-bottom u-m-b-20 u-p-b-20 u-flex u-row-between"><view class="u-font-32 font-bold overflow-hidden u-line-1 u-p-r-10" style="max-width:65%;">{{item.courseName+item.courseName+item.courseName}}</view><view class="u-tips-color u-font-28 u-text-end overflow-hidden u-line-1" style="max-width:35%;">{{item.courseTypeText+item.courseTypeText+item.courseTypeText}}</view></view><view><u-read-more vue-id="{{('3861e38c-4-'+index)+','+('3861e38c-1')}}" showHeight="200" toggle="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-font-28">{{item.content}}</view></u-read-more></view><view class="u-absolute" style="left:-17rpx;top:16rpx;"><u-icon vue-id="{{('3861e38c-5-'+index)+','+('3861e38c-1')}}" name="arrow-down-fill" size="18" color="#fff" bind:__l="__l"></u-icon></view></view></view></block></block><block wx:else><block wx:if="{{$root.g1}}"><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/history.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无记录</view></view></block></block><view hidden="{{!(loading)}}"></view></view></view></view></view></theme-wrap>