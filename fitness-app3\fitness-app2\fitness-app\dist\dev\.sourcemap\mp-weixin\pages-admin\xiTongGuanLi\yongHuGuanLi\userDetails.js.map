{"version": 3, "file": "pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC+FA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,YAAA;MACAC,MAAA;MACAC,IAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;QACAC,EAAA;QACAC,GAAA;QACAC,GAAA;QACAC,eAAA;QACAC,UAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,GAAA;MACA;MACAC,KAAA;MACAC,QAAA;QACAC,YAAA;QACAZ,MAAA;QACAa,KAAA;QACAC,QAAA;QACAC,UAAA;MACA;IACA;EACA;EACAC,MAAA,WAAAA,OAAAC,GAAA;IAAA,IAAAC,UAAA;IACA,IAAAD,GAAA,CAAAE,IAAA;MACA,KAAArB,IAAA,GAAAsB,IAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAE,IAAA;IACA;IACAG,OAAA,CAAAC,GAAA,MAAAzB,IAAA;IACA,IAAA0B,GAAA,KAAAN,UAAA,QAAApB,IAAA,cAAAoB,UAAA,uBAAAA,UAAA,CAAAM,GAAA;IACA,KAAA3B,MAAA,oBAAA2B,GAAA;IACA;IACA,KAAAC,gBAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACAJ,OAAA,CAAAC,GAAA,MAAAI,KAAA,CAAAC,KAAA;IACA,KAAAD,KAAA,CAAAC,KAAA,CAAAC,QAAA,MAAAnB,KAAA;EACA;EACAoB,OAAA;IACAL,gBAAA,WAAAA,iBAAA;MAAA,IAAAM,KAAA;MACAC,YAAA,CAAAC,qBAAA;QACAC,QAAA,OAAApC,IAAA,CAAAoC;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAd,OAAA,CAAAC,GAAA,CAAAa,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAAjC,IAAA,GAAAsC,GAAA,CAAA3C,IAAA;QACA;MACA;IACA;IACA;IACA6C,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACAC,GAAA,CAAAC,WAAA;QACAC,IAAA;MACA;MACA,KAAA/C,UAAA;MACAqC,YAAA,CAAAW,qBAAA;QACAlD,IAAA,OAAAK,IAAA;QACA8C,MAAA;MACA,GACAT,IAAA,WAAAC,GAAA;QACAd,OAAA,CAAAC,GAAA,CAAAa,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAG,GAAA,CAAAK,WAAA;UACAL,GAAA,CAAAM,YAAA;QACA;UACAN,GAAA,CAAAK,WAAA;UACAN,MAAA,CAAA5C,UAAA;UACA4C,MAAA,CAAAZ,KAAA,CAAAoB,OAAA,CAAAC,IAAA;YACAC,OAAA;YACAC,IAAA;YACAC,QAAA;UACA;QACA;MACA,GACAC,KAAA,WAAAC,GAAA;QACAb,GAAA,CAAAK,WAAA;QACAN,MAAA,CAAA5C,UAAA;QACA4C,MAAA,CAAAZ,KAAA,CAAAoB,OAAA,CAAAC,IAAA;UACAC,OAAA;UACAC,IAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;AACA;;;;;;;;;;AC1SA;;;;;;;;;;;;;;;ACAA9D,mBAAA;AAGA,IAAAiE,IAAA,GAAAlE,sBAAA,CAAAC,mBAAA;AACA,IAAAkE,YAAA,GAAAnE,sBAAA,CAAAC,mBAAA;AAA0E,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH1E;AACAkE,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLwG;AACxH;AACA,CAA+D;AACL;AAC1D,CAAwE;;;AAGxE;AACsI;AACtI,gBAAgB,4IAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBugB,CAAC,+DAAe,+dAAG,EAAC;;;;;;;;;;;;;;;;;ACAqd,CAAC,+DAAe,u4BAAG,EAAC", "sources": ["webpack:///./src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue?1ac9", "uni-app:///src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue", "webpack:///./src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue?f80c", "uni-app:///src/main.js", "webpack:///./src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue?4ce2", "webpack:///./src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue?e548", "webpack:///./src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue?555a", "webpack:///./src/pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue?f42e"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uNoNetwork: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-no-network/u-no-network\" */ \"uview-ui/components/u-no-network/u-no-network.vue\"\n      )\n    },\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uNotify: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notify/u-notify\" */ \"uview-ui/components/u-notify/u-notify.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form/u-form\" */ \"uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form-item/u-form-item\" */ \"uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"a51610c2-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"a51610c2-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"a51610c2-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"a51610c2-1\", \"content\") : null\n  var m3 = m0 ? _vm.$getSSP(\"a51610c2-1\", \"content\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <themeWrap>\n    <template #content=\"{ navBarColor, navBarTextColor, buttonLightBgColor }\">\n      <view class=\"page\">\n        <!-- 顶部菜单栏 -->\n        <u-navbar\n          title=\"体征信息\"\n          @rightClick=\"uni.navigateBack()\"\n          :titleStyle=\"{ color: navBarTextColor }\"\n          :bgColor=\"navBarColor\"\n          :leftIconColor=\"navBarTextColor\"\n          :autoBack=\"true\"\n          :placeholder=\"true\"\n          :safeAreaInsetTop=\"true\"\n        >\n        </u-navbar>\n        <u-no-network></u-no-network>\n        <u-toast ref=\"uToast\" />\n        <u-notify ref=\"uNotify\" />\n        <view class=\"content container\">\n          <view class=\"bg-fff border-16 u-p-t-20 u-p-b-20 u-p-r-30 u-p-l-30\">\n            <u-form\n              labelPosition=\"left\"\n              :model=\"form\"\n              ref=\"uForm\"\n              labelWidth=\"140\"\n              errorType=\"toast\"\n              class=\"u-p-20\"\n            >\n              <u-form-item borderBottom label=\"性别\" prop=\"sex\">\n                <view class=\"u-text-right\">\n                  {{ gender }}\n                </view>\n              </u-form-item>\n              <u-form-item borderBottom label=\"年龄\" prop=\"age\">\n                <u--input\n                  v-model=\"form.age\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  type=\"number\"\n                  placeholder=\"请输入您的年龄\"\n                ></u--input>\n              </u-form-item>\n              <u-form-item borderBottom label=\"身高(cm)\" prop=\"height\">\n                <u--input\n                  v-model=\"form.height\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  type=\"digit\"\n                  placeholder=\"请输入您的身高\"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item borderBottom label=\"体重(kg)\" prop=\"weight\">\n                <u--input\n                  v-model=\"form.weight\"\n                  disabledColor=\"#ffffff\"\n                  type=\"digit\"\n                  border=\"none\"\n                  placeholder=\"请输入您的体重\"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item borderBottom label=\"体脂率(%)\" prop=\"bf\">\n                <u--input\n                  readonly\n                  v-model=\"form.bf\"\n                  type=\"digit\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\" \"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item borderBottom label=\"BMI\" prop=\"BMI\">\n                <u--input\n                  readonly\n                  v-model=\"form.BMI\"\n                  type=\"digit\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\" \"\n                ></u--input>\n              </u-form-item>\n\n              <!-- <u-form-item borderBottom label=\"基础代谢\" prop=\"phonenumber\">\n                <u--input\n                  v-model=\"form.phonenumber\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\"请输入您的基础代谢\"\n                ></u--input>\n              </u-form-item> -->\n\n              <u-form-item borderBottom label=\"肌肉量(kg)\" prop=\"lbm\">\n                <u--input\n                  v-model=\"form.lbm\"\n                  disabledColor=\"#ffffff\"\n                  type=\"digit\"\n                  border=\"none\"\n                  placeholder=\"请输入您的肌肉量\"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item borderBottom label=\"腰臀比\" prop=\"waistToHipRatio\">\n                <u--input\n                  v-model=\"form.waistToHipRatio\"\n                  disabledColor=\"#ffffff\"\n                  type=\"digit\"\n                  border=\"none\"\n                  placeholder=\"请输入您的腰臀比\"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item borderBottom label=\"胸围(cm)\" prop=\"chestGirth\">\n                <u--input\n                  v-model=\"form.chestGirth\"\n                  type=\"digit\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\"请输入您的胸围\"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item\n                borderBottom\n                label=\"腰围(cm)\"\n                prop=\"waistCircumference\"\n              >\n                <u--input\n                  v-model=\"form.waistCircumference\"\n                  disabledColor=\"#ffffff\"\n                  type=\"digit\"\n                  border=\"none\"\n                  placeholder=\"请输入您的腰围\"\n                ></u--input>\n              </u-form-item>\n\n              <u-form-item\n                borderBottom\n                label=\"臀围(cm)\"\n                prop=\"hipCircumference\"\n              >\n                <u--input\n                  v-model=\"form.hipCircumference\"\n                  type=\"digit\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\"请输入您的臀围\"\n                ></u--input>\n              </u-form-item>\n\n              <!-- <u-form-item borderBottom label=\"脂肪量(kg)\" prop=\"phonenumber\">\n                <u--input\n                  v-model=\"form.phonenumber\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\"请输入您的脂肪量\"\n                ></u--input>\n              </u-form-item> -->\n\n              <u-form-item label=\"骨骼肌含量(kg)\" prop=\"smm\">\n                <u--input\n                  v-model=\"form.smm\"\n                  type=\"digit\"\n                  disabledColor=\"#ffffff\"\n                  border=\"none\"\n                  placeholder=\"请输入您的骨骼肌含量\"\n                ></u--input>\n              </u-form-item>\n            </u-form>\n          </view>\n          <view class=\"u-p-t-50 u-p-b-40\">\n            <u-button\n              @click=\"submit\"\n              :loading=\"btnLoading\"\n              size=\"large\"\n              :color=\"buttonLightBgColor\"\n              :customStyle=\"btnStyle\"\n              >确认</u-button\n            >\n          </view>\n        </view>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\nimport api from \"@/common/api\";\nexport default {\n  data() {\n    return {\n      loading: true,\n      btnLoading: false,\n      showBirthday: false,\n      gender: '男',\n      form: {\n        age: \"\",\n        height: \"\",\n        weight: \"\",\n        bf: \"\",\n        BMI: \"\",\n        lbm: \"\",\n        waistToHipRatio: \"\",\n        chestGirth: \"\",\n        waistCircumference: \"\",\n        hipCircumference: \"\",\n        smm: \"\",\n      },\n      rules: [],\n      btnStyle: {\n        borderRadius: \"16rpx\",\n        height: \"94rpx\",\n        color: \"#fff\",\n        fontSize: \"32rpx\",\n        fontWeight: \"bold\",\n      },\n    };\n  },\n  onLoad(obj) {\n    if (obj.list) {\n      this.form = JSON.parse(obj.list);\n    }\n    console.log(this.form);\n    const sex = this.form?.sex || 0\n    this.gender = ['男', '女', '未知'][sex]\n    // 获取用户指标\n    this.getUserIndicator();\n    // api.getDataType({\n    //   data: {\n    //     companyId: 1,\n    //   },\n    //   dictType: 'sys_user_sex',\n    //   method: 'GET',\n    // }).then((ret) => {\n    //   console.log(ret);\n    //   if (ret.code == 200) {\n    //     this.sexList = ret.data\n    //   }\n    //   this.$nextTick(() => {\n    //     this.loadData();\n    //   })\n    // }).catch(() => {\n    //   this.loadData();\n    // })\n  },\n  onReady() {\n    console.log( this.$refs.uForm);\n    this.$refs.uForm.setRules(this.rules);\n  },\n  methods: {\n    getUserIndicator() {\n      api.getOtherUserIndicator({\n          memberId: this.form.memberId,\n        }).then((res) => {\n        console.log(res);\n        if (res.code == 200) {\n          this.form = res.data\n        }\n      });\n    },\n    //修改信息\n    submit() {\n      uni.showLoading({\n        mask: true,\n      });\n      this.btnLoading = true;\n      api.putOtherUserIndicator({\n          data: this.form,\n          method: \"PUT\",\n        })\n        .then((res) => {\n          console.log(res);\n          if(res.code == 200){\n            uni.hideLoading();\n            uni.navigateBack()\n          }else{\n            uni.hideLoading();\n            this.btnLoading = false;\n            this.$refs.uNotify.show({\n              message: \"修改失败!\",\n              type: \"error\",\n              duration: \"1500\",\n            });\n          }\n        })\n        .catch((err) => {\n          uni.hideLoading();\n          this.btnLoading = false;\n          this.$refs.uNotify.show({\n            message: \"修改失败!\",\n            type: \"error\",\n            duration: \"1500\",\n          });\n        });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.content {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  padding-top: 80upx;\n  padding-bottom: 60upx;\n}\n\n.bg-000 {\n  background-color: #6f6f6f;\n  border-radius: 16upx;\n}\n\n.card {\n  background-color: #fff;\n  border-radius: 24upx;\n  padding: 8upx 36upx;\n  margin-bottom: 20upx;\n}\n\n.avatar-wrap {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 30upx;\n  position: relative;\n\n  .uploading {\n    position: absolute;\n    right: 20upx;\n    top: 20upx;\n  }\n\n  .avatar {\n    width: 144upx;\n    height: 144upx;\n    line-height: 0;\n    position: relative;\n  }\n}\n\n.edit-icon {\n  display: inline-block;\n  right: 0;\n  bottom: 0;\n  background-color: #3f3f3f;\n  border-radius: 8upx;\n  position: absolute !important;\n}\n\n.u-radio-group {\n  flex: 0 !important;\n  justify-content: flex-end;\n}\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userDetails.vue?vue&type=template&id=89745b72&\"\nvar renderjs\nimport script from \"./userDetails.vue?vue&type=script&lang=js&\"\nexport * from \"./userDetails.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userDetails.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userDetails.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userDetails.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userDetails.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userDetails.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userDetails.vue?vue&type=template&id=89745b72&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "data", "loading", "btnLoading", "showBirthday", "gender", "form", "age", "height", "weight", "bf", "BMI", "lbm", "waistToHipRatio", "chestGirth", "waistCircumference", "hipCircumference", "smm", "rules", "btnStyle", "borderRadius", "color", "fontSize", "fontWeight", "onLoad", "obj", "_this$form", "list", "JSON", "parse", "console", "log", "sex", "getUserIndicator", "onReady", "$refs", "uForm", "setRules", "methods", "_this", "api", "getOtherUserIndicator", "memberId", "then", "res", "code", "submit", "_this2", "uni", "showLoading", "mask", "putOtherUserIndicator", "method", "hideLoading", "navigateBack", "uNotify", "show", "message", "type", "duration", "catch", "err", "_vue", "_userDetails", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}