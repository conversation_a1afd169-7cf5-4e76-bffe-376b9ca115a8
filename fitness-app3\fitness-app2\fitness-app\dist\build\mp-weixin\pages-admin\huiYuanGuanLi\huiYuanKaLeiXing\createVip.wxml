<theme-wrap scoped-slots-compiler="augmented" vue-id="162e5acc-1" class="data-v-4cf6cdae" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-4cf6cdae" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('162e5acc-2')+','+('162e5acc-1')}}" title="会员预约" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-4cf6cdae" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-4cf6cdae"><view class="formView data-v-4cf6cdae"><view data-event-opts="{{[['tap',[['choseUser',['$event']]]]]}}" class="formList data-v-4cf6cdae" bindtap="__e"><view class="data-v-4cf6cdae">选择会员:</view><u--input bind:input="__e" vue-id="{{('162e5acc-3')+','+('162e5acc-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择会员" value="{{formList.nickName}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickName','$event',[]],['formList']]]]]}}" class="data-v-4cf6cdae" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="formList data-v-4cf6cdae" bindtap="__e"><view class="data-v-4cf6cdae">选择会员卡:</view><u--input bind:input="__e" vue-id="{{('162e5acc-4')+','+('162e5acc-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择会员卡" value="{{formList.cardName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardName','$event',[]],['formList']]]]]}}" class="data-v-4cf6cdae" bind:__l="__l"></u--input></view></view></view><u-picker vue-id="{{('162e5acc-5')+','+('162e5acc-1')}}" show="{{showType}}" columns="{{actions}}" keyName="cardName" data-event-opts="{{[['^confirm',[['typeSelect']]],['^cancel',[['e2']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-4cf6cdae" bind:__l="__l"></u-picker><u-picker vue-id="{{('162e5acc-6')+','+('162e5acc-1')}}" show="{{showLX}}" columns="{{actionsLX}}" keyName="courseTypeName" data-event-opts="{{[['^confirm',[['courseSelect']]],['^cancel',[['e3']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-4cf6cdae" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-4cf6cdae"><view data-event-opts="{{[['tap',[['submit']]]]}}" class="confirmBtn data-v-4cf6cdae" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">添加会员卡</view></view></view></theme-wrap>