<view class="border-10 calendar-wrap u-p-t-20 u-p-b-20 u-p-l-30 bg-fff u-p-r-30"><view class="u-p-b-20 u-flex u-row-between u-border-bottom w-100 u-col-center"><view data-event-opts="{{[['tap',[['openCalendar',['$event']]]]]}}" class="u-flex-1" bindtap="__e"><u-icon class="u-m-r-10" vue-id="530f20a6-1" name="calendar" size="30" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['openCalendar',['$event']]]]]}}" class="u-flex-1 u-text-center" bindtap="__e">{{''+now+''}}</view><block wx:if="{{showFilter}}"><view class="u-flex-1 u-flex u-row-end"><u-icon vue-id="530f20a6-2" name="list-dot" size="28" data-event-opts="{{[['^click',[['handleFilter']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></view></block><block wx:else><view class="u-flex-1 u-flex u-row-end"></view></block></view><view class="u-flex u-row-between u-col-center u-p-t-20"><block wx:for="{{currentWeek}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeActive',[index]]]]]}}" bindtap="__e"><view class="font-bold u-font-28">{{currentTime==i.fulldate?"今日":i.dayText}}</view><view class="{{['date-wrap',(currentTime==i.fulldate)?'current':'',(activeDate==i.fulldate)?'active':'']}}">{{i.date}}</view></view></block></view><uni-calendar class="vue-ref" vue-id="530f20a6-3" insert="{{false}}" date="{{activeDate}}" maskClose="{{true}}" data-ref="uniCalendar" data-event-opts="{{[['^confirm',[['confirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-calendar></view>