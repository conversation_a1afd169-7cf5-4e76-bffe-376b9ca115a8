(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-checkbox-group/u-checkbox-group"],{26371:function(e,t,n){"use strict";var i;n.r(t),n.d(t,{__esModule:function(){return a.__esModule},default:function(){return f}});var u,o=function(){var e=this,t=e.$createElement;e._self._c},c=[],a=n(66361),s=a["default"],r=n(45729),l=n.n(r),h=(l(),n(18535)),d=(0,h["default"])(s,o,c,!1,null,"5907e1fa",null,!1,i,u),f=d.exports},45729:function(){},66361:function(e,t,n){"use strict";var i=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var u=o(n(20785));function o(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-checkbox-group",mixins:[i.$u.mpMixin,i.$u.mixin,u.default],computed:{parentData:function(){return[this.value,this.disabled,this.inactiveColor,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.borderBottom,this.placement]},bemClass:function(){return this.bem("checkbox-group",["placement"])}},watch:{parentData:function(){this.children.length&&this.children.map((function(e){"function"===typeof e.init&&e.init()}))}},data:function(){return{}},created:function(){this.children=[]},methods:{unCheckedOther:function(e){var t=[];this.children.map((function(e){e.isChecked&&t.push(e.name)})),this.$emit("change",t),this.$emit("input",t)}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-checkbox-group/u-checkbox-group-create-component"],{},function(e){e("81715")["createComponent"](e(26371))}]);