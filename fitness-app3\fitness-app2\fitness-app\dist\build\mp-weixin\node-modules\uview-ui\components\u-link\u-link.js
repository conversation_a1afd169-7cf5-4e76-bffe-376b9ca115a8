(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-link/u-link"],{41378:function(e,t,n){"use strict";var i;n.r(t),n.d(t,{__esModule:function(){return s.__esModule},default:function(){return p}});var u,o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__get_style([e.linkStyle,e.$u.addStyle(e.customStyle)]));e.$mp.data=Object.assign({},{$root:{s0:n}})},l=[],s=n(55883),a=s["default"],c=n(81264),d=n.n(c),r=(d(),n(18535)),f=(0,r["default"])(a,o,l,!1,null,"050d9a99",null,!1,i,u),p=f.exports},55883:function(e,t,n){"use strict";var i=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var u=o(n(70378));function o(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-link",mixins:[i.$u.mpMixin,i.$u.mixin,u.default],computed:{linkStyle:function(){var e={color:this.color,fontSize:i.$u.addUnit(this.fontSize),lineHeight:i.$u.addUnit(i.$u.getPx(this.fontSize)+2),textDecoration:this.underLine?"underline":"none"};return e}},methods:{openLink:function(){var e=this;i.setClipboardData({data:this.href,success:function(){i.hideToast(),e.$nextTick((function(){i.$u.toast(e.mpTips)}))}}),this.$emit("click")}}}},81264:function(){}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-link/u-link-create-component"],{},function(e){e("81715")["createComponent"](e(41378))}]);