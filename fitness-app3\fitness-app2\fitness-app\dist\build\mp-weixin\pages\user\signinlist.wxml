<theme-wrap scoped-slots-compiler="augmented" vue-id="65a625cb-1" class="data-v-241e61ed" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-241e61ed"><u-navbar vue-id="{{('65a625cb-2')+','+('65a625cb-1')}}" title="签到列表" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-241e61ed" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-241e61ed"><view data-event-opts="{{[['tap',[['opensear',['$event']]]]]}}" class="search data-v-241e61ed" bindtap="__e"><u-icon vue-id="{{('65a625cb-3')+','+('65a625cb-1')}}" name="search" color="#000000" size="25" class="data-v-241e61ed" bind:__l="__l"></u-icon></view><block wx:if="{{$root.g0}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-241e61ed"><view class="u-flex u-col-center u-row-start data-v-241e61ed" style="flex-wrap:no-wrap;overflow:hidden;"><view class="w-100 u-p-l-20 data-v-241e61ed"><view class="u-line-1 w-100 data-v-241e61ed">{{''+item.nickName+''}}</view><view class="u-tips-color u-font-26 data-v-241e61ed" style="margin-top:30rpx;">{{'时间：'+item.signInTime+''}}</view><view class="u-tips-color u-font-26 data-v-241e61ed" style="margin-top:10rpx;">{{'类型：'+(item.type==1?'每日':'团课')+''}}</view></view></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-241e61ed"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-241e61ed"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-241e61ed">暂无活动</view></view></block></view><u-popup vue-id="{{('65a625cb-4')+','+('65a625cb-1')}}" show="{{modelshow}}" mode="top" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-241e61ed" bind:__l="__l" vue-slots="{{['default']}}"><view class="container u-p-t-40 data-v-241e61ed"><u-form vue-id="{{('65a625cb-5')+','+('65a625cb-4')}}" model="{{form}}" labelWidth="140" data-ref="uForm" class="data-v-241e61ed vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-241e61ed"><u-form-item vue-id="{{('65a625cb-6')+','+('65a625cb-5')}}" required="{{true}}" borderBottom="{{true}}" prop="memberId" label="会员" class="data-v-241e61ed" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-241e61ed" style="{{'color:'+(form.memberId?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.memberName||'请选择会员')+''}}</view><u-picker vue-id="{{('65a625cb-7')+','+('65a625cb-6')}}" show="{{memberId}}" columns="{{memberIdcolumns}}" keyName="nickName" value="{{form.memberId}}" data-event-opts="{{[['^cancel',[['e3']]],['^confirm',[['confirmmemberId']]],['^input',[['__set_model',['$0','memberId','$event',[]],['form']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-241e61ed" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('65a625cb-8')+','+('65a625cb-5')}}" required="{{true}}" borderBottom="{{true}}" prop="memberId" label="类型" class="data-v-241e61ed" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-241e61ed" style="{{'color:'+(form.type?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.typename||'请选择状态')+''}}</view><u-picker vue-id="{{('65a625cb-9')+','+('65a625cb-8')}}" show="{{type}}" columns="{{columns}}" keyName="label" value="{{form.type}}" data-event-opts="{{[['^cancel',[['e5']]],['^confirm',[['confirmCoach']]],['^input',[['__set_model',['$0','type','$event',[]],['form']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-241e61ed" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('65a625cb-10')+','+('65a625cb-5')}}" required="{{true}}" borderBottom="{{true}}" prop="ic" label="时间" class="data-v-241e61ed" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-241e61ed" style="{{'color:'+(form.time?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.times||'请选择时间')+''}}</view><u-datetime-picker vue-id="{{('65a625cb-11')+','+('65a625cb-10')}}" mode="date" show="{{time}}" closeOnClickOverlay="{{true}}" value="{{form.time}}" data-event-opts="{{[['^close',[['e7']]],['^cancel',[['e8']]],['^confirm',[['endTimecof']]],['^input',[['__set_model',['$0','time','$event',[]],['form']]]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-241e61ed" bind:__l="__l"></u-datetime-picker></u-form-item></view></u-form></view><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="serbtn data-v-241e61ed" bindtap="__e">搜索</view></u-popup></view></theme-wrap>