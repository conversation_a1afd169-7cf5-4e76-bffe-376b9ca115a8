{"version": 3, "file": "pages-admin/huodong<PERSON>uan<PERSON>/huodongindex.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+VAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACyGA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAEA;EACAC,UAAA;IACAC,cAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAEA;MACAC,UAAA;MACAC,SAAA;MACAC,sBAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAV,IAAA;MACAW,WAAA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,QAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACAC,IAAA;MACAC,gBAAA;MACAC,cAAA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAAC,OAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAhC,mBAAA,GAAAiC,IAAA,UAAAC,QAAA;MAAA,IAAAC,UAAA,EAAAhC,IAAA,EAAAiC,mBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,WAAA,EAAAC,IAAA,EAAAC,KAAA,EAAAC,GAAA,EAAAC,aAAA,EAAAC,GAAA,EAAArB,cAAA,EAAAE,iBAAA;MAAA,OAAA5B,mBAAA,GAAAgD,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAC,YAAA;cACAlD,IAAA;gBACAmD,EAAA,EAAAxB,OAAA,CAAAwB;cACA;YACA;UAAA;YAJAnB,UAAA,GAAAe,QAAA,CAAAK,IAAA;YAKAxB,KAAA,CAAA5B,IAAA,GAAAgC,UAAA,CAAAhC,IAAA;YACA4B,KAAA,CAAAP,IAAA,GAAAO,KAAA,CAAA5B,IAAA,CAAAqD,OAAA,CAAAC,KAAA;YACAtD,IAAA;YACA,IAAA2B,OAAA,CAAA4B,QAAA;cACAvD,IAAA;gBACAwD,UAAA,EAAA5B,KAAA,CAAA5B,IAAA,CAAAmD,EAAA;gBACA;gBACAM,YAAA,EAAA9B,OAAA,CAAA4B;cACA;YACA;cACAvD,IAAA;gBACAwD,UAAA,EAAA5B,KAAA,CAAA5B,IAAA,CAAAmD,EAAA;gBACA;cACA;YACA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAC,YAAA;cACAlD,IAAA,EAAAA;YACA;UAAA;YAFAiC,mBAAA,GAAAc,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAGAC,YAAA;cACAlD,IAAA;gBACAwD,UAAA,EAAA5B,KAAA,CAAA5B,IAAA,CAAAmD;cACA;YACA;UAAA;YAJAjB,eAAA,GAAAa,QAAA,CAAAK,IAAA;YAKAxB,KAAA,CAAAvB,kBAAA,GAAA6B,eAAA,CAAAlC,IAAA,CAAA0D,MAAA;YACA9B,KAAA,CAAAxB,YAAA,GAAAwB,KAAA,CAAA+B,UAAA,CAAAzB,eAAA,CAAAlC,IAAA;YAAA+C,QAAA,CAAAE,IAAA;YAAA,OACAC,YAAA;cACAlD,IAAA;gBACAwD,UAAA,EAAA5B,KAAA,CAAA5B,IAAA,CAAAmD,EAAA;gBACAS,GAAA;cACA;YACA;UAAA;YALAzB,eAAA,GAAAY,QAAA,CAAAK,IAAA;YAMAhB,mBAAA;YACA,IAAAD,eAAA,CAAAnC,IAAA,CAAA0D,MAAA;cACAtB,mBAAA,GAAAD,eAAA,CAAAnC,IAAA,CACA6D,IAAA;gBAAA,OAAAC,IAAA,CAAAC,MAAA;cAAA;cAAA,CACAC,KAAA,IAAAF,IAAA,CAAAG,KAAA,CAAA9B,eAAA,CAAAnC,IAAA,CAAA0D,MAAA;YACA;cACAtB,mBAAA,GAAAD,eAAA,CAAAnC,IAAA;YACA;YACA4B,KAAA,CAAAN,gBAAA,GAAAM,KAAA,CAAA+B,UAAA,CAAAvB,mBAAA;YAAAW,QAAA,CAAAE,IAAA;YAAA,OACAC,YAAA;cACAlD,IAAA;gBACAwD,UAAA,EAAA5B,KAAA,CAAA5B,IAAA,CAAAmD;cACA;YACA;UAAA;YAJAd,qBAAA,GAAAU,QAAA,CAAAK,IAAA;YAKAxB,KAAA,CAAAzB,sBAAA,GAAAkC,qBAAA,CAAArC,IAAA;YACA4B,KAAA,CAAAZ,QAAA;cACAC,IAAA,EAAAW,KAAA,CAAAsC,oBAAA,CAAAtC,KAAA,CAAA5B,IAAA,CAAAmE,SAAA,EAAAvC,KAAA,CAAA5B,IAAA,CAAAoE,OAAA;cACAlD,KAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAkB,KAAA,OAAA+B,IAAA,CAAAzC,KAAA,CAAA5B,IAAA,CAAAmE,SAAA;YACA5B,WAAA,OAAA8B,IAAA;YACA7B,IAAA,GAAAD,WAAA,CAAA+B,WAAA;YACA7B,KAAA,GAAAF,WAAA,CAAAgC,QAAA;YACA7B,GAAA,GAAAH,WAAA,CAAAiC,OAAA;YACA/B,KAAA,GAAAA,KAAA,cAAAA,KAAA,GAAAA,KAAA;YACAC,GAAA,GAAAA,GAAA,cAAAA,GAAA,GAAAA,GAAA;YACAC,aAAA,MAAA8B,MAAA,CAAAjC,IAAA,OAAAiC,MAAA,CAAAhC,KAAA,OAAAgC,MAAA,CAAA/B,GAAA;YACAE,GAAA,OAAAyB,IAAA,CAAA1B,aAAA;YACApB,cAAA,GAAAqB,GAAA,CAAA8B,OAAA,KAAApC,KAAA,CAAAoC,OAAA;YACA9C,KAAA,CAAAL,cAAA,GAAAA,cAAA;YACAK,KAAA,CAAAJ,UAAA,GAAAmD,GAAA,CAAAC,cAAA;YAAA7B,QAAA,CAAAE,IAAA;YAAA,OAEAC,YAAA;cACAlD,IAAA;gBACAwD,UAAA,EAAA5B,KAAA,CAAA5B,IAAA,CAAAmD;cACA;YACA;UAAA;YAJA1B,iBAAA,GAAAsB,QAAA,CAAAK,IAAA;YAKAxB,KAAA,CAAAH,iBAAA,GAAAA,iBAAA,CAAAzB,IAAA;YACA6E,OAAA,CAAAC,GAAA,CAAArD,iBAAA;UAAA;UAAA;YAAA,OAAAsB,QAAA,CAAAgC,IAAA;QAAA;MAAA,GAAAhD,OAAA;IAAA;EACA;EACAiD,MAAA,WAAAA,OAAA;EACAC,aAAA,WAAAA,cAAA;EACAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAlF,UAAA;MACA;MACAiD,YAAA,CACAkC,OAAA;QACApF,IAAA;UACAqF,SAAA;QACA;QACAC,MAAA;MACA,GACAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAd,GAAA,CAAAe,cAAA,eAAAF,GAAA,CAAAG,MAAA;QACA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA1F,SAAA;IACA;IACAgE,oBAAA,WAAAA,qBAAA2B,SAAA,EAAAC,OAAA;MACA;MACA,IAAAxD,KAAA,OAAA+B,IAAA,CAAAwB,SAAA;MACA,IAAAjD,GAAA,OAAAyB,IAAA,CAAAyB,OAAA;MACA;MACA,IAAAvE,cAAA,GAAAqB,GAAA,GAAAN,KAAA;MACA;MACA,IAAAyD,cAAA,GAAAxE,cAAA;MACA,OAAAuC,IAAA,CAAAG,KAAA,CAAA8B,cAAA;IACA;IACAC,GAAA,WAAAA,IAAAC,IAAA;MAAA,IAAAC,MAAA;MACAhD,YAAA,CACAiD,WAAA;QACAnG,IAAA;UACAwD,UAAA,OAAAxD,IAAA,CAAAmD,EAAA;UACAiD,IAAA,EAAAH,IAAA,CAAAI;QACA;MACA,GAAAd,IAAA,WAAAe,GAAA;QACA,IAAAA,GAAA,CAAAb,IAAA;UACAZ,OAAA,CAAAC,GAAA,CAAAwB,GAAA;UACA3B,GAAA,CAAA4B,cAAA;YACAC,QAAA;YACAC,QAAA,EAAAH,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAD,QAAA;YACAE,OAAA,EAAAL,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAE,UAAA;YACAC,OAAA,EAAAP,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAG,OAAA;YACAC,QAAA,EAAAR,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAI,QAAA;YACAC,SAAA,EAAAT,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAK,SAAA;YACA;YACAC,OAAA,WAAAA,gBAAA;cACAnC,OAAA,CAAAC,GAAA,CAAAkC,QAAA;cACAC,UAAA;gBACAf,MAAA,CAAAgB,EAAA,CAAAC,KAAA;gBACAxC,GAAA,CAAAyC,YAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;YACAC,IAAA,WAAAA,KAAAC,GAAA;cACAzC,OAAA,CAAAC,GAAA,CAAAwC,GAAA;cACApB,MAAA,CAAAgB,EAAA,CAAAC,KAAA;cACAjB,MAAA,CAAAqB,QAAA;YACA;UACA;QACA;MACA;IACA;IACA5D,UAAA,WAAAA,WAAA6D,GAAA;MACA;MACA,IAAAA,GAAA,CAAA9D,MAAA;QACA,QAAA8D,GAAA;MACA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAA9D,MAAA,EAAAgE,CAAA;QACA;QACAD,MAAA,CAAAE,IAAA,CAAAH,GAAA,CAAAxD,KAAA,CAAA0D,CAAA,EAAAA,CAAA;MACA;MACA,OAAAD,MAAA;IACA;IACAG,iBAAA,WAAAA,kBAAAtB,GAAA;MACA,IAAA9E,UAAA,GAAAmD,GAAA,CAAAC,cAAA;MACA,IAAAiD,GAAA;QACAC,KAAA,OAAA9H,IAAA,CAAA+H,IAAA;QACAC,IAAA,gDAAAvD,MAAA,MAAAzE,IAAA,CAAAmD,EAAA,gBAAAsB,MAAA,CAAAjD,UAAA,CAAA+B,QAAA;QACA0E,QAAA,OAAAjI,IAAA,CAAAkI;MACA;MACA,OAAAL,GAAA;IACA;IACA;IACAM,eAAA,WAAAA,gBAAA7B,GAAA;MACA,IAAA9E,UAAA,GAAAmD,GAAA,CAAAC,cAAA;MACA,IAAAiD,GAAA;QACAC,KAAA,OAAA9H,IAAA,CAAA+H,IAAA;QACAC,IAAA,gDAAAvD,MAAA,MAAAzE,IAAA,CAAAmD,EAAA,gBAAAsB,MAAA,CAAAjD,UAAA,CAAA+B,QAAA;QACA0E,QAAA,OAAAjI,IAAA,CAAAkI;MACA;MACA,OAAAL,GAAA;IACA;IACAO,GAAA,WAAAA,IAAA;MAAA,IAAAC,MAAA;MACAnF,YAAA,CACAiD,WAAA;QACAnG,IAAA;UACAwD,UAAA,OAAAxD,IAAA,CAAAmD;QACA;MACA,GACAoC,IAAA,WAAAe,GAAA;QACA,IAAAA,GAAA,CAAAb,IAAA;UACAZ,OAAA,CAAAC,GAAA,CAAAwB,GAAA;UACA3B,GAAA,CAAA4B,cAAA;YACAC,QAAA;YACAC,QAAA,EAAAH,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAD,QAAA;YACAE,OAAA,EAAAL,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAE,UAAA;YACAC,OAAA,EAAAP,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAG,OAAA;YACAC,QAAA,EAAAR,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAI,QAAA;YACAC,SAAA,EAAAT,GAAA,CAAAtG,IAAA,CAAA0G,MAAA,CAAAK,SAAA;YACA;YACAC,OAAA,WAAAA,iBAAA;cACAnC,OAAA,CAAAC,GAAA,CAAAkC,SAAA;cACAC,UAAA;gBACAoB,MAAA,CAAAnB,EAAA,CAAAC,KAAA;gBACAxC,GAAA,CAAAyC,YAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;YACAC,IAAA,WAAAA,KAAAC,GAAA;cACAzC,OAAA,CAAAC,GAAA,CAAAwC,GAAA;cACAe,MAAA,CAAAnB,EAAA,CAAAC,KAAA;cACAkB,MAAA,CAAAd,QAAA;YACA;UACA;QACA;MACA;MACA;MACA;IACA;IACAe,mBAAA,WAAAA,oBAAA5I,CAAA;MACA,KAAAa,aAAA,SAAAA,aAAA;IACA;IACAgI,cAAA,WAAAA,eAAA7I,CAAA;MACA,KAAAc,QAAA,SAAAA,QAAA;IACA;IACAgI,cAAA,WAAAA,eAAA9I,CAAA;MACA,KAAAe,QAAA,GAAAf,CAAA,CAAA+I,MAAA,CAAAC,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAjJ,CAAA;MACA,KAAAgB,QAAA,GAAAhB,CAAA,CAAA+I,MAAA,CAAAC,KAAA;IACA;IACAE,QAAA,WAAAA,SAAAlJ,CAAA;MACA,KAAAsB,QAAA,GAAAtB,CAAA;IACA;EACA;AACA;;;;;;;;;;ACleA;;;;;;;;;;;;;;;ACAAD,mBAAA;AAGA,IAAAoJ,IAAA,GAAArJ,sBAAA,CAAAC,mBAAA;AACA,IAAAqJ,aAAA,GAAAtJ,sBAAA,CAAAC,mBAAA;AAA+D,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH/D;AACAqJ,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLqH;AACrI;AACA,CAAgE;AACL;AAC3D,CAAiG;;;AAGjG;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvByf,CAAC,+DAAe,geAAG,EAAC;;;;;;;;;;;;;;;;;ACAie,CAAC,+DAAe,g6BAAG,EAAC", "sources": ["webpack:///./src/pages-admin/huodongGuanli/huodongindex.vue?18f9", "uni-app:///src/pages-admin/huodong<PERSON>/huodongindex.vue", "webpack:///./src/pages-admin/huodongGuanli/huodongindex.vue?45a0", "uni-app:///src/main.js", "webpack:///./src/pages-admin/huodongGuanli/huodongindex.vue?0f68", "webpack:///./src/pages-admin/huodongGuanli/huodongindex.vue?7b04", "webpack:///./src/pages-admin/huodongGuanli/huodongindex.vue?b838", "webpack:///./src/pages-admin/huodongGuanli/huodongindex.vue?387a"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"73f69fd2-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"73f69fd2-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"73f69fd2-1\", \"content\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n    _vm.e1 = function ($event) {\n      _vm.modelshow = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showQrcode = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\r\n    <themeWrap>\r\n        <template #content=\"{ navBarColor, navBarTextColor, buttonLightBgColor }\">\r\n            <view>\r\n                <!-- 顶部菜单栏 -->\r\n                <u-navbar title=\"活动\" @rightClick=\"uni.navigateBack()\" :titleStyle=\"{ color: navBarTextColor }\"\r\n                    bgColor=\"#298ec3\" :leftIconColor=\"navBarTextColor\" :autoBack=\"true\" :placeholder=\"true\"\r\n                    :safeAreaInsetTop=\"true\">\r\n                    <view class=\"joins\" slot=\"center\" @click=\"onShareAppMessage\">\r\n                        <!-- <button open-type=\"share\" class=\"sharebtn\">\r\n                            分享赚￥\r\n                        </button> -->\r\n                    </view>\r\n                </u-navbar>\r\n            </view>\r\n            <view class=\"box\">\r\n                <view class=\"bg\">\r\n                    <image style=\"width: 100%;    display: block;\"  mode=\"widthFix\" :src=\"data.poster\" />\r\n                </view>\r\n                <view class=\"con\" :style=\"{ backgroundImage: 'url(' + data.background + ')' }\">\r\n                    <view class=\"title\">\r\n                        {{ data.remark || '' }}\r\n                    </view>\r\n                    <view class=\"huodongtime\">\r\n                        活动开始\r\n                        <view style=\"margin-left: 30rpx;\"> <u-count-down :time=\"timeDifference\" format=\"DD:HH:mm:ss\"\r\n                                autoStart millisecond @change=\"onChange\" ref=\"countDown\">\r\n                                <view class=\"time\">\r\n                                    <view class=\"bordercl\">{{ timeData.days }}</view>\r\n                                    <view style=\"color: red;font-weight: 700;margin-left: 10rpx;\">天</view>\r\n                                    <view class=\"bordercl\">{{\r\n                                        timeData.hours >= 10 ? timeData.hours : '0' + timeData.hours }}</view>\r\n                                    <view style=\"color: red;font-weight: 700;margin-left: 10rpx;\">:</view>\r\n                                    <view class=\"bordercl\">{{ timeData.minutes }}</view>\r\n                                    <view style=\"color: red;font-weight: 700;margin-left: 10rpx;\">:</view>\r\n                                    <view class=\"bordercl\">{{ timeData.seconds }}</view>\r\n                                </view>\r\n                            </u-count-down></view>\r\n                    </view>\r\n                    <view class=\"userinfo\">\r\n                        <view class=\"infotext \">\r\n                            <view class=\"poi\">{{ getActivityRecordInfos.readNum }}</view>\r\n                            <view>已浏览</view>\r\n                        </view>\r\n                        <view class=\"infotext poi\">\r\n                            <view class=\"poi\">{{ getActivityRecordInfos.enterNum }}</view>\r\n\r\n                            <view>已报名</view>\r\n                        </view>\r\n                        <view class=\"infotext poi\">\r\n                            <view class=\"poi\">{{ getActivityRecordInfos.shareNum }}</view>\r\n\r\n                            <view>已分享</view>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"shopinfo\">\r\n                        <view class=\"shopinfotop\">\r\n                            <view class=\"shopimg\">\r\n                                <image style=\"width: 100%;height:100%\" :src=\"data.poster\" />\r\n                            </view>\r\n                            <view class=\"shoptopright\">\r\n                                <view class=\"shoptit\">{{ data.name || '' }}</view>\r\n                                <view class=\"shopspecialPrice\">拼团价\r\n                                    <span style=\"font-size: 50rpx;font-weight: 800;\">￥{{ data.specialPrice }}</span>\r\n                                </view>\r\n                                <view class=\"shopsourcePrice\">\r\n                                    <view>原价{{ data.sourcePrice }}</view>\r\n                                </view>\r\n\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"shopbtn\" @click=\"pay\">\r\n                            立即开团\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"lunbo\">\r\n                        <view class=\"luobouser\"><span style=\"font-size: 40rpx;color:red;font-weight: 700;\">{{\r\n                            getEnablePtListlen }}</span>人发出邀请，去参加他们的团\r\n                        </view>\r\n                        <swiper class=\"swiper\" circular :autoplay=\"autoplay\" :interval=\"interval\" :duration=\"duration\"\r\n                            :vertical=\"true\">\r\n                            <swiper-item v-for=\"(item, index) in EnablePtList\" :key=\"index\">\r\n                                <view class=\"infouser\" v-for=\"value in item\" :key=\"value\">\r\n                                    <view class=\"lefts\">\r\n                                        <view class=\"imgsa\">\r\n                                            <image style=\"width: 100%;height:100%\"\r\n                                                :src=\"value.avatar ? value.avatar : '/static/images/default/head1.png'\" />\r\n                                        </view>\r\n                                        {{ value.nickName }}\r\n                                    </view>\r\n                                    <view class=\"rights\">当前{{ value.currentNum }}人，还差{{ value.deficitNum }}人成团、\r\n                                        <view class=\"gow\" @click=\"gow\">去拼团</view>\r\n                                    </view>\r\n                                </view>\r\n                            </swiper-item>\r\n                        </swiper>\r\n                    </view>\r\n\r\n                </view>\r\n\r\n            </view>\r\n            <image v-for=\"item in imgs\"  mode=\"widthFix\" class=\"itemimg\" :src=\"item\" style=\"width: 100%; display: block; \" />\r\n            <view class=\"orderlist\">\r\n                <view class=\"ordertext\">最新订单记录</view>\r\n                <swiper class=\"swiper\" circular :autoplay=\"autoplay\" :interval=\"interval\" :duration=\"duration\"\r\n                    :vertical=\"true\">\r\n                    <swiper-item v-for=\"item in getNewOrderLists\">\r\n                        <view class=\"infouser\" v-for=\"value in item\">\r\n                            <view class=\"lefts\">\r\n                                <view class=\"imgsa\">\r\n                                    <image style=\"width: 100%;height:100%\"\r\n                                        :src=\"value.avatar ? value.avatar : '/static/images/default/head1.png'\" />\r\n                                </view>\r\n                                <view style=\"font-size: 26rpx;\">\r\n                                    <view>{{ value.nickName }}</view>\r\n                                    <view>2025-03-21</view>\r\n                                </view>\r\n\r\n                            </view>\r\n                            <view class=\"rights\">已经支付{{ value.amount }}元 </view>\r\n                        </view>\r\n                    </swiper-item>\r\n                </swiper>\r\n            </view>\r\n            <view class=\"botbtn\">\r\n                <view class=\"shouyi\" @click=\"modelshow = true\">\r\n                    <view><u-icon name=\"integral-fill\" color=\"#abadb0f2\" size=\"20\"></u-icon></view>\r\n                    <view style=\"width: 100%;text-align: center;\">收益中心</view>\r\n                </view>\r\n                <view class=\"kaituan\" @click=\"pay\">\r\n                    <view class=\"textli\">立即开团</view>\r\n                </view>\r\n            </view>\r\n            <u-popup :show=\"modelshow\" mode=\"center\" :round=\"40\" @close=\"cloces\" @open=\"open\" :safeAreaInsetTop=\"false\">\r\n                <view class=\"slot-content\">\r\n                    <view class=\"texts\">邀好友 赢红包</view>\r\n                    <view class=\"texts\" style=\"font-size: 30rpx;\">想要更多佣金，请联系商家</view>\r\n                    <view class=\"modelcon\">\r\n                        <view style=\"    display: flex;margin-bottom: 40rpx;padding-top: 40rpx;\r\n    align-items: center;\r\n    justify-content: center;\">\r\n                            <image style=\"width: 60rpx;height:60rpx\" src=\"/static/images/default/head1.png\" />\r\n                            <view>{{ wxUserInfo.memberName }}的推广记录</view>\r\n                        </view>\r\n                        <view class=\"yiji\">\r\n                            <view>商品</view>\r\n                            <view>购买人</view>\r\n                            <view>奖金类型</view>\r\n                            <view>佣金</view>\r\n                            <view>发放状态</view>\r\n\r\n                        </view>\r\n                        <view class=\"yijicon\">\r\n                            <view v-for=\"i in getCommissionList\" class=\"yiji\" style=\"\">\r\n                                <view class=\"textset\">{{ i.activityName }}</view>\r\n                                <view class=\"textset\">{{ i.nickName }}</view>\r\n                                <view class=\"textset\">{{ i.commissionType }}</view>\r\n                                <view class=\"textset\">{{ i.amount }}</view>\r\n                                <view class=\"textset\">{{ i.grantStatus == 1 ? '已发放' : '未发放' }}</view>\r\n                            </view>\r\n\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"modelbtn\">\r\n                        <view class=\"guanzhu\" @click=\"showQrcode = true\">关注公众号</view>\r\n                        <view class=\"yao\"> <button open-type=\"share\" class=\"sharebtns\">邀请好友赚佣金\r\n                            </button></view>\r\n                    </view>\r\n                    <view class=\"cloce\" @click=\"cloces\"><u-icon size=\"30\" color=\"#e1e1e1\" name=\"close-circle\"></u-icon>\r\n                    </view>\r\n                </view>\r\n\r\n            </u-popup>\r\n            <u-popup :show=\"showQrcode\" mode=\"center\" :safeAreaInsetBottom=\"false\" :round=\"20\" @close=\"onOfficialClose\">\r\n                <officialQrcode></officialQrcode>\r\n            </u-popup>\r\n        </template>\r\n    </themeWrap>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/common/api\";\r\nimport officialQrcode from \"@/components/official-qrcode\";\r\nexport default {\r\n    components: {\r\n        officialQrcode\r\n    },\r\n    data() {\r\n\r\n        return {\r\n            showQrcode: false,\r\n            modelshow: false,\r\n            getActivityRecordInfos: null,\r\n            EnablePtList: [],\r\n            getEnablePtListlen: '',\r\n            background: ['color1', 'color2', 'color3'],\r\n            indicatorDots: true,\r\n            autoplay: true,\r\n            interval: 2000,\r\n            duration: 500,\r\n            data: '',\r\n            currentPage: 1,\r\n            total: 1,\r\n            list: [],\r\n            limit: 100,\r\n            type: '',\r\n            timeData: {\r\n                days: 0,\r\n                hours: 0,\r\n                minutes: 0,\r\n                seconds: 0,\r\n            },\r\n            imgs: [],\r\n            getNewOrderLists: [],\r\n            timeDifference: '',\r\n            wxUserInfo: '',\r\n            getCommissionList: []\r\n        };\r\n    },\r\n    async onLoad(options) {\r\n        let activityPt = await api['getactivityPt1']({\r\n            data: {\r\n                id: options.id\r\n            },\r\n        })\r\n        this.data = activityPt.data\r\n        this.imgs = this.data.infoPic.split(',')\r\n        let data = {}\r\n        if (options.memberId) {\r\n            data = {\r\n                activityId: this.data.id,\r\n                \"type\": 'share',\r\n                prevMemberId: options.memberId\r\n            }\r\n        } else {\r\n            data = {\r\n                activityId: this.data.id,\r\n                \"type\": 'read',\r\n            }\r\n        }\r\n        let setActivityPtRecord = await api['setActivityPtRecord']({\r\n            data: data,\r\n        })\r\n        let getEnablePtList = await api['getEnablePtList']({\r\n            data: {\r\n                activityId: this.data.id,\r\n            },\r\n        })\r\n        this.getEnablePtListlen = getEnablePtList.data.length\r\n        this.EnablePtList = this.chunkArray(getEnablePtList.data)\r\n        let getNewOrderList = await api['getNewOrderList']({\r\n            data: {\r\n                activityId: this.data.id,\r\n                num: 999\r\n            },\r\n        })\r\n        let getNewOrderListdata = []\r\n        if (getNewOrderList.data.length > 6) {\r\n            getNewOrderListdata = getNewOrderList.data\r\n                .sort(() => Math.random() - 0.5) // 随机打乱数组\r\n                .slice(0, Math.floor(getNewOrderList.data.length / 2)); // 获取一半元素\r\n        } else {\r\n            getNewOrderListdata = getNewOrderList.data\r\n        }\r\n        this.getNewOrderLists = this.chunkArray(getNewOrderListdata)\r\n        let getActivityRecordInfo = await api['getActivityRecordInfo']({\r\n            data: {\r\n                activityId: this.data.id,\r\n            },\r\n        })\r\n        this.getActivityRecordInfos = getActivityRecordInfo.data\r\n        this.timeData = {\r\n            days: this.calculateDaysBetween(this.data.startTime, this.data.endTime),\r\n            hours: 23,\r\n            minutes: 59,\r\n            seconds: 59,\r\n        }\r\n        let start = new Date(this.data.startTime);\r\n        let currentDate = new Date();\r\n        let year = currentDate.getFullYear();\r\n        let month = currentDate.getMonth() + 1;\r\n        let day = currentDate.getDate();\r\n        month = month < 10 ? '0' + month : month;\r\n        day = day < 10 ? '0' + day : day;\r\n        let formattedDate = `${year}-${month}-${day}`;\r\n        let end = new Date(formattedDate);\r\n        let timeDifference = end.getTime() - start.getTime();\r\n        this.timeDifference = timeDifference\r\n        this.wxUserInfo = uni.getStorageSync('wxUserInfo')\r\n\r\n        let getCommissionList = await api['getCommissionList']({\r\n            data: {\r\n                activityId: this.data.id,\r\n            },\r\n        })\r\n        this.getCommissionList = getCommissionList.data\r\n        console.log(getCommissionList, 'getCommissionListgetCommissionList')\r\n    },\r\n    onShow() { },\r\n    onReachBottom() { },\r\n    methods: {\r\n        // 公众号弹窗关闭,重新获取用户信息,判断是否关注\r\n        onOfficialClose() {\r\n            this.showQrcode = false\r\n            // 获取用户信息\r\n            api\r\n                .getInfo({\r\n                    data: {\r\n                        companyId: 1,\r\n                    },\r\n                    method: \"GET\",\r\n                })\r\n                .then((ret) => {\r\n                    if (ret.code == 200) {\r\n                        uni.setStorageSync(\"wxUserInfo\", ret.wxUser);\r\n                    }\r\n                });\r\n        },\r\n        cloces() {\r\n            this.modelshow = false\r\n        },\r\n        calculateDaysBetween(startDate, endDate) {\r\n            // 将日期字符串转换为 Date 对象\r\n            let start = new Date(startDate);\r\n            let end = new Date(endDate);\r\n            // 计算时间差（毫秒）\r\n            let timeDifference = end - start;\r\n            // 将毫秒转换为天数\r\n            let daysDifference = timeDifference / (1000 * 3600 * 24); // 1000毫秒 * 3600秒 * 24小时\r\n            return Math.floor(daysDifference); // 返回天数（去掉小数部分）\r\n        },\r\n        gow(info) {\r\n            api\r\n                .createOrder({\r\n                    data: {\r\n                        activityId: this.data.id,\r\n                        ptId: info.ptInfoId,\r\n                    },\r\n                }).then(res => {\r\n                    if (res.code == 200) {\r\n                        console.log(res);\r\n                        uni.requestPayment({\r\n                            provider: \"weixin\",\r\n                            nonceStr: res.data.prepay.nonceStr,\r\n                            package: res.data.prepay.packageVal,\r\n                            paySign: res.data.prepay.paySign,\r\n                            signType: res.data.prepay.signType,\r\n                            timeStamp: res.data.prepay.timeStamp + \"\",\r\n                            // orderInfo: res.data.order,\r\n                            success: (success) => {\r\n                                console.log(success);\r\n                                setTimeout(() => {\r\n                                    this.$u.toast(\"支付成功！\");\r\n                                    uni.navigateBack()\r\n                                }, 2000)\r\n                                // api.postCardPayOrder({\r\n                                //     data: {\r\n                                //       tradeId: res.data.tradeId,\r\n                                //     },\r\n                                //     method: \"GET\",\r\n                                //   }).then((order) => {\r\n                                //     // 0正常 1未付款 2已过期\r\n                                //     setTimeout(() => {\r\n                                //       this.$u.toast(\"支付成功！\");\r\n                                //       uni.navigateBack()\r\n                                //     }, 2000)\r\n                                //   }).catch(err => {\r\n                                //     setTimeout(() => {\r\n                                //       this.$u.toast(\"支付成功！支付状态查询失败~\");\r\n                                //       uni.navigateBack()\r\n                                //     }, 2000)\r\n                                //   });\r\n                            },\r\n                            fail: (err) => {\r\n                                console.log(err);\r\n                                this.$u.toast(\"支付失败！\");\r\n                                this.disabled = false;\r\n                            },\r\n                        });\r\n                    }\r\n                })\r\n        },\r\n        chunkArray(arr) {\r\n            // 检查数组是否大于2\r\n            if (arr.length <= 2) {\r\n                return [arr];\r\n            }\r\n            let result = [];\r\n            for (let i = 0; i < arr.length; i += 2) {\r\n                // 将每两项放到一组中\r\n                result.push(arr.slice(i, i + 2));\r\n            }\r\n            return result;\r\n        },\r\n        onShareAppMessage(res) {\r\n            const wxUserInfo = uni.getStorageSync('wxUserInfo')\r\n            let obj = {\r\n                title: this.data.name,\r\n                path: `/pages-admin/huodongGuanli/huodongindex?id=${this.data.id}&memberId=${wxUserInfo.memberId}`,\r\n                imageUrl: this.data.poster,\r\n            }\r\n            return obj\r\n        },\r\n        //2.分享到朋友圈\r\n        onShareTimeline(res) {\r\n            const wxUserInfo = uni.getStorageSync('wxUserInfo')\r\n            let obj = {\r\n                title: this.data.name,\r\n                path: `/pages-admin/huodongGuanli/huodongindex?id=${this.data.id}&memberId=${wxUserInfo.memberId}`,\r\n                imageUrl: this.data.poster,\r\n            }\r\n            return obj\r\n        },\r\n        pay() {\r\n            api\r\n                .createOrder({\r\n                    data: {\r\n                        activityId: this.data.id,\r\n                    },\r\n                })\r\n                .then((res) => {\r\n                    if (res.code == 200) {\r\n                        console.log(res);\r\n                        uni.requestPayment({\r\n                            provider: \"weixin\",\r\n                            nonceStr: res.data.prepay.nonceStr,\r\n                            package: res.data.prepay.packageVal,\r\n                            paySign: res.data.prepay.paySign,\r\n                            signType: res.data.prepay.signType,\r\n                            timeStamp: res.data.prepay.timeStamp + \"\",\r\n                            // orderInfo: res.data.order,\r\n                            success: (success) => {\r\n                                console.log(success);\r\n                                setTimeout(() => {\r\n                                    this.$u.toast(\"支付成功！\");\r\n                                    uni.navigateBack()\r\n                                }, 2000)\r\n                                // api.postCardPayOrder({\r\n                                //     data: {\r\n                                //       tradeId: res.data.tradeId,\r\n                                //     },\r\n                                //     method: \"GET\",\r\n                                //   }).then((order) => {\r\n                                //     // 0正常 1未付款 2已过期\r\n                                //     setTimeout(() => {\r\n                                //       this.$u.toast(\"支付成功！\");\r\n                                //       uni.navigateBack()\r\n                                //     }, 2000)\r\n                                //   }).catch(err => {\r\n                                //     setTimeout(() => {\r\n                                //       this.$u.toast(\"支付成功！支付状态查询失败~\");\r\n                                //       uni.navigateBack()\r\n                                //     }, 2000)\r\n                                //   });\r\n                            },\r\n                            fail: (err) => {\r\n                                console.log(err);\r\n                                this.$u.toast(\"支付失败！\");\r\n                                this.disabled = false;\r\n                            },\r\n                        });\r\n                    }\r\n                });\r\n            // 是否支持自动续费\r\n            // if(this.detail.is_xuFei){}\r\n        },\r\n        changeIndicatorDots(e) {\r\n            this.indicatorDots = !this.indicatorDots\r\n        },\r\n        changeAutoplay(e) {\r\n            this.autoplay = !this.autoplay\r\n        },\r\n        intervalChange(e) {\r\n            this.interval = e.target.value\r\n        },\r\n        durationChange(e) {\r\n            this.duration = e.target.value\r\n        },\r\n        onChange(e) {\r\n            this.timeData = e\r\n        }\r\n    },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.textset {\r\n    width: 25%;\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    /* 防止文字换行 */\r\n    overflow: hidden;\r\n    /* 隐藏超出容器部分的文字 */\r\n    text-overflow: ellipsis;\r\n    /* 用省略号表示被截断的文字 */\r\n}\r\n\r\n.slot-content {\r\n    width: 80vw;\r\n    height: 700rpx;\r\n    background: rgb(242, 62, 60);\r\n    border-radius: 50rpx;\r\n\r\n    .texts {\r\n        font-size: 50rpx;\r\n        font-weight: 800;\r\n        font-family: Arial, Helvetica, sans-serif;\r\n        color: rgb(253, 227, 190);\r\n        text-align: center;\r\n        width: 100%;\r\n    }\r\n\r\n    .modelcon {\r\n        width: 90%;\r\n        height: 500rpx;\r\n        background: white;\r\n        border-radius: 50rpx;\r\n        text-align: center;\r\n        margin: 0 auto;\r\n        margin-top: 50rpx;\r\n\r\n        .yiji {\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-around;\r\n            color: #c7c1c1;\r\n            font-size: 30rpx;\r\n        }\r\n\r\n        .yijicon {\r\n            width: 100%;\r\n            height: 300rpx;\r\n            color: #c7c1c1;\r\n            display: flex;\r\n            justify-content: space-around;\r\n            flex-wrap: wrap;\r\n            overflow: auto;\r\n            font-size: 24rpx;\r\n        }\r\n    }\r\n}\r\n\r\n.cloce {\r\n    position: relative;\r\n    height: 5rpx;\r\n    left: 0;\r\n    bottom: -30rpx;\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.modelbtn {\r\n    position: relative;\r\n    height: 170rpx;\r\n    left: 0;\r\n    bottom: -20rpx;\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-around;\r\n\r\n    .guanzhu {\r\n        width: 40%;\r\n        padding: 20rpx 40rpx;\r\n        height: 74rpx;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-around;\r\n        font-size: 30rpx;\r\n        background: rgb(254, 227, 188);\r\n        border-radius: 30rpx;\r\n        color: rgb(104, 70, 47);\r\n        justify-content: center;\r\n\r\n    }\r\n\r\n    .yao {\r\n        width: 50%;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 30rpx;\r\n        background: rgb(249, 90, 60);\r\n        border-radius: 30rpx;\r\n        color: white;\r\n        justify-content: center;\r\n\r\n        .sharebtns {\r\n            width: 200rp;\r\n            height: 74rpx;\r\n            background: rgb(249, 90, 60);\r\n            font-size: 30rpx;\r\n            color: white;\r\n            display: inline;\r\n            border: none;\r\n\r\n        }\r\n    }\r\n}\r\n\r\n.sharebtn {\r\n    width: 30vw;\r\n    height: 100rpx;\r\n    margin-left: 10%;\r\n    background: rgb(26, 110, 154);\r\n    display: flex;\r\n    align-items: center;\r\n    border-radius: 30rpx;\r\n    color: white;\r\n    text-align: center;\r\n    justify-content: center;\r\n    font-size: 34rpx;\r\n}\r\n\r\n.joins {\r\n    width: 30vw;\r\n    margin-left: 10%;\r\n    background: rgb(26, 110, 154);\r\n    display: flex;\r\n    align-items: center;\r\n    border-radius: 30rpx;\r\n    color: white;\r\n    text-align: center;\r\n    justify-content: center;\r\n}\r\n\r\n.time {\r\n    @include flex;\r\n    align-items: center;\r\n    color: #fff;\r\n\r\n    .bordercl {\r\n        padding: 10rpx;\r\n        background: red;\r\n        margin-left: 10rpx;\r\n        border-radius: 10rpx;\r\n    }\r\n\r\n    &__item {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        text-align: center;\r\n    }\r\n}\r\n\r\n.uni-margin-wrap {\r\n    width: 690rpx;\r\n    width: 100%;\r\n}\r\n\r\n\r\n\r\n.uni-common-mt {\r\n    margin-top: 60rpx;\r\n    position: relative;\r\n}\r\n\r\n.info {\r\n    position: absolute;\r\n    right: 20rpx;\r\n}\r\n\r\n.uni-padding-wrap {\r\n    width: 550rpx;\r\n    padding: 0 100rpx;\r\n}\r\n\r\n.box {\r\n    width: 100vw;\r\n    height: auto;\r\n    background: rgb(62, 200, 125);\r\n    overflow: scroll;\r\n    // padding-bottom: 162rpx;\r\n\r\n    .bg {\r\n        width: 100%;\r\n    }\r\n\r\n    .con {\r\n        width: 100%;\r\n        background-size: 100%;\r\n        padding-top: 40rpx;\r\n\r\n        .title {\r\n            width: 90%;\r\n            font-size: 34rpx;\r\n            font-weight: 700;\r\n            color: red;\r\n            padding: 10rpx 40rpx 10rpx 40rpx;\r\n            background: white;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            word-wrap: break-word;\r\n            margin: 0 auto;\r\n            text-align: center;\r\n            border-radius: 10rpx;\r\n        }\r\n\r\n        .huodongtime {\r\n            width: 70%;\r\n            margin: 0 auto;\r\n            height: 100rpx;\r\n            font-size: 30rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            color: red;\r\n        }\r\n\r\n        .userinfo {\r\n            width: 90%;\r\n            border-radius: 10rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            background: white;\r\n            color: white;\r\n            margin: 0 auto;\r\n            margin-top: 30rpx;\r\n\r\n\r\n\r\n            .infotext {\r\n                width: 90%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-around;\r\n                color: #b7b7b7ef;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                flex-wrap: wrap;\r\n                font-size: 30rpx;\r\n\r\n\r\n                .poi {\r\n                    width: 100%;\r\n                    color: red;\r\n                    font-size: 40rpx;\r\n                    font-weight: 700;\r\n                    text-align: center;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .shopinfo {\r\n            width: 90%;\r\n            border-radius: 10rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            background: white;\r\n            color: white;\r\n            margin: 0 auto;\r\n            margin-top: 30rpx;\r\n            flex-wrap: wrap;\r\n\r\n            .shopinfotop {\r\n                width: 100%;\r\n                display: flex;\r\n                justify-content: center;\r\n                padding: 30rpx;\r\n\r\n                .shopimg {\r\n                    width: 40%;\r\n                    height: 200rpx;\r\n                }\r\n\r\n                .shoptopright {\r\n                    width: 70%;\r\n                    padding-left: 20rpx;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    flex-wrap: wrap;\r\n                    justify-content: center;\r\n\r\n                    .shoptit {\r\n                        width: 100%;\r\n                        font-size: 40rpx;\r\n                        font-weight: 700;\r\n                        word-wrap: break-word;\r\n                        /* 长单词换行 */\r\n                        color: black;\r\n                    }\r\n\r\n                    .shopspecialPrice {\r\n                        width: 100%;\r\n                        display: flex;\r\n                        font-size: 30rpx;\r\n                        color: red;\r\n                        align-items: baseline;\r\n                    }\r\n\r\n                    .shopsourcePrice {\r\n                        width: 100%;\r\n                        font-size: 30rpx;\r\n                        color: #b7b7b7ef;\r\n                        text-decoration: line-through;\r\n                    }\r\n\r\n\r\n                }\r\n\r\n            }\r\n\r\n            .shopbtn {\r\n                width: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-size: 44rpx;\r\n                color: white;\r\n                background: rgb(253, 41, 69);\r\n                padding: 20rpx;\r\n                animation: shopbtn 2s infinite;\r\n                border-radius: 10rpx;\r\n\r\n            }\r\n\r\n\r\n        }\r\n\r\n        .lunbo {\r\n            width: 90%;\r\n            border-radius: 10rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            background: white;\r\n            color: white;\r\n            margin: 0 auto;\r\n            margin-top: 30rpx;\r\n            flex-wrap: wrap;\r\n\r\n            .luobouser {\r\n                width: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                color: black;\r\n            }\r\n\r\n            .swiper {\r\n                width: 100%;\r\n                height: 300rpx;\r\n\r\n                .infouser {\r\n                    width: 100%;\r\n                    height: 50%;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: space-between;\r\n                    padding: 0 10rpx;\r\n\r\n                    .lefts {\r\n                        width: 30%;\r\n                        height: 100rpx;\r\n                        color: black;\r\n                        display: flex;\r\n                        align-items: center;\r\n\r\n                        .imgsa {\r\n                            width: 60rpx;\r\n                            height: 60rpx;\r\n                            border-radius: 50%;\r\n                            color: black;\r\n                            overflow: hidden;\r\n                            margin: 0 10rpx;\r\n                        }\r\n\r\n                    }\r\n\r\n                    .rights {\r\n                        width: 70%;\r\n                        height: 100rpx;\r\n                        color: black;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: space-between;\r\n                        font-size: 30rpx;\r\n\r\n                        .gow {\r\n                            width: 114rpx;\r\n                            height: 56rpx;\r\n                            display: flex;\r\n                            align-items: center;\r\n                            justify-content: center;\r\n                            color: white;\r\n                            background: red;\r\n                            border-radius: 20rpx;\r\n                            font-weight: 700;\r\n                        }\r\n                    }\r\n\r\n                }\r\n            }\r\n\r\n            .swiper-item {\r\n                display: block;\r\n                height: 300rpx;\r\n                line-height: 300rpx;\r\n                text-align: center;\r\n            }\r\n\r\n            .swiper-list {\r\n                margin-top: 40rpx;\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n\r\n\r\n    }\r\n}\r\n\r\n\r\n.itemimg {\r\n    width: 100%;\r\n    height: auto;\r\n}\r\n\r\n\r\n.orderlist {\r\n    width: 90%;\r\n    border-radius: 10rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: rgb(249, 87, 100);\r\n    color: white;\r\n    margin: 0 auto;\r\n    margin-top: 30rpx;\r\n    flex-wrap: wrap;\r\n    margin-bottom: 143rpx;\r\n    .ordertext {\r\n        color: white;\r\n        font-size: 40rpx;\r\n\r\n    }\r\n\r\n    .luobouser {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n    }\r\n\r\n    .swiper {\r\n        width: 100%;\r\n        margin: 20rpx;\r\n        height: 400rpx;\r\n        background: white;\r\n        border-radius: 20rpx;\r\n\r\n        .infouser {\r\n            width: 100%;\r\n            height: 50%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            padding: 0 10rpx;\r\n\r\n            .lefts {\r\n                width: 55%;\r\n                height: 100rpx;\r\n                color: black;\r\n                display: flex;\r\n                align-items: center;\r\n\r\n                .imgsa {\r\n                    width: 100rpx;\r\n                    height: 100rpx;\r\n                    border-radius: 50%;\r\n                    color: black;\r\n                    overflow: hidden;\r\n                    margin: 0 10rpx;\r\n                }\r\n\r\n            }\r\n\r\n            .rights {\r\n                width: 70%;\r\n                height: 100rpx;\r\n                color: red;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: flex-end;\r\n                font-size: 30rpx;\r\n\r\n                .gow {\r\n                    width: 114rpx;\r\n                    height: 56rpx;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    color: white;\r\n                    background: red;\r\n                }\r\n            }\r\n\r\n        }\r\n    }\r\n\r\n    .swiper-item {\r\n        display: block;\r\n        height: 300rpx;\r\n        line-height: 300rpx;\r\n        text-align: center;\r\n    }\r\n\r\n    .swiper-list {\r\n        margin-top: 40rpx;\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\n@keyframes shopbtn {\r\n    0% {\r\n        transform: scale(0.8);\r\n        /* 初始大小 */\r\n    }\r\n\r\n    50% {\r\n        transform: scale(0.9);\r\n        /* 放大到1.5倍 */\r\n    }\r\n\r\n    100% {\r\n        transform: scale(0.8);\r\n        /* 缩小回原大小 */\r\n    }\r\n}\r\n\r\n.botbtn {\r\n    width: 100vw;\r\n    height: 100rpx;\r\n    position: fixed;\r\n    left: 0;\r\n    bottom: 0;\r\n    display: flex;\r\n    background: white;\r\n\r\n    .shouyi {\r\n        width: 20%;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        justify-content: center;\r\n        align-items: center;\r\n        font-size: 30rpx;\r\n    }\r\n\r\n    .kaituan {\r\n        width: 80%;\r\n        height: 100%;\r\n        background: red;\r\n        font-size: 35rpx;\r\n        color: white;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-weight: bold;\r\n\r\n        .textli {\r\n            animation: zoomText 2s infinite;\r\n        }\r\n\r\n        /* 动画持续2秒，循环播放 */\r\n    }\r\n\r\n    @keyframes zoomText {\r\n        0% {\r\n            transform: scale(1);\r\n            /* 初始大小 */\r\n        }\r\n\r\n        50% {\r\n            transform: scale(1.5);\r\n            /* 放大到1.5倍 */\r\n        }\r\n\r\n        100% {\r\n            transform: scale(1);\r\n            /* 缩小回原大小 */\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages-admin/huodongGuanli/huodongindex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./huodongindex.vue?vue&type=template&id=beb65098&scoped=true&\"\nvar renderjs\nimport script from \"./huodongindex.vue?vue&type=script&lang=js&\"\nexport * from \"./huodongindex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./huodongindex.vue?vue&type=style&index=0&id=beb65098&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"beb65098\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages-admin/huodongGuanli/huodongindex.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./huodongindex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./huodongindex.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./huodongindex.vue?vue&type=style&index=0&id=beb65098&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./huodongindex.vue?vue&type=style&index=0&id=beb65098&scoped=true&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./huodongindex.vue?vue&type=template&id=beb65098&scoped=true&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "_regeneratorRuntime", "components", "officialQrcode", "data", "showQrcode", "modelshow", "getActivityRecordInfos", "EnablePtList", "getEnablePtListlen", "background", "indicatorDots", "autoplay", "interval", "duration", "currentPage", "total", "list", "limit", "type", "timeData", "days", "hours", "minutes", "seconds", "imgs", "getNewOrderLists", "timeDifference", "wxUserInfo", "getCommissionList", "onLoad", "options", "_this", "_asyncToGenerator", "mark", "_callee", "activityPt", "setActivityPtRecord", "getEnablePtList", "getNewOrderList", "getNewOrderListdata", "getActivityRecordInfo", "start", "currentDate", "year", "month", "day", "formattedDate", "end", "wrap", "_callee$", "_context", "prev", "next", "api", "id", "sent", "infoPic", "split", "memberId", "activityId", "prevMemberId", "length", "chunkArray", "num", "sort", "Math", "random", "slice", "floor", "calculateDaysBetween", "startTime", "endTime", "Date", "getFullYear", "getMonth", "getDate", "concat", "getTime", "uni", "getStorageSync", "console", "log", "stop", "onShow", "onReachBottom", "methods", "onOfficialClose", "getInfo", "companyId", "method", "then", "ret", "code", "setStorageSync", "wxUser", "cloces", "startDate", "endDate", "daysDifference", "gow", "info", "_this2", "createOrder", "ptId", "ptInfoId", "res", "requestPayment", "provider", "nonceStr", "prepay", "package", "packageVal", "paySign", "signType", "timeStamp", "success", "setTimeout", "$u", "toast", "navigateBack", "fail", "err", "disabled", "arr", "result", "i", "push", "onShareAppMessage", "obj", "title", "name", "path", "imageUrl", "poster", "onShareTimeline", "pay", "_this3", "changeIndicatorDots", "changeAutoplay", "intervalChange", "target", "value", "durationChange", "onChange", "_vue", "_huodongindex", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}