(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/@dcloudio/uni-ui/lib/uni-td/uni-td"],{6076:function(){},28179:function(e,t,n){"use strict";var u;n.r(t),n.d(t,{__esModule:function(){return l.__esModule},default:function(){return p}});var i,o=function(){var e=this,t=e.$createElement;e._self._c},r=[],l=n(92840),a=l["default"],d=n(6076),s=n.n(d),c=(s(),n(18535)),f=(0,c["default"])(a,o,r,!1,null,null,null,!1,u,i),p=f.exports},92840:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;t["default"]={name:"uniTd",options:{virtualHost:!0},props:{width:{type:[String,Number],default:""},align:{type:String,default:"left"},rowspan:{type:[Number,String],default:1},colspan:{type:[Number,String],default:1}},data:function(){return{border:!1}},created:function(){this.root=this.getTable(),this.border=this.root.border},methods:{getTable:function(){var e=this.$parent,t=e.$options.name;while("uniTable"!==t){if(e=e.$parent,!e)return!1;t=e.$options.name}return e}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/@dcloudio/uni-ui/lib/uni-td/uni-td-create-component"],{},function(e){e("81715")["createComponent"](e(28179))}]);