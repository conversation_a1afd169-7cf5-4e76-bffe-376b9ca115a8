<theme-wrap scoped-slots-compiler="augmented" vue-id="304cf2e6-1" class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast vue-id="{{('304cf2e6-2')+','+('304cf2e6-1')}}" data-ref="toast" class="data-v-3bef88c6 vue-ref" bind:__l="__l"></u-toast><view class="data-v-3bef88c6"><u-navbar vue-id="{{('304cf2e6-3')+','+('304cf2e6-1')}}" title="信息" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-3bef88c6" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-3bef88c6"><u-form vue-id="{{('304cf2e6-4')+','+('304cf2e6-1')}}" model="{{form}}" labelWidth="140" data-ref="uForm" class="data-v-3bef88c6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-3bef88c6"><u-form-item vue-id="{{('304cf2e6-5')+','+('304cf2e6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="sn" label="设备sn" class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('304cf2e6-6')+','+('304cf2e6-5')}}" inputAlign="right" border="none" placeholder="请输入设备sn" value="{{form.sn}}" data-event-opts="{{[['^input',[['__set_model',['$0','sn','$event',[]],['form']]]]]}}" class="data-v-3bef88c6" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('304cf2e6-7')+','+('304cf2e6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="deviceTypeId" label="设备类型id" class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-3bef88c6" style="{{'color:'+(form.deviceTypeId?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.deviceTypename||'请选择设备类型id')+''}}</view><u-picker vue-id="{{('304cf2e6-8')+','+('304cf2e6-7')}}" show="{{deviceTypeId}}" columns="{{deviceTypeIdcolumns}}" keyName="typeName" value="{{form.deviceTypeId}}" data-event-opts="{{[['^cancel',[['e2']]],['^confirm',[['confirmdeviceTypeId']]],['^input',[['__set_model',['$0','deviceTypeId','$event',[]],['form']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-3bef88c6" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('304cf2e6-9')+','+('304cf2e6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="name" label="设备名 " class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('304cf2e6-10')+','+('304cf2e6-9')}}" inputAlign="right" border="none" placeholder="请输入设备名" value="{{form.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" class="data-v-3bef88c6" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('304cf2e6-11')+','+('304cf2e6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="inOrOut" label="进出状态" class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-3bef88c6" style="{{'color:'+(form.inOrOut?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.inOrOutname||'请选择进出状态')+''}}</view><u-picker vue-id="{{('304cf2e6-12')+','+('304cf2e6-11')}}" show="{{inOrOut}}" columns="{{columns}}" keyName="label" value="{{form.type}}" data-event-opts="{{[['^cancel',[['e4']]],['^confirm',[['confirmCoach']]],['^input',[['__set_model',['$0','type','$event',[]],['form']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-3bef88c6" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('304cf2e6-13')+','+('304cf2e6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="position" label="所放位置" class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('304cf2e6-14')+','+('304cf2e6-13')}}" inputAlign="right" border="none" placeholder="请输入所放位置" value="{{form.position}}" data-event-opts="{{[['^input',[['__set_model',['$0','position','$event',[]],['form']]]]]}}" class="data-v-3bef88c6" bind:__l="__l"></u-input></u-form-item></view></u-form></view><block wx:if="{{type!='view'}}"><view class="bottom-blk bg-fff w-100 u-p-40 data-v-3bef88c6"><u-button vue-id="{{('304cf2e6-15')+','+('304cf2e6-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" loading="{{disabled}}" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['createGroupCourse']]]]}}" bind:click="__e" class="data-v-3bef88c6" bind:__l="__l" vue-slots="{{['default']}}">{{''+(type=='add'?'创建':'保存')+''}}</u-button></view></block></view></theme-wrap>