<theme-wrap scoped-slots-compiler="augmented" vue-id="563484e7-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('563484e7-2')+','+('563484e7-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="{{nickname}}" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="container u-p-t-40 bottom-placeholder"><view class="u-flex u-row-between u-col-center"><view class="u-font-40 font-bold">{{detail.courseName}}</view></view><view class="w-100 bg-fff u-m-t-40 border-16 overflow-hidden"><calendar bind:changeActive="__e" vue-id="{{('563484e7-3')+','+('563484e7-1')}}" data-event-opts="{{[['^changeActive',[['changeActive']]]]}}" bind:__l="__l"></calendar></view><view data-event-opts="{{[['tap',[['handleOfficial',['$event']]]]]}}" class="u-flex u-tips-color u-font-26 u-m-l-10 u-m-t-20" bindtap="__e"></view><view class="w-100 bg-fff border-16 u-p-40 u-m-t-20 overflow-hidden"><view class="u-flex u-row-between u-col-center u-p-b-20"><view class="u-font-32 font-bold">选择排期</view><view class="u-font-28 u-tips-color">{{'课程时长：'+detail.courseDuration+'分钟'}}</view></view><block wx:if="{{$root.g0}}"><view class="u-border-top u-p-t-20 u-p-b-20 u-relative"><view hidden="{{!(loading)}}" class="u-absolute u-flex u-row-center u-col-center w-100 h-100" style="top:0;left:0;"><u-loading-icon vue-id="{{('563484e7-4')+','+('563484e7-1')}}" mode="circle" loading="{{true}}" bind:__l="__l"></u-loading-icon></view><view class="courses-wrap"><block wx:for="{{courses}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="course-blk u-p-10"><view data-event-opts="{{[['tap',[['changeCurrentIndex',[index,'$0'],[[['courses','',index,'status']]]]]]]}}" class="{{['w-100','u-p-t-20','u-relative','u-p-b-20','u-text-center','course-item','border-16',(i.status!==1)?'disabled':'',(index==currentIndex)?'active':'',(i.status===2)?'expired':'',(i.status===3)?'full':'',(i.status===4)?'reset':'']}}" bindtap="__e">{{''+i.text+''}}</view></view></block></view></view></block><block wx:else><view class="w-100 u-p-t-80 u-border-top u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:300rpx;height:300rpx;" src="/static/images/empty/order.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无排期</view></view></block></view><block wx:if="{{currentIndex!==null}}"><view class="bottom-blk bg-fff u-flex w-100 u-p-40"><block wx:if="{{!isCoach}}"><view class="u-flex-1 u-m-r-10"><u-button vue-id="{{('563484e7-5')+','+('563484e7-1')}}" color="{{$root.m2['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['toBuyHuiYuanKa']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">购买会员卡</u-button></view></block><block wx:if="{{!isCoach&&currentIndex!==null}}"><view class="u-flex-1"><u-button vue-id="{{('563484e7-6')+','+('563484e7-1')}}" color="{{$root.m3['buttonLightBgColor']}}" loading="{{disabled}}" loadingText="预约中..." shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['appointment']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">预约课程</u-button></view></block><block wx:if="{{isCoach&&currentIndex!==null}}"><view class="u-flex-1"><u-button vue-id="{{('563484e7-7')+','+('563484e7-1')}}" color="{{$root.m4['buttonLightBgColor']}}" loading="{{disabled}}" loadingText="预约中..." shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['appointmentHelp']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">帮学员预约</u-button></view></block></view></block></view><u-popup vue-id="{{('563484e7-8')+','+('563484e7-1')}}" show="{{showUserSelect}}" mode="center" safeAreaInsetBottom="{{false}}" round="{{20}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="user-select"><u-search vue-id="{{('563484e7-9')+','+('563484e7-8')}}" placeholder="学员手机号" showAction="{{true}}" value="{{phoneKey}}" data-event-opts="{{[['^search',[['getUserList']]],['^custom',[['getUserList']]],['^input',[['__set_model',['','phoneKey','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" bind:__l="__l"></u-search><u-list vue-id="{{('563484e7-10')+','+('563484e7-8')}}" height="200" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{userList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-list-item vue-id="{{('563484e7-11-'+index)+','+('563484e7-10')}}" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('563484e7-12-'+index)+','+('563484e7-11-'+index)}}" title="{{item.nickName}}" data-event-opts="{{[['^click',[['handleUserItem',[index]]]]]}}" bind:click="__e" bind:__l="__l"></u-cell></u-list-item></block></u-list></view></u-popup><u-popup vue-id="{{('563484e7-13')+','+('563484e7-1')}}" show="{{showQrcode}}" mode="center" safeAreaInsetBottom="{{false}}" round="{{20}}" data-event-opts="{{[['^close',[['onOfficialClose']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><official-qrcode vue-id="{{('563484e7-14')+','+('563484e7-13')}}" bind:__l="__l"></official-qrcode></u-popup></view></theme-wrap>