{"version": 3, "file": "pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACvBA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACzEA,IAAAA,KAAA,GAAAC,mBAAA;AAAA,SAAAC,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAX,CAAA,GAAAS,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAP,CAAA,GAAAA,CAAA,CAAAY,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAP,CAAA,EAAAC,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAR,CAAA,YAAAQ,CAAA;AAAA,SAAAS,cAAAX,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAjB,CAAA,EAAAG,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAe,gBAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAkB,cAAA,CAAAlB,CAAA,MAAAD,CAAA,GAAAG,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,IAAAmB,KAAA,EAAAlB,CAAA,EAAAM,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAAtB,CAAA,CAAAC,CAAA,IAAAC,CAAA,EAAAF,CAAA;AAAA,SAAAmB,eAAAjB,CAAA,QAAAqB,CAAA,GAAAC,YAAA,CAAAtB,CAAA,gCAAAT,OAAA,CAAA8B,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAtB,CAAA,EAAAD,CAAA,oBAAAR,OAAA,CAAAS,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAF,CAAA,GAAAE,CAAA,CAAAP,MAAA,CAAA8B,WAAA,kBAAAzB,CAAA,QAAAuB,CAAA,GAAAvB,CAAA,CAAA0B,IAAA,CAAAxB,CAAA,EAAAD,CAAA,gCAAAR,OAAA,CAAA8B,CAAA,UAAAA,CAAA,YAAAI,SAAA,yEAAA1B,CAAA,GAAA2B,MAAA,GAAAC,MAAA,EAAA3B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACA4B,QAAA,EAAAnB,aAAA,KACA,IAAAoB,gBAAA,mBACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,MAAAC,WAAA;EACA;AACA;;;;;;;;;;;;;;;;;;ACwCA,IAAAC,IAAA,GAAAC,sBAAA,CAAAlD,mBAAA;AACA,IAAAmD,UAAA,GAAAD,sBAAA,CAAAlD,mBAAA;AAAA,SAAAkD,uBAAA1C,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAA4C,UAAA,GAAA5C,CAAA,KAAAoC,OAAA,EAAApC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACA6C,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,YAAA;QACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,KAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACAC,QAAA;MACAC,OAAA;MACAC,YAAA;MACAC,YAAA;MACAC,SAAA;MACAC,YAAA;MACAC,MAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAAC,GAAA;IAAA,IAAAC,KAAA;IACA,KAAApB,QAAA,CAAAC,MAAA,GAAAkB,GAAA,CAAAlB,MAAA;IACAT,OAAA,CAAAC,GAAA,CAAA0B,GAAA;IACA;IACA,IAAAA,GAAA,CAAAE,KAAA;MACA,KAAAT,OAAA,IACA;QACAU,SAAA;QACAC,SAAA;MACA,GACA;MACA,KAAAT,YAAA;QACAQ,SAAA;QACAC,SAAA;MACA;MACA,KAAAvB,QAAA,CAAAG,YAAA;MACA,KAAAH,QAAA,CAAAwB,QAAA;MACA,KAAAP,MAAA;IACA;MACAQ,YAAA,CAAAC,WAAA;QACAC,QAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAT,KAAA,CAAAN,YAAA,GAAAe,GAAA,CAAA9B,IAAA;QACAqB,KAAA,CAAAR,OAAA,IAAAiB,GAAA,CAAA9B,IAAA;MACA;MACA,KAAA+B,YAAA;IACA;EACA;EACAC,OAAA;IACA;IACAD,YAAA,WAAAA,aAAA;MAAA,IAAAE,MAAA;MACAP,YAAA,CAAAQ,YAAA;QACAlC,IAAA;UACAE,MAAA,OAAAD,QAAA,CAAAC;QACA;MACA,GAAA2B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAK,IAAA;UACA1C,OAAA,CAAAC,GAAA,CAAAoC,GAAA,CAAAM,IAAA;UACAH,MAAA,CAAAhB,YAAA,IAAAa,GAAA,CAAAM,IAAA;QACA;MACA;IACA;IACAX,QAAA,WAAAA,SAAAY,GAAA;MACA,KAAAtB,YAAA,CAAA9C,OAAA,WAAAqE,IAAA;QACA,IAAAA,IAAA,CAAAd,SAAA,IAAAa,GAAA;UACA,OAAAC,IAAA,CAAAf,SAAA;QACA;MACA;MACA;IACA;IACAgB,UAAA,WAAAA,WAAApF,CAAA;MACAsC,OAAA,CAAAC,GAAA,CAAAvC,CAAA;MACA,KAAA8C,QAAA,CAAAG,YAAA,GAAAjD,CAAA,CAAAoB,KAAA,IAAAgD,SAAA;MACA,KAAAtB,QAAA,CAAAwB,QAAA,GAAAtE,CAAA,CAAAoB,KAAA,IAAAiD,SAAA;MACA;MACA,IAAArE,CAAA,CAAAoB,KAAA,IAAAiD,SAAA;QACA,KAAAvB,QAAA,CAAAS,SAAA;QACA,KAAAT,QAAA,CAAAuC,OAAA;QACA,KAAAtB,MAAA;MACA;QACA,KAAAA,MAAA;MACA;MACA,KAAAN,QAAA;IACA;IACA6B,WAAA,WAAAA,YAAAtF,CAAA;MACAsC,OAAA,CAAAC,GAAA,CAAAvC,CAAA;MACA,KAAA8C,QAAA,CAAAS,SAAA,GAAAvD,CAAA,CAAAoB,KAAA,IAAAmE,QAAA;MACA,KAAAzC,QAAA,CAAAuC,OAAA,GAAArF,CAAA,CAAAoB,KAAA,IAAAiE,OAAA;MACA,KAAAxB,SAAA;IACA;IACA;IACA2B,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACAC,GAAA,CAAAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAtB,YAAA,CAAAuB,OAAA;QACAjD,IAAA,OAAAC,QAAA;QACAiD,MAAA;MACA,GAAArB,IAAA,WAAAC,GAAA;QACAe,GAAA,CAAAM,WAAA;QACA,IAAArB,GAAA,CAAAK,IAAA;UACAS,MAAA,CAAAQ,EAAA,CAAAC,KAAA;UACAC,UAAA;YACAT,GAAA,CAAAU,YAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAZ,GAAA,CAAAM,WAAA;MACA;IACA;IACA;IACAO,SAAA,WAAAA,UAAA;MACAjE,OAAA,CAAAC,GAAA;MACA,IAAAiE,GAAA,QAAAC,GAAA;MACAf,GAAA,CAAAgB,YAAA;QACAC,IAAA,GAAAH,GAAA;QACAI,gBAAA;UACAC,OAAA,WAAAA,QAAAhE,IAAA;YACAP,OAAA,CAAAC,GAAA,CAAAM,IAAA;UACA;UACAiE,IAAA,WAAAA,KAAAR,GAAA;YACAhE,OAAA,CAAAC,GAAA,CAAA+D,GAAA,CAAAS,MAAA;UACA;QACA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA,GAAAxB,GAAA,CAAAyB,cAAA;MACAzB,GAAA,CAAA0B,WAAA;QACAC,KAAA;QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QAAA;QACAV,OAAA,WAAAA,QAAAlC,GAAA;UACArC,OAAA,CAAAC,GAAA,CAAAoC,GAAA;UACAe,GAAA,CAAAC,WAAA;YACAC,IAAA;YACAC,KAAA;UACA;UACA,IAAA2B,aAAA,GAAA7C,GAAA,CAAA6C,aAAA;UACA9B,GAAA,CAAA+B,UAAA;YACAjB,GAAA,EAAAS,MAAA,CAAAS,UAAA;YACAC,QAAA,EAAAH,aAAA;YACAI,IAAA;YACAC,MAAA;cACAC,aAAA,EAAAZ;YACA;YACAL,OAAA,WAAAA,QAAAkB,IAAA;cACAzF,OAAA,CAAAC,GAAA,CAAAwF,IAAA,CAAAlF,IAAA;cACA,IAAAmF,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,IAAA,CAAAlF,IAAA;cACAoE,MAAA,CAAAnE,QAAA,CAAAO,KAAA,GAAA2E,OAAA,CAAAG,MAAA;YACA;YACArB,IAAA,WAAAA,KAAAR,GAAA;cACAhE,OAAA,CAAAC,GAAA,CAAA+D,GAAA;YACA;YACA8B,QAAA,WAAAA,SAAA;cACA1C,GAAA,CAAAM,WAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;;;;;;;;;;AC7PA;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;ACAmI;AACnI;AACA,CAA8D;AACL;AACzD,CAA+F;;;AAG/F;AACgI;AAChI,gBAAgB,4IAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBwe,CAAC,+DAAe,8dAAG,EAAC;;;;;;;;;;;;;;;;;ACAqd,CAAC,+DAAe,85BAAG,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAp/BxG,mBAAA;AAGA,IAAA6I,IAAA,GAAA3F,sBAAA,CAAAlD,mBAAA;AACA,IAAA8I,IAAA,GAAA5F,sBAAA,CAAAlD,mBAAA;AAAuE,SAAAkD,uBAAA1C,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAA4C,UAAA,GAAA5C,CAAA,KAAAoC,OAAA,EAAApC,CAAA;AAHvE;AACAuI,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC;;;;;;;;;;;;;;;;;;;ACL4G;AAC5H;AACA,CAAuD;AACL;AAClD,CAAwF;;;AAGxF;AACsI;AACtI,gBAAgB,4IAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvB+f,CAAC,+DAAe,udAAG,EAAC;;;;;;;;;;;;;;;;;ACA6e,CAAC,+DAAe,u5BAAG,EAAC", "sources": ["webpack:///./src/layout/theme-wrap.vue?8473", "webpack:///./src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue?4935", "uni-app:///src/layout/theme-wrap.vue", "uni-app:///src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue", "webpack:///./src/layout/theme-wrap.vue?ddc8", "webpack:///./src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue?2b1a", "webpack:///./src/layout/theme-wrap.vue?e3fa", "webpack:///./src/layout/theme-wrap.vue?8af5", "webpack:///./src/layout/theme-wrap.vue?afdb", "uni-app:///src/main.js", "webpack:///./src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue?c533", "webpack:///./src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue?53bd", "webpack:///./src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue?580f", "webpack:///./src/pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue?7e47"], "sourcesContent": ["var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"content\", {\n      logo: _vm.themeConfig.logo,\n      bgColor: _vm.themeConfig.baseBgColor,\n      color: _vm.themeConfig.baseColor,\n      buttonBgColor: _vm.themeConfig.buttonBgColor,\n      buttonTextColor: _vm.themeConfig.buttonTextColor,\n      buttonLightBgColor: _vm.themeConfig.buttonLightBgColor,\n      navBarColor: _vm.themeConfig.navBarColor,\n      navBarTextColor: _vm.themeConfig.navBarTextColor,\n      couponColor: _vm.themeConfig.couponColor,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    \"u-Image\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--image/u--image\" */ \"uview-ui/components/u--image/u--image.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--textarea/u--textarea\" */ \"uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"19d26eec-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"19d26eec-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"19d26eec-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"19d26eec-1\", \"content\") : null\n  var f0 = m0 ? _vm._f(\"Img\")(_vm.formList.cover) : null\n  var m3 = m0 ? _vm.$getSSP(\"19d26eec-1\", \"content\") : null\n  var m4 = m0 ? _vm.$getSSP(\"19d26eec-1\", \"content\") : null\n  var m5 = m0 ? _vm.$getSSP(\"19d26eec-1\", \"content\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n    _vm.e1 = function ($event) {\n      _vm.showType = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showCoach = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showType = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showCoach = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        f0: f0,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <view\n    class=\"theme-wrap u-relative\"\n    :style=\"{\n      '--base-bg-color': themeConfig.baseBgColor,\n      '--base-color': themeConfig.baseTextColor,\n      '--button-bg-color': themeConfig.buttonBgColor,\n      '--button-text-color': themeConfig.buttonTextColor,\n      '--button-light-bg-color': themeConfig.buttonLightBgColor,\n      '--scroll-item-bg-color': themeConfig.scrollItemBgColor,\n      'padding-bottom': isTab?'180rpx':'0',\n      '--navbar-color': themeConfig.navBarColor\n    }\"\n  >\n    <slot\n      name=\"content\"\n      :logo=\"themeConfig.logo\"\n      :bgColor=\"themeConfig.baseBgColor\"\n      :color=\"themeConfig.baseColor\"\n      :buttonBgColor=\"themeConfig.buttonBgColor\"\n      :buttonTextColor=\"themeConfig.buttonTextColor\"\n      :buttonLightBgColor=\"themeConfig.buttonLightBgColor\"\n      :navBarColor=\"themeConfig.navBarColor\"\n      :navBarTextColor=\"themeConfig.navBarTextColor\"\n      :couponColor=\"themeConfig.couponColor\"\n    ></slot>\n  </view>\n</template>\n<script>\nimport { mapGetters } from \"vuex\";\nexport default {\n  computed: {\n    ...mapGetters([\"themeConfig\"]),\n  },\n  props: {\n    isTab:{\n      type: Boolean,\n      default: false\n    }\n  },\n  mounted() {\n    console.log(this.themeConfig);\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.theme-wrap {\n  min-height: 100vh;\n  width: 100vw;\n  background: var(--base-bg-color);\n}\n</style>\n", "<template>\n  <themeWrap>\n    <template #content=\"{navBarColor,navBarTextColor,buttonLightBgColor,buttonTextColor,couponColor}\">\n      <view>\n        <!-- 顶部菜单栏 -->\n        <u-navbar title=\"编辑会员卡\" @rightClick=\"uni.navigateBack()\" :titleStyle=\"{ color: navBarTextColor }\"\n          :bgColor=\"navBarColor\" :leftIconColor=\"navBarTextColor\" :autoBack=\"true\" :placeholder=\"true\"\n          :safeAreaInsetTop=\"true\">\n        </u-navbar>\n      </view>\n      <!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->\n      <view class=\"formView\">\n        <view class=\"formList\">\n          <view>会员卡名称:</view>\n          <u--input :border=\"false\" v-model=\"formList.cardName\"></u--input>\n        </view>\n        <view class=\"formList\">\n          <view>会员卡类型:</view>\n          <view class=\"u-flex-1\" @click=\"showType = true\">\n            <u--input :border=\"false\" disabled v-model=\"formList.cardTypeName\"></u--input>\n          </view>\n        </view>\n        <view class=\"formList\" v-if=\"showJL\">\n          <view>绑定教练:</view>\n          <view class=\"u-flex-1\" @click=\"showCoach = true\">\n            <u--input :border=\"false\" disabled v-model=\"formList.coachName\"></u--input>\n          </view>\n        </view>\n        <!-- 会员卡图片 -->\n        <view class=\"formTextarea border-8\">\n          <view class=\"textareaTitle u-flex\">\n            <view class=\"u-flex-1\">会员卡图片:</view>\n            <u-icon name=\"photo\" color=\"#000\" size=\"28\" @click=\"choseLogo\"></u-icon>\n          </view>\n          <view class=\"formLogo\">\n            <u--image :showLoading=\"true\" :src=\"formList.cover | Img\" width=\"240rpx\" height=\"160rpx\" radius=\"4\"\n              @click=\"clickLogo\"></u--image>\n          </view>\n        </view>\n        <view class=\"formList\">\n          <view>有效天数:</view>\n          <u--input :border=\"false\" v-model=\"formList.validDays\"></u--input>\n        </view>\n        <view class=\"formList\">\n          <view>有效次数:</view>\n          <u--input :border=\"false\" v-model=\"formList.validTimes\"></u--input>\n        </view>\n        <view class=\"formList\">\n          <view>会员卡价格:</view>\n          <u--input :border=\"false\" v-model=\"formList.cardPrice\"></u--input>\n        </view>\n        <view class=\"formTextarea border-8\">\n          <view class=\"textareaTitle u-flex\">\n            <view class=\"u-flex-1\">会员卡描述:</view>\n          </view>\n          <view class=\"formLogo\">\n            <u--textarea v-model=\"formList.description\" placeholder=\"请输入内容\" count :maxlength=\"250\"></u--textarea>\n          </view>\n        </view>\n        <!-- <view class=\"formTextarea border-8\" style=\"margin-bottom: 60px;\">\n          <view class=\"textareaTitle u-flex\">\n            <view class=\"u-flex-1\">会员卡合同:</view>\n          </view>\n          <view class=\"formLogo formLogo1\">\n            <u--textarea v-model=\"formList.contract\" placeholder=\"请输入内容\" count :maxlength=\"5000\"></u--textarea>\n          </view>\n        </view> -->\n      </view>\n      <!-- 选择会员卡类型 -->\n      <u-picker :show=\"showType\" :columns=\"actions\" keyName=\"dictLabel\" @confirm=\"typeSelect\" @cancel=\"showType = false\"></u-picker>\n      <!-- 选择教练 -->\n      <u-picker :show=\"showCoach\" :columns=\"coachActions\" keyName=\"nickName\" @confirm=\"coachSelect\" @cancel=\"showCoach = false\"></u-picker>\n      <!-- 最下面按钮 -->\n      <view class=\"bottonBtn u-flex\">\n        <view class=\"saveBtn\" @click=\"saveCoupon()\"\n          :style=\"{'background': buttonLightBgColor, color: buttonTextColor, 'border-color': buttonLightBgColor}\">新增\n        </view>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\n  import api from \"@/common/api\";\n  import themeWrap from '../../../layout/theme-wrap.vue';\n  export default {\n    data() {\n      return {\n        formList: {\n          shopId: '',\n          cardName: '',\n          cardTypeName: '',\n          validDays: '',\n          validTimes: '',\n          cardPrice: '',\n          cover: '',\n          description: '',\n          coachName: '',\n          contract: ''\n        },\n        showType: false,\n        actions: [],\n        memberCardId: '',\n        cardTypeList: [],\n        showCoach: false,\n        coachActions: [],\n        showJL: true,\n      }\n    },\n    onLoad(obj) {\n      this.formList.shopId = obj.shopId;\n      console.log(obj);\n      // 教练只能创建私教卡\n      if (obj.coach == 1) {\n        this.actions = [\n          [{\n            dictLabel: '私教课',\n            dictValue: '2'\n          }]\n        ]\n        this.cardTypeList = [{\n          dictLabel: '私教课',\n          dictValue: '2'\n        }]\n        this.formList.cardTypeName = '私教课';\n        this.formList.cardType = '2';\n        this.showJL = false;\n      } else {\n        api.getDataType({\n          dictType: 'dict_member_card_type'\n        }).then((res) => {\n          this.cardTypeList = res.data;\n          this.actions = [res.data];\n        })\n        this.getCoachList();\n      }\n    },\n    methods: {\n      // 获取教练列表\n      getCoachList() {\n        api.getcoachList({\n          data: {\n            shopId: this.formList.shopId,\n          },\n        }).then((res) => {\n          if (res.code == 200) {\n            console.log(res.rows);\n            this.coachActions = [res.rows];\n          }\n        });\n      },\n      cardType(val) {\n        this.cardTypeList.forEach(list => {\n          if (list.dictValue == val) {\n            return list.dictLabel\n          }\n        })\n        return '其他'\n      },\n      typeSelect(e) {\n        console.log(e);\n        this.formList.cardTypeName = e.value[0].dictLabel\n        this.formList.cardType = e.value[0].dictValue\n        // 如果是会员卡，私教课信息清空\n        if (e.value[0].dictValue == '1') {\n          this.formList.coachName = ''\n          this.formList.coachId = ''\n          this.showJL = false\n        } else {\n          this.showJL = true\n        }\n        this.showType = false\n      },\n      coachSelect(e) {\n        console.log(e);\n        this.formList.coachName = e.value[0].nickName\n        this.formList.coachId = e.value[0].coachId\n        this.showCoach = false\n      },\n      // 保存会员卡\n      saveCoupon() {\n        uni.showLoading({\n          mask: true,\n          title: '新增会员卡中，请稍后……'\n        })\n        api.putCard({\n          data: this.formList,\n          method: 'POST'\n        }).then((res) => {\n          uni.hideLoading();\n          if (res.code == 200) {\n            this.$u.toast(\"新增成功！\")\n            setTimeout(() => {\n              uni.navigateBack()\n            }, 2000)\n          }\n        }).catch((err) => {\n          uni.hideLoading();\n        })\n      },\n      // 点击图片\n      clickLogo() {\n        console.log(1232);\n        let url = this.src\n        uni.previewImage({\n          urls: [url],\n          longPressActions: {\n            success: function(data) {\n              console.log(data);\n            },\n            fail: function(err) {\n              console.log(err.errMsg);\n            }\n          }\n        });\n      },\n      // 选择场馆LOGO\n      choseLogo() {\n        let token = uni.getStorageSync(\"token\");\n        uni.chooseImage({\n          count: 1, //默认9\n          sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\n          sourceType: ['album'], //从相册选择\n          success: (res) => {\n            console.log(res);\n            uni.showLoading({\n              mask: true,\n              title: '正在上传中……请稍后'\n            })\n            const tempFilePaths = res.tempFilePaths\n            uni.uploadFile({\n              url: this.$serverUrl + '/shop/shop/upload/logo',\n              filePath: tempFilePaths[0],\n              name: 'logo',\n              header: {\n                Authorization: token\n              },\n              success: (succ) => {\n                console.log(succ.data);\n                let datamsg = JSON.parse(succ.data);\n                this.formList.cover = datamsg.imgUrl\n              },\n              fail: (err) => {\n                console.log(err);\n              },\n              complete() {\n                uni.hideLoading();\n              }\n            });\n          }\n        });\n      },\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .bottonBtn {\n    height: 160rpx;\n    width: 750rpx;\n    position: fixed;\n    bottom: 0;\n\n    .saveBtn {\n      width: 660rpx;\n      height: 80rpx;\n      margin: 0 auto;\n      text-align: center;\n      line-height: 80rpx;\n      border: 1px solid;\n      border-radius: 40rpx;\n    }\n  }\n\n  .formView {\n    padding: 30rpx 40rpx;\n\n    .formList {\n      background-color: #fafafa;\n      display: flex;\n      align-items: center;\n      flex-direction: row;\n      box-sizing: border-box;\n      padding: 10rpx 30rpx;\n      font-size: 30rpx;\n      color: #303133;\n      align-items: center;\n      border: 2rpx solid #d6d7d9;\n      margin-bottom: 20rpx;\n    }\n\n    .formTextarea {\n      border: 2rpx solid #e6e6e6;\n      margin-bottom: 20rpx;\n\n      .textareaTitle {\n        border-radius: 8rpx 8rpx 0 0;\n        padding: 10rpx 30rpx;\n        background-color: #e6e6e6;\n      }\n      .formLogo1 {\n\n\n      }\n      .formLogo {\n        margin: 20rpx;\n        flex-wrap: wrap;\n\n        .imgView {\n          width: 200rpx;\n          height: 120rpx;\n          position: relative;\n          margin-right: 10rpx;\n          margin-bottom: 20rpx;\n\n          ::v-deep .u-icon--right {\n            position: absolute;\n            z-index: 999;\n            top: -10rpx;\n            right: -10rpx;\n          }\n        }\n      }\n    }\n  }\n</style>\n", "// extracted by mini-css-extract-plugin", "// extracted by mini-css-extract-plugin", "import { render, staticRenderFns, recyclableRender, components } from \"./theme-wrap.vue?vue&type=template&id=7a7df696&scoped=true&\"\nvar renderjs\nimport script from \"./theme-wrap.vue?vue&type=script&lang=js&\"\nexport * from \"./theme-wrap.vue?vue&type=script&lang=js&\"\nimport style0 from \"./theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a7df696\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"layout/theme-wrap.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true&\"", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add.vue?vue&type=template&id=42e5bd5a&scoped=true&\"\nvar renderjs\nimport script from \"./add.vue?vue&type=script&lang=js&\"\nexport * from \"./add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add.vue?vue&type=style&index=0&id=42e5bd5a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"42e5bd5a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages-admin/huiYuanGuanLi/huiYuanKaLeiXing/add.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&id=42e5bd5a&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&id=42e5bd5a&scoped=true&lang=scss&\"", "export * from \"-!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=template&id=42e5bd5a&scoped=true&\""], "names": ["_vuex", "require", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "computed", "mapGetters", "props", "isTab", "type", "Boolean", "default", "mounted", "console", "log", "themeConfig", "_api", "_interopRequireDefault", "_themeWrap", "__esModule", "data", "formList", "shopId", "cardName", "cardTypeName", "validDays", "validTimes", "cardPrice", "cover", "description", "<PERSON><PERSON><PERSON>", "contract", "showType", "actions", "memberCardId", "cardTypeList", "showCoach", "coachActions", "showJL", "onLoad", "obj", "_this", "coach", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "cardType", "api", "getDataType", "dictType", "then", "res", "getCoachList", "methods", "_this2", "getcoachList", "code", "rows", "val", "list", "typeSelect", "coachId", "coachSelect", "nick<PERSON><PERSON>", "saveCoupon", "_this3", "uni", "showLoading", "mask", "title", "putCard", "method", "hideLoading", "$u", "toast", "setTimeout", "navigateBack", "catch", "err", "clickLogo", "url", "src", "previewImage", "urls", "longPressActions", "success", "fail", "errMsg", "<PERSON><PERSON><PERSON>", "_this4", "token", "getStorageSync", "chooseImage", "count", "sizeType", "sourceType", "tempFilePaths", "uploadFile", "$serverUrl", "filePath", "name", "header", "Authorization", "succ", "datamsg", "JSON", "parse", "imgUrl", "complete", "_vue", "_add", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}