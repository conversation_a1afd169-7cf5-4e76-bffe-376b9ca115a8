<view class="u-toast data-v-60b6a9e7"><u-overlay vue-id="255e32ed-1" show="{{isShow}}" custom-style="{{overlayStyle}}" class="data-v-60b6a9e7" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['u-toast__content','data-v-60b6a9e7','u-type-'+tmpConfig.type,tmpConfig.type==='loading'||tmpConfig.loading?'u-toast__content--loading':'']}}" style="{{$root.s0}}"><block wx:if="{{tmpConfig.type==='loading'}}"><u-loading-icon vue-id="{{('255e32ed-2')+','+('255e32ed-1')}}" mode="circle" color="rgb(255, 255, 255)" inactiveColor="rgb(120, 120, 120)" size="25" class="data-v-60b6a9e7" bind:__l="__l"></u-loading-icon></block><block wx:else><block wx:if="{{tmpConfig.type!=='defalut'&&iconName}}"><u-icon vue-id="{{('255e32ed-3')+','+('255e32ed-1')}}" name="{{iconName}}" size="17" color="{{tmpConfig.type}}" customStyle="{{iconStyle}}" class="data-v-60b6a9e7" bind:__l="__l"></u-icon></block></block><block wx:if="{{tmpConfig.type==='loading'||tmpConfig.loading}}"><u-gap vue-id="{{('255e32ed-4')+','+('255e32ed-1')}}" height="12" bgColor="transparent" class="data-v-60b6a9e7" bind:__l="__l"></u-gap></block><text class="{{['u-toast__content__text','data-v-60b6a9e7','u-toast__content__text--'+tmpConfig.type]}}" style="max-width:400rpx;">{{tmpConfig.message}}</text></view></u-overlay></view>