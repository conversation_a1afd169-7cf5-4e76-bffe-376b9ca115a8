(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-icon/u-icon"],{25440:function(){},78278:function(t,e,i){"use strict";var n;i.r(e),i.d(e,{__esModule:function(){return s.__esModule},default:function(){return f}});var u,l=function(){var t=this,e=t.$createElement,i=(t._self._c,t.isImg?t.__get_style([t.imgStyle,t.$u.addStyle(t.customStyle)]):null),n=t.isImg?null:t.__get_style([t.iconStyle,t.$u.addStyle(t.customStyle)]),u=""!==t.label?t.$u.addUnit(t.labelSize):null,l=""!==t.label&&"right"==t.labelPos?t.$u.addUnit(t.space):null,o=""!==t.label&&"bottom"==t.labelPos?t.$u.addUnit(t.space):null,s=""!==t.label&&"left"==t.labelPos?t.$u.addUnit(t.space):null,a=""!==t.label&&"top"==t.labelPos?t.$u.addUnit(t.space):null;t.$mp.data=Object.assign({},{$root:{s0:i,s1:n,g0:u,g1:l,g2:o,g3:s,g4:a}})},o=[],s=i(93329),a=s["default"],c=i(25440),d=i.n(c),r=(d(),i(18535)),h=(0,r["default"])(a,l,o,!1,null,"1b4719dc",null,!1,n,u),f=h.exports},93329:function(t,e,i){"use strict";var n=i(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var u=o(i(59027)),l=o(i(20263));function o(t){return t&&t.__esModule?t:{default:t}}e["default"]={name:"u-icon",data:function(){return{}},mixins:[n.$u.mpMixin,n.$u.mixin,l.default],computed:{uClasses:function(){var t=[];return t.push(this.customPrefix+"-"+this.name),this.color&&n.$u.config.type.includes(this.color)&&t.push("u-icon__icon--"+this.color),t},iconStyle:function(){var t={};return t={fontSize:n.$u.addUnit(this.size),lineHeight:n.$u.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:n.$u.addUnit(this.top)},this.color&&!n.$u.config.type.includes(this.color)&&(t.color=this.color),t},isImg:function(){return-1!==this.name.indexOf("/")},imgStyle:function(){var t={};return t.width=this.width?n.$u.addUnit(this.width):n.$u.addUnit(this.size),t.height=this.height?n.$u.addUnit(this.height):n.$u.addUnit(this.size),t},icon:function(){return u.default["uicon-"+this.name]||this.name}},methods:{clickHandler:function(t){this.$emit("click",this.index),this.stop&&this.preventEvent(t)}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-icon/u-icon-create-component"],{},function(t){t("81715")["createComponent"](t(78278))}]);