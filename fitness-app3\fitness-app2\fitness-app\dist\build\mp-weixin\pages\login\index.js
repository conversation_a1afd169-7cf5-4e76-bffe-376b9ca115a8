(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/login/index"],{22727:function(e,n,o){"use strict";var t=o(51372)["default"],i=o(81715)["createPage"];o(96910);s(o(923));var u=s(o(69309));function s(e){return e&&e.__esModule?e:{default:e}}t.__webpack_require_UNI_MP_PLUGIN__=o,i(u.default)},52213:function(){},68934:function(e,n,o){"use strict";var t=o(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var i=u(o(68466));o(77020);function u(e){return e&&e.__esModule?e:{default:e}}n["default"]={data:function(){return{bgImg:o(69562),src:"",btnLoading:!1,userName:"点击获取头像",userId:"",showFirst:!1,nickName:""}},components:{},onShow:function(){},onLoad:function(){this.wxLogin()},methods:{changeName:function(e){e.detail.value.trim()&&(this.nickName=e.detail.value)},wxLogin:function(){var e=this;t.login({provider:"weixin",success:function(n){t.getUserInfo({provider:"weixin",success:function(e){console.log("用户昵称为："+e.userInfo.nickName)}}),console.log("123132",n),i.default.login({data:{companyId:1,jsCode:n.code},method:"GET"}).then((function(n){console.log(n),200==n.code&&(t.setStorageSync("token",n.token),n.isNew,e.getUserInfo())}))}})},getUserInfo:function(){var e=this;i.default.getInfo({data:{companyId:1},method:"GET"}).then((function(n){console.log(n),200==n.code&&(e.userId=n.user.userId,t.setStorageSync("userInfo",n.user),t.setStorageSync("wxUserInfo",n.wxUser),t.setStorageSync("userRoles",n.roles),t.reLaunch({url:"/pages/index/index"}))})).catch((function(e){t.reLaunch({url:"/pages/index/index"})}))},goIndex:function(){t.reLaunch({url:"/pages/index/index"})},confirm:function(){var e=this;console.log(789),""!=this.nickName?(this.showFirst=!1,t.showLoading({mask:!0,title:"修改信息中……"}),i.default.putUser({data:{nickName:this.nickName},method:"PUT"}).then((function(n){console.log(n),t.hideLoading(),e.getUserInfo()})).catch((function(n){t.hideLoading(),e.$refs.uNotify.show({message:"修改失败!",type:"error",duration:"1500"}),e.getUserInfo()}))):this.$u.toast("昵称不能为空！")},cancel:function(){this.getUserInfo()},bindGetUserInfo:function(e){console.log(e)},bindAvatar:function(e){console.log(e);var n=t.getStorageSync("token");t.uploadFile({url:this.$serverUrl+"shop/shop/upload/logo",filePath:e.detail.avatarUrl,name:"logo",header:{Authorization:n},success:function(e){console.log(e.data);var n=JSON.parse(e.data),o=n.imgUrl;i.default.putUser({data:{memberAvatar:o},method:"PUT"}).then((function(e){console.log(e)}))},fail:function(e){console.log(e)},complete:function(){}}),this.src=e.detail.avatarUrl},bind:function(e){console.log(e),this.btnLoading=!0,"getPhoneNumber:fail user deny"==e.detail.errMsg?(this.btnLoading=!1,this.$refs.uToast.show({message:"绑定失败",type:"error",duration:"2300"})):this.btnLoading=!1}}}},69309:function(e,n,o){"use strict";o.r(n),o.d(n,{__esModule:function(){return r.__esModule},default:function(){return g}});var t,i={uNoNetwork:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-no-network/u-no-network")]).then(o.bind(o,43763))},uToast:function(){return o.e("node-modules/uview-ui/components/u-toast/u-toast").then(o.bind(o,67559))},uNotify:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-notify/u-notify")]).then(o.bind(o,11380))},"u-Image":function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u--image/u--image")]).then(o.bind(o,84027))}},u=function(){var e=this,n=e.$createElement;e._self._c},s=[],r=o(68934),a=r["default"],c=o(52213),l=o.n(c),d=(l(),o(18535)),f=(0,d["default"])(a,u,s,!1,null,null,null,!1,i,t),g=f.exports}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],(function(){return n(22727)}));e.O()}]);