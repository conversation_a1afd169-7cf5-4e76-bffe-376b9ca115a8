{"pages": ["pages/login/index", "pages/index/index", "pages/login/authorize", "pages/login/authorize_xcx", "pages/yu-yue/index", "pages/yu-yue/detail", "pages/yu-yue/tuanKe", "pages/yu-yue/success", "pages/jiaoLian/list", "pages/jiaoLian/detail", "pages/huiYuanKa/index", "pages/tizheng/index", "pages/huiYuanKa/detail", "pages/changGuan/index", "pages/user/index", "pages/user/xunLianJianYi", "pages/user/yu-yue/index", "pages/user/yu-yue/yu-yue-manage", "pages/user/yu-yue/tuanke", "pages/user/fenxiao/index", "pages/user/huiYuanKa", "pages/ruChang/qrcode", "pages/ruChang/history", "pages/user/edit", "pages/information/about", "pages/information/agreement", "pages/information/contract", "pages/information/question", "pages/notFind", "pages/user/signin", "pages/user/signinlist"], "subPackages": [{"root": "pages-admin", "pages": ["admin/index/index", "qiYeGuanLi/qiYeXinXiGuanLi/index", "xiTongGuanLi/yongHuGuanLi/index", "xiTongGuanLi/yongHuGuanLi/userDetails", "xiTongGuanLi/jiaGeSheZhi/index", "xiTongGuanLi/jiaGeSheZhi/details", "xiTongGuan<PERSON>i/jia<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/add", "qiYeGuanLi/qiYeXinXiGuanLi/details", "qiYeGuanLi/qiYeXinXiGuanLi/add", "qiYeGuanLi/qiYeJiaoFeiChaXun/index", "tuanKeGuanLi/index", "tuanKeGuanLi/create", "tuanKeGuanLi/detail", "tuanKeGuanLi/admin-list", "tuanKeGuanLi/week-index", "tuanKeGuanLi/check", "tuanKeGuanLi/edit", "huiYuanGuanLi/zaiJiHuiYuan/index", "huiYuanGuanLi/huiYuanGaiShu/index", "huiYuanGuan<PERSON>i/huiYuanGai<PERSON>hu/details", "huiYuanGuanLi/guoQiHuiYuan/index", "huiYuanGuanLi/huiYuanKaLeiXing/index", "huiYuanGuanLi/huiYuanKaLeiXing/edit", "huiYuanGuanLi/huiYuanKaLeiXing/add", "huiYuanGuanLi/huiYuanKaLeiXing/createVip", "huiYuanGuanLi/huiYuanFenPei/index", "huiYuanGuanLi/xuanZeYongHu/index", "siJiaoGuanLi/siJiaoYuYueGuanLi/index", "siJiaoGuanLi/keChengGuanLi/index", "siJiaoGuanLi/keChengGuanLi/details", "siJiaoGuanLi/keChengGuanLi/add", "siJiaoGuanLi/keChengFenLei/index", "siJiaoGuanLi/keChengFenLei/details", "siJiaoGuanLi/keChengFenLei/add", "siJiaoGuanLi/yuYueGuanLi/details", "siJiaoGuanLi/yuYueGuanLi/index", "si<PERSON>iaoGuan<PERSON>i/yu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/choseUser", "changGuanGuanLi/changGuanBianJi/index", "changGuanGuanLi/xinZengChangGuan/index", "changGuanGuanLi/yuanGongGuanLi/index", "changGuanGuan<PERSON>i/yuanGong<PERSON>uan<PERSON>i/quan<PERSON>ian<PERSON><PERSON>", "changGuanGuanLi/changGuanLieBiao/index", "changGuanGuanLi/jueSeGuanLi/index", "changGuanGuanLi/jueSeGuanLi/details", "jiaoLianGuanLi/index", "jiaoLianGuanLi/details", "jiaoLianGuan<PERSON>i/add", "jiaoLianGuanLi/huiyuankashezhi", "jiaoLianLeiXing/index", "jiaoLianLeiXing/add", "jiaoLianLeiXing/details", "zhiFuZhongXin/huiYuanKaGuanLi/index", "zhiFuZhongXin/huiYuanKaGuanLi/details", "huodongGuanli/index", "huodongGuanli/info", "huodongGuanli/huodongindex", "device/index", "device/info", "device/faceinfo", "device/faceindex"]}, {"root": "pages-b<PERSON><PERSON><PERSON>", "pages": ["zhiFuGuanLi/huiYuanKaBaoBiao/index_1", "zhiFuGuanLi/huiYuanKaBaoBiao/index", "huiYuanGuanLi/huiYuanBaoBiao/index", "caiWuBaoBiao/caiWuBaoBiao"]}], "window": {"navigationStyle": "custom", "navigationBarTextStyle": "white", "navigationBarTitleText": "健身预约", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8", "enablePullDownRefresh": false}, "tabBar": {"custom": true, "list": [{"pagePath": "pages/index/index"}, {"pagePath": "pages/jiaoLian/list"}, {"pagePath": "pages/yu-yue/index"}, {"pagePath": "pages/user/index"}]}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation"], "usingComponents": {"theme-wrap": "/layout/theme-wrap"}}