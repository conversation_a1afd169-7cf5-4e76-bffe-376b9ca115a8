<theme-wrap scoped-slots-compiler="augmented" vue-id="2a457929-1" class="data-v-6f3d8f53" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast vue-id="{{('2a457929-2')+','+('2a457929-1')}}" data-ref="toast" class="data-v-6f3d8f53 vue-ref" bind:__l="__l"></u-toast><view class="data-v-6f3d8f53"><u-navbar vue-id="{{('2a457929-3')+','+('2a457929-1')}}" title="签到页面" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-6f3d8f53" bind:__l="__l"></u-navbar></view><u-tabs vue-id="{{('2a457929-4')+','+('2a457929-1')}}" list="{{list1}}" data-event-opts="{{[['^click',[['clicktab']]]]}}" bind:click="__e" class="data-v-6f3d8f53" bind:__l="__l"></u-tabs><view class="calendar-wrapper data-v-6f3d8f53"><view class="flex alignCenter justifyBetween data-v-6f3d8f53"><view class="title data-v-6f3d8f53">往日签到</view><view class="header data-v-6f3d8f53"><view data-event-opts="{{[['tap',[['changeMonth',['pre']]]]]}}" class="pre data-v-6f3d8f53" bindtap="__e"></view><view class="data-v-6f3d8f53">{{y+'年'+$root.m3+'月'}}</view><view data-event-opts="{{[['tap',[['changeMonth',['next']]]]]}}" class="next data-v-6f3d8f53" bindtap="__e"></view></view></view><view class="week data-v-6f3d8f53"><block wx:for="{{weekDay}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="week-day data-v-6f3d8f53">{{item}}</view></block></view><view class="{{['content','data-v-6f3d8f53',(!monthOpen)?'hide':'']}}" style="{{'height:'+(height)+';'}}"><view class="days data-v-6f3d8f53" style="{{'top:'+(positionTop+'rpx')+';'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-6f3d8f53"><view data-event-opts="{{[['tap',[['selectOne',['$0','$event'],[[['dates','',index]]]]]]]}}" class="{{['day','data-v-6f3d8f53',(item.m4)?'choose':'',(!item.$orig.isCurM)?'nolm':'',(item.m5)?'today':'',(item.m6)?'isWorkDay':'']}}" bindtap="__e">{{''+item.m7+''}}</view></view></block></view></view><block wx:if="{{collapsible}}"><image class="{{['weektoggle','data-v-6f3d8f53',(monthOpen)?'down':'']}}" src="https://i.loli.net/2020/07/16/2MmZsucVTlRjSwK.png" mode="scaleToFill" data-event-opts="{{[['tap',[['toggle',['$event']]]]]}}" bindtap="__e"></image></block></view></view></theme-wrap>