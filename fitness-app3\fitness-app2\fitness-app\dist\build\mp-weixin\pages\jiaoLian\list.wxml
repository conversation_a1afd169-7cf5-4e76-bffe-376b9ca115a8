<theme-wrap scoped-slots-compiler="augmented" vue-id="6bf60759-1" class="data-v-1fe42cc4" bind:__l="__l" vue-slots="{{['content']}}"><view style="background-color:#fff;" class="data-v-1fe42cc4" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('6bf60759-2')+','+('6bf60759-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="教练列表" safeAreaInsetTop="{{true}}" class="data-v-1fe42cc4" bind:__l="__l" vue-slots="{{['left']}}"><view class="data-v-1fe42cc4" slot="left"></view></u-navbar><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-1fe42cc4"><view class="searchView u-flex u-p-r-40 data-v-1fe42cc4" style="{{'padding-right:100px !important;'+('background:'+('#fff')+';')}}"><text class="font-bold u-font-36 u-m-r-30 data-v-1fe42cc4">选个教练</text><u-search vue-id="{{('6bf60759-3')+','+('6bf60759-1')}}" height="24" showAction="{{false}}" placeholder="请输入教练名字" animation="{{true}}" data-event-opts="{{[['^search',[['search']]]]}}" bind:search="__e" class="data-v-1fe42cc4" bind:__l="__l"></u-search></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" catchtap="__e" class="data-v-1fe42cc4"><view class="u-flex w-100 bg-fff u-p-r-20 u-p-l-20 data-v-1fe42cc4" style="font-size:12px;"><view data-event-opts="{{[['tap',[['showFilter',['genderShow']]]]]}}" class="u-flex-1 u-flex u-row-center u-col-center u-p-t-10 u-p-b-10 u-border-right data-v-1fe42cc4" catchtap="__e">{{''+gender_text+''}}<view class="{{['u-m-l-10','tra','data-v-1fe42cc4',genderShow?'upTurn':'downTurn']}}"><u-icon vue-id="{{('6bf60759-4')+','+('6bf60759-1')}}" bold="{{true}}" name="play-right-fill" color="{{genderShow?'#dd4d51':'#666'}}" size="15" class="data-v-1fe42cc4" bind:__l="__l"></u-icon></view></view><view data-event-opts="{{[['tap',[['showFilter',['filterShow']]]]]}}" class="u-flex-1 u-flex u-row-center u-col-center data-v-1fe42cc4" catchtap="__e">筛选<view class="{{['u-m-l-10','tra','data-v-1fe42cc4',filterShow?'upTurn':'downTurn']}}"><u-icon vue-id="{{('6bf60759-5')+','+('6bf60759-1')}}" bold="{{true}}" name="play-right-fill" color="{{filterShow?'#dd4d51':'#666'}}" size="15" class="data-v-1fe42cc4" bind:__l="__l"></u-icon></view></view></view><view hidden="{{!(genderShow)}}" class="searchCurtain data-v-1fe42cc4"><view class="bg-fff u-p-34 w-100 data-v-1fe42cc4" style="position:absolute;z-index:999;right:0;border-radius:0 0 50rpx 50rpx;"><u-radio-group bind:input="__e" vue-id="{{('6bf60759-6')+','+('6bf60759-1')}}" placement="column" iconPlacement="right" activeColor="#000" value="{{sex}}" data-event-opts="{{[['^input',[['__set_model',['','sex','$event',[]]]]]]}}" class="data-v-1fe42cc4" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{radiolist1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('6bf60759-7-'+index)+','+('6bf60759-6')}}" customStyle="{{({margin:'8px'})}}" label="{{item.name}}" name="{{item.value}}" data-event-opts="{{[['^change',[['changeGender',[index]]]]]}}" bind:change="__e" class="data-v-1fe42cc4" bind:__l="__l"></u-radio></block></u-radio-group></view></view><view hidden="{{!(filterShow)}}" class="searchCurtain data-v-1fe42cc4"><view class="bg-fff u-p-r-34 u-p-l-34 overView data-v-1fe42cc4"><view class="data-v-1fe42cc4"><view class="u-font-28 font-bold u-p-b-20 data-v-1fe42cc4">课程</view><view class="filter-option-wrap data-v-1fe42cc4"><block wx:for="{{tagFilterList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeCourse',['$0'],[[['tagFilterList','',index,'courseId']]]]]]]}}" class="{{['u-font-26','option-item','u-relative','u-line-1','overflow-hidden','','data-v-1fe42cc4',(item.disabled)?'disabled':'',(queryForm['courseId']==item.courseId)?'active':'']}}" bindtap="__e"><view class="clamp data-v-1fe42cc4">{{''+item.courseName+''}}</view><view hidden="{{!(queryForm['courseId']==item.courseId)}}" class="u-absolute check-box u-flex u-row-end u-col-end data-v-1fe42cc4"><u-icon vue-id="{{('6bf60759-8-'+index)+','+('6bf60759-1')}}" name="checkmark" bold="{{true}}" color="#fec629" size="12" class="data-v-1fe42cc4" bind:__l="__l"></u-icon></view></view></block></view></view><view class="u-m-t-20 data-v-1fe42cc4"><view class="u-font-28 font-bold u-p-b-20 data-v-1fe42cc4">课程分类</view><view class="filter-option-wrap data-v-1fe42cc4" style="flex-wrap:wrap;"><block wx:for="{{tagFilterList2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeTarget',['$0'],[[['tagFilterList2','',index,'courseTypeId']]]]]]]}}" class="{{['u-font-26','option-item','u-relative','overflow-hidden','data-v-1fe42cc4',(item.disabled)?'disabled':'',(queryForm['courseTypeId']==item.courseTypeId)?'active':'']}}" bindtap="__e"><view class="clamp data-v-1fe42cc4">{{''+item.courseTypeName+''}}</view><view hidden="{{!(queryForm['courseTypeId']==item.courseTypeId)}}" class="u-absolute check-box u-flex u-row-end u-col-end data-v-1fe42cc4"><u-icon vue-id="{{('6bf60759-9-'+index)+','+('6bf60759-1')}}" name="checkmark" bold="{{true}}" color="#fec629" size="14" class="data-v-1fe42cc4" bind:__l="__l"></u-icon></view></view></block></view></view><view class="u-m-t-20 data-v-1fe42cc4"><view class="u-font-28 font-bold u-p-b-20 data-v-1fe42cc4">教练类型</view><view class="filter-option-wrap data-v-1fe42cc4" style="flex-wrap:wrap;"><block wx:for="{{tagList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeCoach',['$0'],[[['tagList','',index,'coachTypeId']]]]]]]}}" class="{{['u-font-26','option-item','u-relative','overflow-hidden','data-v-1fe42cc4',(item.disabled)?'disabled':'',(queryForm['coachTypeId']==item.coachTypeId)?'active':'']}}" bindtap="__e"><view class="clamp data-v-1fe42cc4">{{''+item.coachTypeName+''}}</view><view hidden="{{!(queryForm['coachTypeId']==item.coachTypeId)}}" class="u-absolute check-box u-flex u-row-end u-col-end data-v-1fe42cc4"><u-icon vue-id="{{('6bf60759-10-'+index)+','+('6bf60759-1')}}" name="checkmark" bold="{{true}}" color="#fec629" size="14" class="data-v-1fe42cc4" bind:__l="__l"></u-icon></view></view></block></view></view><view class="u-flex u-p-t-40 u-p-b-30 data-v-1fe42cc4"><view data-event-opts="{{[['tap',[['resetFilter',['$event']]]]]}}" class="u-flex-1 u-m-r-10 border-8 u-text-center u-font-30 font-bold u-p-t-16 u-p-b-16 data-v-1fe42cc4" style="border:2px solid #000;" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['handleFilter',['$event']]]]]}}" class="u-flex-2 u-m-l-20 border-8 fc-fff u-font-30 font-bold u-p-t-16 u-p-b-16 u-text-center data-v-1fe42cc4" style="background:#000;border:2px solid #000;" bindtap="__e">确定</view></view></view></view></view><view class="w-100 border-16 team-list data-v-1fe42cc4"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0>0}}"><view class="u-flex border-16 u-m-30 data-v-1fe42cc4" style="margin-top:96rpx !important;"><view class="w-100 u-flex u-row-between u-col-end border-16 u-relative data-v-1fe42cc4" style="max-height:260rpx;height:230rpx;background-color:#e0e0e0;"><view class="overflow-hidden u-relative data-v-1fe42cc4" style="width:30%;line-height:0;max-height:170px;"><block wx:if="{{item.$orig.coachPhotos}}"><image class="w-100 data-v-1fe42cc4" style="height:100%;" src="{{item.f0}}" lazy-load="{{true}}" mode="widthFix"></image></block><block wx:else><image class="w-100 data-v-1fe42cc4" style="height:100%;" src="/static/images/default/coach_photo.png" lazy-load="{{true}}" mode="widthFix"></image></block></view><view class="u-p-l-10 u-relative u-p-10 data-v-1fe42cc4" style="width:70%;align-self:flex-start;"><view class="u-p-r-10 u-flex data-v-1fe42cc4" style="align-items:center;"><u-icon vue-id="{{('6bf60759-11-'+index)+','+('6bf60759-1')}}" size="30" color="#000" name="account-fill" class="data-v-1fe42cc4" bind:__l="__l"></u-icon><text class="font-bold u-font-34 u-p-r-10 data-v-1fe42cc4">{{item.$orig.nickName}}</text></view><view class="u-p-r-10 u-p-t-20 u-p-b-20 w-100 u-flex no-wrap data-v-1fe42cc4"><view class="btn-blk fc-fff u-font-24 font-bold u-m-r-20 u-text-center border-8 data-v-1fe42cc4" style="background:#000;">{{''+(item.$orig.coachTypeName||"教练类型")+''}}</view><view style="width:1px;height:16px;background-color:#a19fcc;" class="data-v-1fe42cc4"></view><block wx:if="{{item.g1}}"><view class="overflow-hidden u-p-l-20 data-v-1fe42cc4"><view class="u-flex no-wrap data-v-1fe42cc4" style="overflow:scroll;"><block wx:for="{{item.$orig.specialtyList}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="border-8 u-m-r-10 u-m-l-10 text-no-wrap u-font-22 u-p-t-8 u-p-b-8 u-p-l-14 u-p-r-14 data-v-1fe42cc4" style="background-color:#d4d4d4;">{{''+i+''}}</view></block></view></view></block></view><view class="u-flex u-m-r-20 data-v-1fe42cc4" style="flex-direction:row-reverse;"><view data-event-opts="{{[['tap',[['goDetails',['$0'],[[['mingXingList','',index]]]]]]]}}" class="confirmBtn data-v-1fe42cc4" bindtap="__e">预约</view></view></view></view></view></block></block><block wx:if="{{$root.g2==0}}"><view style="height:400px;padding-top:100px;" class="data-v-1fe42cc4"><u-empty vue-id="{{('6bf60759-12')+','+('6bf60759-1')}}" mode="list" text="暂无教练" class="data-v-1fe42cc4" bind:__l="__l"></u-empty></view></block></view></view><zw-tab-bar vue-id="{{('6bf60759-13')+','+('6bf60759-1')}}" selIdx="{{1}}" bigIdx="{{2}}" class="data-v-1fe42cc4" bind:__l="__l"></zw-tab-bar></view></theme-wrap>