<theme-wrap scoped-slots-compiler="augmented" vue-id="2b1cb2b0-1" class="data-v-0dbc1dd4" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-tabs vue-id="{{('2b1cb2b0-2')+','+('2b1cb2b0-1')}}" list="{{tabList}}" name="title" lineColor="{{$root.m1['buttonLightBgColor']}}" activeStyle="{{({fontWeight:'bold'})}}" scrollable="{{false}}" current="{{current}}" data-event-opts="{{[['^change',[['changeTabs']]]]}}" bind:change="__e" class="data-v-0dbc1dd4" bind:__l="__l"></u-tabs><view class="u-flex u-p-b-10 u-border-bottom data-v-0dbc1dd4"><view class="w-100 u-flex-1 data-v-0dbc1dd4"><picker class="w-100 data-v-0dbc1dd4" mode="date" end="{{maxDate}}" value="{{maxDate}}" data-event-opts="{{[['change',[['changeDate',['$event','startDate']]]]]}}" bindchange="__e"><view class="u-tips-color u-flex w-100 u-row-center u-p-10 data-v-0dbc1dd4"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-line-1 u-p-r-10 data-v-0dbc1dd4" bindtap="__e">{{''+(form.startDate||"开始时间")+''}}</view><block wx:if="{{!form.endData}}"><u-icon vue-id="{{('2b1cb2b0-3')+','+('2b1cb2b0-1')}}" name="calendar" color="#999" size="18" class="data-v-0dbc1dd4" bind:__l="__l"></u-icon></block><block wx:else><u-icon vue-id="{{('2b1cb2b0-4')+','+('2b1cb2b0-1')}}" name="close-fill" color="#999" size="18" class="data-v-0dbc1dd4" bind:__l="__l"></u-icon></block></view></picker></view><view class="w-100 u-flex-1 data-v-0dbc1dd4"><picker class="w-100 data-v-0dbc1dd4" mode="date" end="{{maxDate}}" value="{{maxDate}}" data-event-opts="{{[['change',[['changeDate',['$event','endDate']]]]]}}" bindchange="__e"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-tips-color u-flex w-100 u-row-center u-p-10 data-v-0dbc1dd4" bindtap="__e"><view class="u-line-1 u-p-r-10 data-v-0dbc1dd4">{{''+(form.endDate||"结束时间")+''}}</view><block wx:if="{{!form.endData}}"><u-icon vue-id="{{('2b1cb2b0-5')+','+('2b1cb2b0-1')}}" name="calendar" color="#999" size="18" class="data-v-0dbc1dd4" bind:__l="__l"></u-icon></block><block wx:else><u-icon vue-id="{{('2b1cb2b0-6')+','+('2b1cb2b0-1')}}" name="close-fill" color="#999" size="18" class="data-v-0dbc1dd4" bind:__l="__l"></u-icon></block></view></picker></view></view><view class="data-v-0dbc1dd4"><echart vue-id="{{('2b1cb2b0-7')+','+('2b1cb2b0-1')}}" option="{{option}}" exportBase64="{{true}}" data-ref="chart" class="data-v-0dbc1dd4 vue-ref" bind:__l="__l"></echart><block wx:if="{{image}}"><image src="{{image}}" class="data-v-0dbc1dd4"></image></block></view></view></theme-wrap>