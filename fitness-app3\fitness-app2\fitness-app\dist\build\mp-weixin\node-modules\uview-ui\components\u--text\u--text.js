"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u--text/u--text"],{16605:function(e,n,t){var u=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var o=l(t(30537));function l(e){return e&&e.__esModule?e:{default:e}}var c=function(){Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-text/u-text")]).then(function(){return resolve(t(58693))}.bind(null,t))["catch"](t.oe)};n["default"]={name:"u--text",mixins:[u.$u.mpMixin,o.default,u.$u.mixin],components:{uvText:c}}},57665:function(e,n,t){var u;t.r(n),t.d(n,{__esModule:function(){return i.__esModule},default:function(){return d}});var o,l=function(){var e=this,n=e.$createElement;e._self._c},c=[],i=t(16605),a=i["default"],s=t(18535),r=(0,s["default"])(a,l,c,!1,null,null,null,!1,u,o),d=r.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u--text/u--text-create-component"],{},function(e){e("81715")["createComponent"](e(57665))}]);