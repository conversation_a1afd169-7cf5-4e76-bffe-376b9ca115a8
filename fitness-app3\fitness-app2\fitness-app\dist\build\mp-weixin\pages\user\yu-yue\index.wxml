<theme-wrap scoped-slots-compiler="augmented" vue-id="6b1ae7e0-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('6b1ae7e0-2')+','+('6b1ae7e0-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="我的预约" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="type-box w-100 u-p-t-20 u-p-b-20 nbc u-flex u-row-center u-col-center"><view style="width:35vw;"><u-subsection vue-id="{{('6b1ae7e0-3')+','+('6b1ae7e0-1')}}" list="{{typeList}}" mode="subsection" inactiveColor="{{$root.m2['buttonTextColor']}}" activeColor="{{$root.m3['buttonLightBgColor']}}" bgColor="{{$root.m4['navBarColor']}}" current="{{currentType}}" data-event-opts="{{[['^change',[['changeCurrentType']]]]}}" bind:change="__e" bind:__l="__l"></u-subsection></view><block wx:if="{{isAdmin}}"><view data-event-opts="{{[['tap',[['handleManage',['$event']]]]]}}" class="manage-btn" bindtap="__e">预约管理</view></block></view><block wx:if="{{!currentType}}"><u-tabs vue-id="{{('6b1ae7e0-4')+','+('6b1ae7e0-1')}}" list="{{tabList}}" scrollable="{{false}}" lineColor="{{$root.m5['buttonLightBgColor']}}" lineWidth="60rpx" activeStyle="{{$root.a1}}" current="{{currentTab}}" data-event-opts="{{[['^change',[['clickTab']]]]}}" bind:change="__e" bind:__l="__l"></u-tabs></block><view class="container u-p-t-40 u-p-b-40"><view hidden="{{!(!currentType)}}"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="u-p-40 bg-fff border-16 u-m-b-40"><view class="w-100 u-flex u-col-start u-p-b-20"><view class="u-flex-5"><view class="u-flex u-row-center"><view class="u-p-t-10 u-p-b-10 u-p-r-24 u-p-l-24 border-8 font-bold u-font-24" style="display:inline-block;color:var(--button-light-bg-color);border:2rpx solid var(--button-light-bg-color);">{{i.m6}}</view></view></view><view class="u-flex-11 u-p-l-20"><view class="u-font-36 font-bold u-m-b-10">{{''+i.$orig.courseName+''}}</view><view class="u-font-30 u-m-b-10">{{''+i.$orig.bookingTime+''}}</view><view class="u-font-30 u-m-b-10 u-flex w-100 u-line-1">{{'教练：'+i.$orig.coachName+''}}</view><view class="u-font-30 u-m-b-10 u-flex w-100 u-line-1">{{'学员：'+i.$orig.memberName+''}}</view><view class="u-font-28 u-m-b-10 u-tips-color u-flex w-100 u-line-1">{{''+i.$orig.shopName+''}}</view></view></view><view class="u-p-t-20 u-border-top u-flex u-row-end"><block wx:if="{{i.$orig.status=='0'}}"><view data-event-opts="{{[['tap',[['cancelAgree',['$0'],[[['list','',index,'memberBookingId']]]]]]]}}" class="lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">取消预约</view></block><block wx:if="{{i.$orig.status=='0'&&isCoach&&i.$orig.type==1}}"><view data-event-opts="{{[['tap',[['agree',['$0'],[[['list','',index,'memberBookingId']]]]]]]}}" class="u-m-l-20 lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">确认预约</view></block><block wx:if="{{i.$orig.status=='0'&&!isCoach&&i.$orig.type==2}}"><view data-event-opts="{{[['tap',[['userAgree',['$0'],[[['list','',index,'memberBookingId']]]]]]]}}" class="u-m-l-20 lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">确认预约</view></block><block wx:if="{{i.$orig.status=='1'&&!isCoach}}"><view data-event-opts="{{[['tap',[['cancelForce',['$0'],[[['list','',index]]]]]]]}}" class="lbc btc u-p-t-10 u-p-b-10 u-m-r-18 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">{{"强制取消\n\t\t\t\t\t\t\t\t\t"+(i.$orig.superCancelStatus==="0"?'（已提交）':i.$orig.superCancelStatus==="1"?'（已同意）':i.$orig.superCancelStatus==="2"?'（已拒绝）':'')+''}}</view></block><block wx:if="{{i.$orig.status=='1'}}"><view data-event-opts="{{[['tap',[['confirmQianDao',['$0'],[[['list','',index,'memberBookingId']]]]]]]}}" class="lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">签到</view></block></view></view></block></block><block wx:else><block wx:if="{{$root.g1}}"><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:300rpx;height:300rpx;" src="/static/images/empty/list.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无预约</view></view></block></block><u-loadmore vue-id="{{('6b1ae7e0-5')+','+('6b1ae7e0-1')}}" status="{{loadStatus}}" data-event-opts="{{[['^click',[['loadClick']]]]}}" bind:click="__e" bind:__l="__l"></u-loadmore></view><view hidden="{{!(currentType)}}"><block wx:if="{{$root.g2}}"><block wx:for="{{$root.l1}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="group-course-item"><block wx:if="{{item.background}}"><image class="bg" src="{{item.background}}" mode="widthFix"></image></block><block wx:else><image class="bg" src="/static/images/default/banner.png" mode="widthFix"></image></block><view class="content u-flex-11 u-p-l-20"><view class="group-course-title">{{i.$orig.title}}</view><view class="u-font-30 u-m-b-10">{{''+i.$orig.classTime+''}}</view><block wx:if="{{!isCoach}}"><view class="u-font-30 u-m-b-10 u-flex w-100 u-line-1">{{''+i.$orig.coachName+''}}</view></block><view class="u-font-30 u-m-b-10 u-flex w-100 u-line-1">{{'时长：'+(i.$orig.classLength||'-')+'分钟'}}</view><view class="u-flex u-font-26 u-p-t-10 u-p-b-10 text-no-wrap">{{''+(i.m7-i.m8)+"/"+(i.$orig.attendance||0)+''}}</view></view><block wx:if="{{isCoach}}"><view style="z-index:1;"><navigator class="group-course-btn group-course-btn-yuyue" url="{{'/pages/user/yu-yue/tuanke?courseId='+i.$orig.groupCourseId+'&title='+i.$orig.title}}">查看报名</navigator></view></block><block wx:else><view class="u-p-t-20 u-flex u-row-end" style="z-index:1;"><block wx:if="{{i.$orig.bookingStatus=='1'||i.$orig.bookingStatus=='2'}}"><view data-event-opts="{{[['tap',[['groupCancelAgree',['$0','$1'],[[['list','',index,'groupCourseId']],[['list','',index,'groupCourseBookingId']]]]]]]}}" class="lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" style="margin-right:10rpx;" bindtap="__e">取消预约</view></block><block wx:if="{{i.$orig.bookingStatus=='1'||i.$orig.bookingStatus=='2'}}"><view data-event-opts="{{[['tap',[['groupConfirmQianDao',['$0'],[[['list','',index,'groupCourseId']]]]]]]}}" class="lbc btc u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 font-bold u-font-28 border-8" bindtap="__e">签到</view></block></view></block></view></block></block><block wx:else><block wx:if="{{$root.g3}}"><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:300rpx;height:300rpx;" src="/static/images/empty/list.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无预约</view></view></block></block></view><view hidden="{{!(loading)}}"><block wx:for="{{$root.l3}}" wx:for-item="j" wx:for-index="idx" wx:key="idx"><view clas="u-p-t-40 u-p-b-40 u-font-36 font-bold"><view class="placeholder-w-1 u-m-b-40 u-m-t-40" style="height:50rpx;"></view><view class="border-16 bg-fff u-m-b-40 u-p-40 u-flex"><block wx:for="{{j.l2}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="u-p-r-10 u-p-l-10 u-flex-1"><view class="placeholder" style="aspect-ratio:3/2;"></view></view></block></view></view></block></view></view></view></theme-wrap>