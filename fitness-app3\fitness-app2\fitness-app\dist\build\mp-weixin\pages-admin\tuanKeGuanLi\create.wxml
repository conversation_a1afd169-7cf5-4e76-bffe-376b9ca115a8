<theme-wrap scoped-slots-compiler="augmented" vue-id="2364c77c-1" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast vue-id="{{('2364c77c-2')+','+('2364c77c-1')}}" data-ref="toast" class="data-v-265f964b vue-ref" bind:__l="__l"></u-toast><view class="data-v-265f964b"><u-navbar vue-id="{{('2364c77c-3')+','+('2364c77c-1')}}" title="编辑" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-265f964b" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-265f964b"><u-form vue-id="{{('2364c77c-4')+','+('2364c77c-1')}}" model="{{form}}" labelWidth="140" data-ref="uForm" class="data-v-265f964b vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-265f964b"><u-form-item vue-id="{{('2364c77c-5')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="title" label="团课标题" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-6')+','+('2364c77c-5')}}" inputAlign="right" border="none" placeholder="请输入团课标题" value="{{form.title}}" data-event-opts="{{[['^input',[['__set_model',['$0','title','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-7')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="attendance" label="最多人数" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-8')+','+('2364c77c-7')}}" inputAlign="right" type="number" border="none" placeholder="请输入上课人数上限" value="{{form.attendance}}" data-event-opts="{{[['^input',[['__set_model',['$0','attendance','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><block wx:if="{{!type}}"><u-form-item vue-id="{{('2364c77c-9')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classTime" label="课程日期" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-265f964b" style="{{'color:'+(classTimeListName?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(classTimeListName||'请选择课程日期')+''}}</view><u-calendar vue-id="{{('2364c77c-10')+','+('2364c77c-9')}}" show="{{showCalendar}}" mode="multiple" data-event-opts="{{[['^confirm',[['confirmCalendar']]],['^close',[['e2']]]]}}" bind:confirm="__e" bind:close="__e" class="data-v-265f964b" bind:__l="__l"></u-calendar></u-form-item></block><block wx:if="{{!type}}"><u-form-item vue-id="{{('2364c77c-11')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classTime" label="课程时间" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-265f964b" style="{{'color:'+(form.classTime?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.classTime||'请选择课程时间')+''}}</view><u-datetime-picker vue-id="{{('2364c77c-12')+','+('2364c77c-11')}}" mode="time" show="{{timePicker}}" closeOnClickOverlay="{{true}}" minHour="5" minDate="{{minDate}}" maxHour="23" data-event-opts="{{[['^close',[['e4']]],['^confirm',[['changeTime']]],['^cancel',[['e5']]]]}}" bind:close="__e" bind:confirm="__e" bind:cancel="__e" class="data-v-265f964b" bind:__l="__l"></u-datetime-picker></u-form-item></block><block wx:else><u-form-item vue-id="{{('2364c77c-13')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" label="最初课程时间" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-265f964b" style="{{'color:'+(form.firstClassTime?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.firstClassTime||'请选择最初课程时间1')+''}}</view><u-datetime-picker vue-id="{{('2364c77c-14')+','+('2364c77c-13')}}" mode="datetime" show="{{timePickera}}" format="yyyy-MM-dd HH:mm:ss" closeOnClickOverlay="{{true}}" data-ref="datetimePicker" value="{{form.firstClassTimes}}" data-event-opts="{{[['^close',[['e7']]],['^cancel',[['e8']]],['^confirm',[['changeTimes']]],['^input',[['__set_model',['$0','firstClassTimes','$event',[]],['form']]]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-265f964b vue-ref" bind:__l="__l"></u-datetime-picker></u-form-item></block><block wx:if="{{type=='view'}}"><u-form-item vue-id="{{('2364c77c-15')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" label="最新课程时间" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e9',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-265f964b" style="{{'color:'+(form.lastClassTime?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.lastClassTime||'请选择最初课程时间1')+''}}</view><u-datetime-picker vue-id="{{('2364c77c-16')+','+('2364c77c-15')}}" mode="datetime" show="{{timePickera}}" format="yyyy-MM-dd HH:mm:ss" closeOnClickOverlay="{{true}}" data-ref="datetimePicker" value="{{form.firstClassTimes}}" data-event-opts="{{[['^close',[['e10']]],['^cancel',[['e11']]],['^confirm',[['changeTimes']]],['^input',[['__set_model',['$0','firstClassTimes','$event',[]],['form']]]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-265f964b vue-ref" bind:__l="__l"></u-datetime-picker></u-form-item></block><u-form-item vue-id="{{('2364c77c-17')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classLength" label="课程时长(分钟)" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-18')+','+('2364c77c-17')}}" inputAlign="right" type="number" border="none" placeholder="请输入课程时长" value="{{form.classLength}}" data-event-opts="{{[['^input',[['__set_model',['$0','classLength','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-19')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="phone" label="联系人号码" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-20')+','+('2364c77c-19')}}" inputAlign="right" border="none" placeholder="请输入联系人号码" value="{{form.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-21')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="price" label="团课价格" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-22')+','+('2364c77c-21')}}" inputAlign="right" type="digit" border="none" placeholder="请输入团课价格" value="{{form.price}}" data-event-opts="{{[['^input',[['__set_model',['$0','price','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-23')+','+('2364c77c-4')}}" required="{{true}}" borderBottom="{{true}}" prop="coachId" label="选择教练" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e12',['$event']]]]]}}" class="w-100 u-text-right u-font-30 data-v-265f964b" style="{{'color:'+(form.coachId?'#333':'#c0c4cc')+';'}}" bindtap="__e">{{''+(form.coachName||'请选择教练')+''}}</view><u-picker vue-id="{{('2364c77c-24')+','+('2364c77c-23')}}" show="{{showCoach}}" columns="{{columnsCoach}}" keyName="nickName" data-event-opts="{{[['^cancel',[['e13']]],['^confirm',[['confirmCoach']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-265f964b" bind:__l="__l"></u-picker></u-form-item><u-form-item vue-id="{{('2364c77c-25')+','+('2364c77c-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="beforeTimeBooking" label="禁止预约(课程开始前)" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-26')+','+('2364c77c-25')}}" inputAlign="right" type="number" border="none" placeholder="分钟" value="{{form.beforeTimeBooking}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeBooking','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-27')+','+('2364c77c-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="beforeTimeCancel" label="禁止取消(课程开始前)" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-28')+','+('2364c77c-27')}}" inputAlign="right" type="number" border="none" placeholder="分钟" value="{{form.beforeTimeCancel}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeCancel','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-29')+','+('2364c77c-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="minAttendance" label="最低开课人数" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-30')+','+('2364c77c-29')}}" inputAlign="right" type="number" border="none" placeholder="人数(人)" value="{{form.minAttendance}}" data-event-opts="{{[['^input',[['__set_model',['$0','minAttendance','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('2364c77c-31')+','+('2364c77c-4')}}" labelWidth="200" required="{{true}}" prop="beforeTimeClose" label="未满足开课人数关闭课程时间" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2364c77c-32')+','+('2364c77c-31')}}" inputAlign="right" type="number" border="none" placeholder="(分钟)" value="{{form.beforeTimeClose}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeClose','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-input></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-265f964b"><u-form-item vue-id="{{('2364c77c-33')+','+('2364c77c-4')}}" required="{{true}}" prop="background" labelPosition="top" label="团课列表背景图片" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-265f964b"><view data-event-opts="{{[['tap',[['chooseBackground',['$event']]]]]}}" class="upload-img-box data-v-265f964b" catchtap="__e"><block wx:if="{{!form.background}}"><u-icon vue-id="{{('2364c77c-34')+','+('2364c77c-33')}}" name="camera" size="40" color="#ddd" class="data-v-265f964b" bind:__l="__l"></u-icon></block><block wx:else><view class="u-relative w-100 h-100 data-v-265f964b"><image class="w-100 data-v-265f964b" src="{{form.background}}" mode="widthFix" data-event-opts="{{[['tap',[['previewBackground',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delBackground',['$event']]]]]}}" hidden="{{!(form.background)}}" class="u-absolute u-p-10 data-v-265f964b" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('2364c77c-35')+','+('2364c77c-33')}}" name="close" color="#fff" size="13" class="data-v-265f964b" bind:__l="__l"></u-icon></view></view></block></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-265f964b"><u-form-item vue-id="{{('2364c77c-36')+','+('2364c77c-4')}}" prop="banner" labelPosition="top" label="团课banner图片" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-265f964b"><block wx:for="{{form.banner}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="upload-img-box data-v-265f964b"><view class="u-relative w-100 h-100 data-v-265f964b"><image class="w-100 data-v-265f964b" src="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewBanner',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delBanner',[index]]]]]}}" class="u-absolute u-p-10 data-v-265f964b" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('2364c77c-37-'+index)+','+('2364c77c-36')}}" name="close" color="#fff" size="13" class="data-v-265f964b" bind:__l="__l"></u-icon></view></view></view></block><view data-event-opts="{{[['tap',[['chooseBanner',['$event']]]]]}}" class="upload-img-box data-v-265f964b" catchtap="__e"><u-icon vue-id="{{('2364c77c-38')+','+('2364c77c-36')}}" name="camera" size="40" color="#ddd" class="data-v-265f964b" bind:__l="__l"></u-icon></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-265f964b"><u-form-item vue-id="{{('2364c77c-39')+','+('2364c77c-4')}}" prop="classInfoPic" labelPosition="top" label="团课详情图片" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-img-container data-v-265f964b"><block wx:for="{{form.classInfoPic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="upload-img-box data-v-265f964b"><view class="u-relative w-100 h-100 data-v-265f964b"><image class="w-100 data-v-265f964b" src="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewClassInfoPic',['$event']]]]]}}" catchtap="__e"></image><view data-event-opts="{{[['tap',[['delClassInfoPic',[index]]]]]}}" class="u-absolute u-p-10 data-v-265f964b" style="border-radius:0 0 0 16rpx;right:0;top:0;background:#dd524d;" catchtap="__e"><u-icon vue-id="{{('2364c77c-40-'+index)+','+('2364c77c-39')}}" name="close" color="#fff" size="13" class="data-v-265f964b" bind:__l="__l"></u-icon></view></view></view></block><view data-event-opts="{{[['tap',[['chooseClassInfoPic',['$event']]]]]}}" class="upload-img-box data-v-265f964b" catchtap="__e"><u-icon vue-id="{{('2364c77c-41')+','+('2364c77c-39')}}" name="camera" size="40" color="#ddd" class="data-v-265f964b" bind:__l="__l"></u-icon></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-265f964b"><u-form-item vue-id="{{('2364c77c-42')+','+('2364c77c-4')}}" required="{{true}}" prop="remark" labelPosition="top" label="课程介绍" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('2364c77c-43')+','+('2364c77c-42')}}" border="none" maxLength="300" placeholder="请输入课程介绍" value="{{form.remark}}" data-event-opts="{{[['^input',[['__set_model',['$0','remark','$event',[]],['form']]]]]}}" class="data-v-265f964b" bind:__l="__l"></u-textarea></u-form-item></view></u-form></view><block wx:if="{{type!='view'}}"><view class="bottom-blk bg-fff w-100 u-p-40 data-v-265f964b"><u-button vue-id="{{('2364c77c-44')+','+('2364c77c-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" loading="{{disabled}}" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['createGroupCourse']]]]}}" bind:click="__e" class="data-v-265f964b" bind:__l="__l" vue-slots="{{['default']}}">{{''+(type==add?'保存':'创建')+''}}</u-button></view></block></view></theme-wrap>