{"version": 3, "file": "pages/login/authorize_xcx.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uZAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACpBA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AACA,IAAAC,SAAA,GAAAD,mBAAA;AAAA,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA,GAAAC,0HAAA,CAAAE,mBAAA;MACAC,SAAA,GAAAH,0HAAA,CAAAI,kBAAA;MACAC,IAAA;MACAC,KAAA;QACAC,MAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,IAAAC,GAAA;YACA,OAAAA,GAAA,CAAAC,IAAA,CAAAH,KAAA;UACA;UACAJ,OAAA;UACAC,OAAA;QACA,EACA;QACAO,IAAA,GACA;UACAT,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAQ,OAAA;IACAC,cAAA,WAAAA,eAAA/B,CAAA;MAAA,IAAAgC,KAAA;MAAA,OAAAC,iBAAA,cAAA9B,mBAAA,GAAA+B,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA;QAAA,OAAAjC,mBAAA,GAAAkC,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,KAAA,CAAAzB,UAAA;cACA,IAAAP,CAAA,CAAA0C,MAAA,CAAAC,MAAA;gBACAX,KAAA,CAAAzB,UAAA;gBACAyB,KAAA,CAAAY,KAAA,CAAAC,MAAA,CAAAC,IAAA;kBACAzB,OAAA;kBACA0B,IAAA;kBACAC,QAAA;gBACA;cACA;gBACAZ,MAAA,GAAAa,GAAA,CAAAC,cAAA,SAAAC,UAAA;gBACAnB,KAAA,CAAAoB,UAAA,CAAApD,CAAA,EAAAoC,MAAA;cACA;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IACA;IACAmB,KAAA,WAAAA,MAAAlB,MAAA;MACAmB,YAAA,CACAC,WAAA;QACApD,IAAA;UACAyB,IAAA,EAAAO;QACA;QACAqB,MAAA;MACA,GACAC,IAAA,WAAAC,GAAA;QACA,IAAAC,OAAA,GAAAD,GAAA,CAAAvD,IAAA,CAAAA,IAAA;QACA6C,GAAA,CAAAY,cAAA,UAAAD,OAAA,CAAAE,KAAA;QACAb,GAAA,CAAAY,cAAA,SAAAD,OAAA,CAAAG,IAAA;QACA;MACA;IACA;IACAX,UAAA,WAAAA,WAAApD,CAAA,EAAAoC,MAAA;MAAA,IAAA4B,MAAA;MAAA,OAAA/B,iBAAA,cAAA9B,mBAAA,GAAA+B,IAAA,UAAA+B,SAAA;QAAA,OAAA9D,mBAAA,GAAAkC,IAAA,UAAA6B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,IAAA,GAAA2B,SAAA,CAAA1B,IAAA;YAAA;cACAuB,MAAA,CAAAzD,UAAA;cACAgD,YAAA,CACAa,aAAA;gBACAhE,IAAA;kBACAyB,IAAA,EAAA7B,CAAA,CAAA0C,MAAA,CAAAb,IAAA;kBACAO,MAAA,EAAAA;gBACA;gBACAqB,MAAA;cACA,GACAC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAvD,IAAA,CAAAyB,IAAA,WAAA8B,GAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAA0D,KAAA;kBACAb,GAAA,CAAAY,cAAA,UAAAF,GAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAA0D,KAAA;kBACAb,GAAA,CAAAY,cAAA,SAAAF,GAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAA2D,IAAA;kBACAd,GAAA,CAAAoB,YAAA;gBACA;kBACApB,GAAA,CAAAqB,SAAA;oBACAC,KAAA,EAAAZ,GAAA,CAAAvD,IAAA,CAAAiB,OAAA;oBACA2B,QAAA;oBACAwB,IAAA;kBACA;kBACAR,MAAA,CAAAzD,UAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4D,SAAA,CAAAd,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IACA;IAEAQ,UAAA,WAAAA,WAAAC,GAAA,EAAAjD,KAAA;MACAwB,GAAA,CAAA0B,iBAAA,CAAAD,GAAA;MACAzB,GAAA,CAAAY,cAAA,CAAAa,GAAA,EAAAjD,KAAA;IACA;EACA;AACA;;;;;;;;;;AC3IA;;;;;;;;;;;;;;;ACAA3B,mBAAA;AAGA,IAAA8E,IAAA,GAAA/E,sBAAA,CAAAC,mBAAA;AACA,IAAA+E,cAAA,GAAAhF,sBAAA,CAAAC,mBAAA;AAAkD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAHlD;AACA8E,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC;;;;;;;;;;;;;;;;;;;ACL0G;AAC1H;AACA,CAAiE;AACL;AAC5D,CAA0E;;;AAG1E;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvB0f,CAAC,+DAAe,ieAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,y4BAAG,EAAC", "sources": ["webpack:///./src/pages/login/authorize_xcx.vue?5f64", "uni-app:///src/pages/login/authorize_xcx.vue", "webpack:///./src/pages/login/authorize_xcx.vue?efcf", "uni-app:///src/main.js", "webpack:///./src/pages/login/authorize_xcx.vue?ec41", "webpack:///./src/pages/login/authorize_xcx.vue?1016", "webpack:///./src/pages/login/authorize_xcx.vue?9c6f", "webpack:///./src/pages/login/authorize_xcx.vue?1003"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNoNetwork: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-no-network/u-no-network\" */ \"uview-ui/components/u-no-network/u-no-network.vue\"\n      )\n    },\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uNotify: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notify/u-notify\" */ \"uview-ui/components/u-notify/u-notify.vue\"\n      )\n    },\n    uLoadingPage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-page/u-loading-page\" */ \"uview-ui/components/u-loading-page/u-loading-page.vue\"\n      )\n    },\n    \"u-Image\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--image/u--image\" */ \"uview-ui/components/u--image/u--image.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <view class=\"page\">\n    <u-no-network></u-no-network>\n    <u-toast ref=\"uToast\" />\n    <u-notify ref=\"uNotify\" />\n    <view class=\"loading\" v-if=\"loading\">\n      <u-loading-page :loading=\"loading\"></u-loading-page>\n    </view>\n    <view class=\"content\" v-else>\n      <view class=\"authorize-page\">\n        <view class=\"logo\">\n          <u--image\n            src=\"@/static/images/logo.png\"\n            mode=\"widthFix\"\n            width=\"150rpx\"\n            :fade=\"true\"\n            duration=\"1000\"\n          ></u--image>\n        </view>\n        <view class=\"auth-title\">绑定手机号</view>\n        <view class=\"auth-info\">获取并绑定您的手机号</view>\n        <view class=\"auth-btn\">\n          <u-button\n            open-type=\"getPhoneNumber\"\n            type=\"warning\"\n            ripple\n            @getphonenumber=\"getPhoneNumber\"\n            :loading=\"btnLoading\"\n          >\n            绑定手机\n          </u-button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport api from \"@/common/api\";\nimport { APPINFO } from \"@/common/constant\";\nexport default {\n  data() {\n    return {\n      loading: false,\n      disabled: false,\n      btnLoading: false,\n      onSubmitDisabled: false,\n      loadNum: 60,\n      codeTips: \"获取验证码\",\n      NEEDMOBILE: +process.env.VUE_APP_NEED_MOBILE,\n      KEYMOBILE: +process.env.VUE_APP_KEY_MOBILE,\n      info: {},\n      rules: {\n        mobile: [\n          {\n            required: true,\n            message: \"请输入手机号\",\n            trigger: [\"change\", \"blur\"]\n          },\n          {\n            validator: (rule, value, callback) => {\n              let reg = /^1[3-9]\\d{9}$/;\n              return reg.test(value);\n            },\n            message: \"手机号码不正确\",\n            trigger: [\"change\", \"blur\"]\n          }\n        ],\n        code: [\n          {\n            required: true,\n            message: \"请输入验证码\",\n            trigger: [\"change\", \"blur\"]\n          }\n        ]\n      }\n    };\n  },\n  methods: {\n    async getPhoneNumber(e) {\n      this.btnLoading = true;\n      if (e.detail.errMsg == \"getPhoneNumber:fail user deny\") {\n        this.btnLoading = false;\n        this.$refs.uToast.show({\n          message: \"绑定失败\",\n          type: \"error\",\n          duration: \"2300\"\n        });\n      } else {\n        let openid = uni.getStorageSync(\"user\").xcx_openid;\n        this.bindMobile(e, openid);\n      }\n    },\n    login(openid) {\n      api\n        .getTokenXcx({\n          data: {\n            code: openid\n          },\n          method: \"POST\"\n        })\n        .then(res => {\n          let resData = res.data.data;\n          uni.setStorageSync(\"token\", resData.token);\n          uni.setStorageSync(\"user\", resData.user);\n          // uni.navigateBack();\n        });\n    },\n    async bindMobile(e, openid) {\n      this.btnLoading = true;\n      api\n        .loginByMobile({\n          data: {\n            code: e.detail.code,\n            openid\n          },\n          method: \"POST\"\n        })\n        .then(res => {\n          if (res.data.code == 200 && res.data.data.token) {\n            uni.setStorageSync(\"token\", res.data.data.token);\n            uni.setStorageSync(\"user\", res.data.data.user);\n            uni.navigateBack();\n          } else {\n            uni.showToast({\n              title: res.data.message,\n              duration: 800,\n              icon: \"none\"\n            });\n            this.btnLoading = false;\n          }\n        });\n    },\n\n    useStorage(key, value) {\n      uni.removeStorageSync(key);\n      uni.setStorageSync(key, value);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\">\n/* #ifdef MP-WEIXIN */\n.authorize-page {\n  width: 100vw;\n  height: 100vh;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  .logo {\n    padding-top: 320upx;\n    line-height: 0;\n    margin-bottom: 40upx;\n  }\n  .auth-title {\n    font-size: 40upx;\n    line-height: 60upx;\n    color: #222;\n    font-weight: bold;\n    margin-bottom: 10upx;\n  }\n  .auth-info {\n    font-size: 26upx;\n    line-height: 36upx;\n    color: #999;\n  }\n}\n.auth-btn {\n  margin-top: 40upx;\n  width: 500rpx;\n  padding: 0 80upx;\n  .cu-btn {\n    width: 100%;\n  }\n}\n/* #endif */\n\n/* #ifdef H5 */\n.page {\n  min-height: 100vh;\n  padding: 0 40upx;\n  position: relative;\n}\n.login-wrap {\n  position: relative;\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  justify-content: center;\n  min-height: 100vh;\n  .back-to-home {\n    position: fixed;\n    top: 50px;\n    left: 20px;\n    .link-item {\n      display: block;\n      border-radius: 8upx;\n      padding: 6upx 16upx;\n      color: #fff;\n      font-size: 26upx;\n      background-color: #73c7a0;\n    }\n    .link-item::before {\n      content: \"\";\n      position: absolute;\n      width: 0;\n      height: 0;\n      left: -10upx;\n      top: 50%;\n      margin-top: -10upx;\n      border-top: 10upx solid transparent;\n      border-bottom: 10upx solid transparent;\n      border-right: 10upx solid #73c7a0;\n    }\n  }\n  .login-logo-wrap {\n    width: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding-bottom: 30upx;\n    .logo {\n      width: 150upx;\n    }\n  }\n  .login-main-title {\n    text-align: center;\n    font-size: 40upx;\n    color: #222;\n    font-weight: bold;\n    margin-bottom: 40upx;\n  }\n  .form-wrap {\n    background: #fff;\n    padding: 60upx 40upx;\n    border-radius: 16upx;\n    .form-btn {\n      padding-top: 40upx;\n    }\n  }\n  .agreement-wrap {\n    border: none;\n    margin-top: 40upx;\n    font-size: 28upx;\n    color: #000000;\n  }\n  .link-item {\n    display: contents;\n    color: #73c7a0;\n  }\n}\n.code-send-blk {\n  background-color: #73c7a0;\n  padding: 14rpx 30rpx;\n  border-radius: 8rpx;\n  color: #fff;\n}\n.num-blk {\n  width: 50rpx;\n  text-align: center;\n}\n/* #endif */\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/authorize_xcx.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./authorize_xcx.vue?vue&type=template&id=8229bbf0&\"\nvar renderjs\nimport script from \"./authorize_xcx.vue?vue&type=script&lang=js&\"\nexport * from \"./authorize_xcx.vue?vue&type=script&lang=js&\"\nimport style0 from \"./authorize_xcx.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/authorize_xcx.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize_xcx.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize_xcx.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize_xcx.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize_xcx.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize_xcx.vue?vue&type=template&id=8229bbf0&\""], "names": ["_api", "_interopRequireDefault", "require", "_constant", "e", "__esModule", "default", "_regeneratorRuntime", "data", "loading", "disabled", "btnLoading", "onSubmitDisabled", "loadNum", "codeTips", "NEEDMOBILE", "process", "env", "VUE_APP_NEED_MOBILE", "KEYMOBILE", "VUE_APP_KEY_MOBILE", "info", "rules", "mobile", "required", "message", "trigger", "validator", "rule", "value", "callback", "reg", "test", "code", "methods", "getPhoneNumber", "_this", "_asyncToGenerator", "mark", "_callee", "openid", "wrap", "_callee$", "_context", "prev", "next", "detail", "errMsg", "$refs", "uToast", "show", "type", "duration", "uni", "getStorageSync", "xcx_openid", "bindMobile", "stop", "login", "api", "getTokenXcx", "method", "then", "res", "resData", "setStorageSync", "token", "user", "_this2", "_callee2", "_callee2$", "_context2", "loginByMobile", "navigateBack", "showToast", "title", "icon", "useStorage", "key", "removeStorageSync", "_vue", "_authorize_xcx", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}