{"version": 3, "file": "pages/user/index.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+VAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC4EA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AACA,IAAAC,SAAA,GAAAD,mBAAA;AAEA,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAEA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,OAAA;MACAC,cAAA;MACAC,SAAA;MACA;MACA;MACA;MACA;MACA;QACAC,IAAA,EAAAvB,mBAAA;QACAwB,KAAA;QACAC,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAF,IAAA,EAAAvB,mBAAA;QACAwB,KAAA;QACAC,GAAA;MACA,GACA;QACAF,IAAA,EAAAvB,mBAAA;QACAwB,KAAA;QACAC,GAAA;MACA,EACA;MACAC,SAAA;QACAH,IAAA,EAAAvB,mBAAA;QACAwB,KAAA;QACAC,GAAA;MACA,GACA;QACAF,IAAA,EAAAvB,mBAAA;QACAwB,KAAA;QACAC,GAAA;MAEA,GACA;QACAF,IAAA,EAAAvB,mBAAA;QACAwB,KAAA;QACAC,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MAAA,CACA;MACAE,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA;IAEA;EACA;EACAC,MAAA,WAAAA,OAAA;IACA;IACA;IACA;IACA,IAAAC,GAAA,CAAAC,cAAA;MACA,KAAAC,QAAA;MACA,KAAAd,OAAA;IACA;MACA,KAAAA,OAAA;MACA,KAAAF,OAAA;IACA;EACA;EACAiB,MAAA,WAAAA,OAAA;IAAA,IAAAC,qBAAA,GAAAC,aAAA,KAOAL,GAAA,CAAAM,kBAAA;IAHA,KAAAf,OAAA,GAAAa,qBAAA,CADAG,WAAA,CACAhB,OAAA;EAMA;EACAiB,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,OAAA,WAAAA,QAAAC,EAAA;MACAX,GAAA,CAAAY,UAAA;QACAhB,GAAA,6BAAAe;MACA;IACA;IACAT,QAAA,WAAAA,SAAA;MAAA,IAAAW,KAAA;MACA;MACAC,YAAA,CACAC,OAAA;QACA9B,IAAA;UACA+B,SAAA;QACA;QACAC,MAAA;MACA,GACAC,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAT,KAAA,CAAAU,MAAA,GAAAJ,GAAA,CAAAhC,IAAA,CAAAoC,MAAA;UACAvB,GAAA,CAAAwB,cAAA,aAAAL,GAAA,CAAAhC,IAAA;UACAa,GAAA,CAAAwB,cAAA,eAAAL,GAAA,CAAAM,MAAA;UACAzB,GAAA,CAAAwB,cAAA,cAAAL,GAAA,CAAAO,KAAA;UACAb,KAAA,CAAA1B,IAAA,GAAAgC,GAAA,CAAAhC,IAAA;UACA0B,KAAA,CAAAzB,OAAA;UACAyB,KAAA,CAAA1B,IAAA,CAAAwC,UAAA,GAAAR,GAAA,CAAAhC,IAAA,CAAAyC,MAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,KAAA;MACA9B,GAAA,CAAAY,UAAA;QACAhB,GAAA,+BAAAkC;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,UAAA;MACA,KAAAA,UAAA,QAAA7C,IAAA,cAAA6C,UAAA,eAAAA,UAAA,CAAAC,UAAA;QACAjC,GAAA,CAAAkC,SAAA;UACAC,IAAA;UACAC,IAAA;UACAzC,KAAA;QACA;MACA;MACA,KAAAP,OAAA;MACAY,GAAA,CAAAqC,iBAAA;MACArC,GAAA,CAAAqC,iBAAA;MACArC,GAAA,CAAAqC,iBAAA;MACArC,GAAA,CAAAqC,iBAAA;MACA,KAAAlD,IAAA;IACA;IACAmD,SAAA,WAAAA,UAAA;MAEA,IAAAC,aAAA,GAAAvC,GAAA,CAAAwC,gBAAA;MACAD,aAAA,CAAAE,gBAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAJ,aAAA,CAAAK,aAAA;YACA5C,GAAA,CAAA6C,SAAA;cACAlD,KAAA;cACAmD,OAAA;cACAC,OAAA,WAAAA,QAAAL,GAAA;gBACA,IAAAA,GAAA,CAAAM,OAAA;kBACAT,aAAA,CAAAU,WAAA;gBACA;cACA;YACA;UACA;UACAV,aAAA,CAAAW,cAAA;YACAlD,GAAA,CAAA6C,SAAA;cACAlD,KAAA;cACAmD,OAAA;YACA;UACA;QACA;UACA9C,GAAA,CAAAkC,SAAA;YACAvC,KAAA;YACAyC,IAAA;YACAD,IAAA;YACAgB,QAAA;UACA;QACA;MACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAxE,mBAAA,GAAAyE,IAAA,UAAAC,QAAA;QAAA,IAAAC,KAAA;QAAA,OAAA3E,mBAAA,GAAA4E,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAL,KAAA,GAAAJ,MAAA;cACArD,GAAA,CAAA+D,KAAA;gBACAC,QAAA;gBACAjB,OAAA,WAAAA,QAAA1E,CAAA;kBACA2B,GAAA,CAAAiE,WAAA;oBACAD,QAAA;oBACAjB,OAAA,WAAAA,QAAAmB,OAAA;sBACA9C,OAAA,CAAAC,GAAA,CAAA6C,OAAA;sBACA;sBACA9C,OAAA,CAAAC,GAAA,YAAA6C,OAAA,CAAAC,QAAA,CAAAC,QAAA;oBACA;kBACA;kBACAhD,OAAA,CAAAC,GAAA,WAAAhD,CAAA;kBACA;kBACAyC,YAAA,CACAiD,KAAA;oBACA9E,IAAA;sBACA+B,SAAA;sBACAqD,MAAA,EAAAhG,CAAA,CAAAiD;oBACA;oBACAL,MAAA;kBACA,GACAC,IAAA,WAAAwB,GAAA;oBACAtB,OAAA,CAAAC,GAAA,CAAAqB,GAAA;oBACA,IAAAA,GAAA,CAAApB,IAAA;sBACAtB,GAAA,CAAAwB,cAAA,UAAAkB,GAAA,CAAA4B,KAAA;sBACA;sBACAxD,YAAA,CACAC,OAAA;wBACA9B,IAAA;0BACA+B,SAAA;wBACA;wBACAC,MAAA;sBACA,GACAC,IAAA,WAAAC,GAAA;wBACAC,OAAA,CAAAC,GAAA,QAAAF,GAAA;wBACA,IAAAA,GAAA,CAAAG,IAAA;0BACAmC,KAAA,CAAAlC,MAAA,GAAAJ,GAAA,CAAAhC,IAAA,CAAAoC,MAAA;0BACAkC,KAAA,CAAAtE,IAAA,GAAAgC,GAAA,CAAAhC,IAAA;0BACAa,GAAA,CAAAwB,cAAA,aAAAL,GAAA,CAAAhC,IAAA;0BACAa,GAAA,CAAAwB,cAAA,eAAAL,GAAA,CAAAM,MAAA;0BACAzB,GAAA,CAAAwB,cAAA,cAAAL,GAAA,CAAAO,KAAA;0BACA+B,KAAA,CAAAvD,QAAA;0BACAuD,KAAA,CAAArE,OAAA;wBACA;sBACA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAwE,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IACA;IACAgB,SAAA,WAAAA,UAAAnG,CAAA,EAAAyD,KAAA;MACA,IAAA7C,IAAA,QAAAwF,KAAA,cAAA3C,KAAA,EAAAzD,CAAA;MACA+C,OAAA,CAAAC,GAAA,CAAApC,IAAA,EAAAA,IAAA,CAAAW,GAAA;MACA,KAAAX,IAAA,CAAAW,GAAA;QACA,KAAAP,SAAA;MACA;QACAW,GAAA,EAAAf,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAyF,OAAA;UACA9E,GAAA,EAAAX,IAAA,CAAAW;QACA;MACA;IACA;IACA+E,MAAA,WAAAA,OAAA;MACA,SAAAvF,OAAA;QACAY,GAAA,CAAAY,UAAA;UACAhB,GAAA;QACA;MACA;IACA;EACA;EACAgF,iBAAA,WAAAA,kBAAA;IACA,IAAA5E,GAAA,CAAAC,cAAA;MACA,KAAAC,QAAA;IACA;IACAF,GAAA,CAAA6E,mBAAA;EACA;AACA;;;;;;;;;;AC5bA;;;;;;;;;;;;;;;ACAA1G,mBAAA;AAGA,IAAA2G,IAAA,GAAA5G,sBAAA,CAAAC,mBAAA;AACA,IAAA4G,MAAA,GAAA7G,sBAAA,CAAAC,mBAAA;AAAyC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAHzC;AACA2G,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLkG;AAClH;AACA,CAAyD;AACL;AACpD,CAAkE;;;AAGlE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBkf,CAAC,+DAAe,ydAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,i4BAAG,EAAC", "sources": ["webpack:///./src/pages/user/index.vue?799d", "uni-app:///src/pages/user/index.vue", "webpack:///./src/pages/user/index.vue?2f88", "uni-app:///src/main.js", "webpack:///./src/pages/user/index.vue?00f2", "webpack:///./src/pages/user/index.vue?829a", "webpack:///./src/pages/user/index.vue?1b80", "webpack:///./src/pages/user/index.vue?2c41"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNoNetwork: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-no-network/u-no-network\" */ \"uview-ui/components/u-no-network/u-no-network.vue\"\n      )\n    },\n    uGrid: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-grid/u-grid\" */ \"uview-ui/components/u-grid/u-grid.vue\"\n      )\n    },\n    uGridItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-grid-item/u-grid-item\" */ \"uview-ui/components/u-grid-item/u-grid-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var f0 = _vm.user.avatar_url ? _vm._f(\"Img\")(_vm.user.avatar_url) : null\n  var l0 = _vm.__map(_vm.baseList1, function (baseListItem, baseListIndex) {\n    var $orig = _vm.__get_orig(baseListItem)\n    var a0 = {\n      paddingBottom: 10 + \"rpx\",\n      paddingTop: 24 + \"rpx\",\n    }\n    return {\n      $orig: $orig,\n      a0: a0,\n    }\n  })\n  var l1 = _vm.__map(_vm.baseList2, function (baseListItem, baseListIndex) {\n    var $orig = _vm.__get_orig(baseListItem)\n    var a1 = {\n      paddingBottom: 14 + \"rpx\",\n      paddingTop: 28 + \"rpx\",\n    }\n    return {\n      $orig: $orig,\n      a1: a1,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.popupShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        f0: f0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n\t<themeWrap>\n\t\t<template #content>\n\t\t\t<u-no-network></u-no-network>\n\t\t\t<!-- <u-navbar\n        :titleStyle=\"{ color: buttonTextColor }\"\n        :bgColor=\"navBarColor\"\n        :placeholder=\"true\"\n        title=\"我的\"\n        @rightClick=\"uni.navigateBack()\"\n        :autoBack=\"true\"\n        leftIconSize=\"0\"\n        :border=\"false\"\n        :safeAreaInsetTop=\"true\"\n      >\n      </u-navbar> -->\n\t\t\t<!-- <view class=\"loading\" v-if=\"loading\">\n        <u-loading-page :loading=\"loading\"></u-loading-page>\n      </view> -->\n\t\t\t<view class=\"container u-p-t-40 u-p-b-40\">\n\t\t\t\t<view class=\"u-flex w-100 u-p-r-40 u-p-l-40 u-col-center  u-p-t-80  u-m-b-40\">\n\t\t\t\t\t<!-- <view class=\"u-flex-1 u-flex-col u-row-center u-col-center\">\n            <view class=\"font-bold\" style=\"font-size: 48rpx;\">0</view>\n            <view class=\"u-tips-color u-font-28\">累计天数</view>\n          </view> -->\n\t\t\t\t\t<view class=\"u-flex-1 u-flex-col u-row-center u-col-center\" @click=\"goEdit\">\n\t\t\t\t\t\t<view class=\"u-m-b-20\" style=\"width: 150rpx;height: 150rpx;overflow:hidden;border-radius: 50%;\">\n\t\t\t\t\t\t\t<image mode=\"widthFix\" class=\"w-100\" :src=\"user.avatar_url | Img\" alt=\"\"\n\t\t\t\t\t\t\t\tv-if=\"user.avatar_url\" />\n\t\t\t\t\t\t\t<image mode=\"widthFix\" class=\"w-100\" src=\"@/static/images/default/head1.png\" alt=\"\" v-else-if=\"user.sex=='0'\" />\n\t\t\t\t\t\t\t<image mode=\"widthFix\" class=\"w-100\" src=\"@/static/images/default/head2.png\" alt=\"\" v-else-if=\"user.sex=='1'\" />\n\t\t\t\t\t\t\t<image mode=\"widthFix\" class=\"w-100\" src=\"@/static/images/default/head.png\" alt=\"\" v-else />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"u-flex u-row-center w-100\">\n\t\t\t\t\t\t\t<view v-if=\"isLogin\">{{ user.nickName || '微信用户' }}</view>\n\t\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t\t<view class=\"border-32 font-bold u-font-26 u-p-t-12 u-p-b-12 u-p-r-26 u-p-l-26\"\n\t\t\t\t\t\t\t\t\tstyle=\"border: 1px solid;\" @click.stop=\"goAuthorize\">用户登录</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <view class=\"u-flex-1 u-flex-col u-row-center u-col-center\">\n            <view class=\"font-bold\" style=\"font-size: 48rpx;\">0</view>\n            <view class=\"u-tips-color u-font-28\">累计训练次数</view>\n          </view> -->\n\t\t\t\t</view>\n\t\t\t\t<!-- <view class=\"card\">\n          <view class=\"w-100 u-flex u-row-between\">\n            <view class=\"u-font-34 font-bold\">本月</view>\n            <view class=\"u-tips-color u-font-28\">全部</view>\n          </view>\n          <view class=\"w-100 u-flex\">\n            <view class=\"u-flex-1 u-flex-col u-col-center u-p-t-10 u-p-b-10\">\n              <view class=\"font-bold\" style=\"font-size: 46rpx;\">0</view>\n              <view class=\"u-tips-color u-font-26\">\n                训练次数\n              </view>\n            </view>\n            <view class=\"u-flex-1 u-flex-col u-col-center u-p-t-10 u-p-b-10\">\n              <view class=\"font-bold\" style=\"font-size: 46rpx;\">0</view>\n              <view class=\"u-tips-color u-font-26\">\n                训练次数\n              </view>\n            </view>\n            <view class=\"u-flex-1 u-flex-col u-col-center u-p-t-10 u-p-b-10\">\n              <view class=\"font-bold\" style=\"font-size: 46rpx;\">0</view>\n              <view class=\"u-tips-color u-font-26\">\n                训练时长/分钟\n              </view>\n            </view>\n          </view>\n        </view> -->\n\t\t\t\t<view class=\"card\">\n\t\t\t\t\t<view class=\"w-100 u-flex u-row-between\">\n\t\t\t\t\t\t<view class=\"u-font-34 font-bold\">场馆服务</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<u-grid :border=\"false\" @click=\"clickGrid($event,'1')\">\n\t\t\t\t\t\t<u-grid-item v-for=\"(baseListItem,baseListIndex) in baseList1\" :key=\"baseListIndex\">\n\t\t\t\t\t\t\t<u-icon :customStyle=\"{paddingBottom:10+'rpx',paddingTop:24+'rpx'}\"\n\t\t\t\t\t\t\t\t:name=\"baseListItem.name\" :size=\"32\"></u-icon>\n\t\t\t\t\t\t\t<text class=\"grid-text u-tips-color u-font-26\">{{baseListItem.title}}</text>\n\t\t\t\t\t\t</u-grid-item>\n\t\t\t\t\t</u-grid>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card\">\n\t\t\t\t\t<view class=\"w-100 u-flex u-row-between\">\n\t\t\t\t\t\t<view class=\"u-font-34 font-bold\">个人中心</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<u-grid :border=\"false\" @click=\"clickGrid($event,'2')\">\n\t\t\t\t\t\t<u-grid-item v-for=\"(baseListItem,baseListIndex) in baseList2\" :key=\"baseListIndex\">\n\t\t\t\t\t\t\t<u-icon :customStyle=\"{paddingBottom:14+'rpx',paddingTop:28+'rpx'}\"\n\t\t\t\t\t\t\t\t:name=\"baseListItem.name\" :size=\"34\"></u-icon>\n\t\t\t\t\t\t\t<text class=\"grid-text u-tips-color u-font-26\">{{baseListItem.title}}</text>\n\t\t\t\t\t\t</u-grid-item>\n\t\t\t\t\t</u-grid>\n\t\t\t\t</view>\n\t\t\t\t<!-- <view class=\"card\">\n          <view class=\"w-100 u-flex u-row-between\">\n            <view class=\"u-font-34 font-bold\">小工具</view>\n          </view>\n          <u-grid\n                :border=\"false\"\n                @click=\"clickGrid($event,'3')\"\n        >\n            <u-grid-item\n                    v-for=\"(baseListItem,baseListIndex) in baseList3\"\n                    :key=\"baseListIndex\"\n            >\n                <u-icon\n                :customStyle=\"{paddingBottom:14+'rpx',paddingTop:28+'rpx'}\"\n                        :name=\"baseListItem.name\"\n                        :size=\"34\"\n                ></u-icon>\n                <text class=\"grid-text u-tips-color u-font-26\">{{baseListItem.title}}</text>\n            </u-grid-item>\n        </u-grid>\n        </view> -->\n\t\t\t\t<!-- <view class=\"link-list-wrap\">\n          <view class=\"link-list\">\n            <u-cell-group :border=\"false\" :customStyle=\"{ fontSize: '30px' }\">\n              <u-cell title=\"我的会员卡\" isLink :border=\"false\" url=\"/pages/user/huiYuanKa\">\n                <u-icon slot=\"icon\" name=\"level\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"体征信息\" isLink :border=\"false\" url=\"/pages/tizheng/index\">\n                <u-icon slot=\"icon\" name=\"more-circle\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"入场记录\" isLink :border=\"false\" url=\"/pages/ruChang/history\">\n                <u-icon slot=\"icon\" name=\"list-dot\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"隐私政策\" isLink :border=\"false\" url=\"/pages/page/index?permalink=privacy-policy\">\n                <u-icon slot=\"icon\" name=\"info-circle\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"训练建议\" url=\"/pages/user/xunLianJianYi\" isLink :border=\"false\">\n                <u-icon slot=\"icon\" name=\"email\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"分销管理\" url=\"/pages/user/fenxiao\" isLink :border=\"false\">\n                <u-icon slot=\"icon\" name=\"rmb-circle\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"常见问题\" isLink :border=\"false\" url=\"/pages/page/index?permalink=faqs\">\n                <u-icon slot=\"icon\" name=\"question-circle\" color=\"#000\" size=\"21\"></u-icon>\n              </u-cell>\n              <u-cell title=\"关于\" isLink :border=\"false\" url=\"/pages/page/index?permalink=about\">\n                <u-icon slot=\"icon\" name=\"setting\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n              <u-cell title=\"退出登录\" isLink :border=\"false\" @click=\"logOut\">\n                <u-icon slot=\"icon\" name=\"backspace\" color=\"#000\" size=\"22\"></u-icon>\n              </u-cell>\n            </u-cell-group>\n          </view>\n        </view> -->\n\t\t\t</view>\n\t\t\t<u-popup :safeAreaInsetBottom=\"false\" closeOnClickOverlay mode=\"center\" bgColor=\"transparent\"\n\t\t\t\t@close=\"popupShow=false\" :show=\"popupShow\">\n\t\t\t\t<view>\n\t\t\t\t\t<image src=\"@/static/icon/coding.png\" mode=\"widthFix\" style=\"width: 64vw\" />\n\t\t\t\t\t<view class=\"u-font-40 font-bold fc-fff u-text-center\">\n\t\t\t\t\t\t功能开发中...\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</u-popup>\n\t\t\t<zwTabBar :selIdx=\"4\" :bigIdx=\"2\"></zwTabBar>\n\t\t</template>\n\t</themeWrap>\n</template>\n\n<script>\n\timport api from \"@/common/api\";\n\timport {\n\t\tAPPINFO\n\t} from \"@/common/constant\";\n\timport zwTabBar from \"@/components/zw-tabbar/zw-tabbar.vue\";\n\texport default {\n\t\tcomponents: {\n\t\t\tzwTabBar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloading: false,\n\t\t\t\tuser: {},\n\t\t\t\tisLogin: false,\n\t\t\t\tpopupShow: false,\n\t\t\t\tteamList: [],\n\t\t\t\tversion: \"\",\n\t\t\t\tnicknameStatus: false,\n\t\t\t\tbaseList1: [\n\t\t\t\t\t// {\n\t\t\t\t\t//   name: require('@/static/icon/tag.png'),\n\t\t\t\t\t//   title: '限时折扣'\n\t\t\t\t\t// },\n\t\t\t\t\t{\n\t\t\t\t\t\tname: require('@/static/icon/vip.png'),\n\t\t\t\t\t\ttitle: \"会员卡购买\",\n\t\t\t\t\t\turl: \"/pages/huiYuanKa/index\",\n\t\t\t\t\t},\n\t\t\t\t\t// {\n\t\t\t\t\t//   name: require('@/static/icon/tag.png'),\n\t\t\t\t\t//   title: '场馆活动'\n\t\t\t\t\t// },\n\t\t\t\t\t// {\n\t\t\t\t\t//   name: require('@/static/icon/history.png'),\n\t\t\t\t\t//   title: '场馆商城',\n\t\t\t\t\t// },\n\t\t\t\t\t{\n\t\t\t\t\t\tname: require('@/static/icon/privacy.png'),\n\t\t\t\t\t\ttitle: '会员入会协议',\n\t\t\t\t\t\turl: \"/pages/information/agreement\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: require('@/static/icon/history.png'),\n\t\t\t\t\t\ttitle: '我的合同',\n\t\t\t\t\t\turl: \"/pages/information/contract\"\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tbaseList2: [{\n\t\t\t\t\t\tname: require('@/static/icon/my-vip.png'),\n\t\t\t\t\t\ttitle: '我的会员卡',\n\t\t\t\t\t\turl: '/pages/user/huiYuanKa'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t  name:require('@/static/icon/yuyue.png'),\n\t\t\t\t\t  title: '我的签到',\n\t\t\t\t\t  url: '/pages/user/signin'\n\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: require('@/static/icon/tizheng.png'),\n\t\t\t\t\t\ttitle: '体征信息',\n\t\t\t\t\t\turl: \"/pages/tizheng/index\"\n\t\t\t\t\t},\n\t\t\t\t\t// {\n\t\t\t\t\t// \tname: require('@/static/icon/help.png'),\n\t\t\t\t\t// \ttitle: '常见问题',\n\t\t\t\t\t// \turl: \"/pages/information/question\"\n\t\t\t\t\t// },\n\t\t\t\t\t// {\n\t\t\t\t\t// \tname: require('@/static/icon/about.png'),\n\t\t\t\t\t// \ttitle: '关于我们',\n\t\t\t\t\t// \turl: \"/pages/information/about\"\n\t\t\t\t\t// },\n\t\t\t\t\t\n\t\t\t\t\t// {\n\t\t\t\t\t//   name:require('@/static/icon/tips.png'),\n\t\t\t\t\t//   title: '入会协议'\n\t\t\t\t\t// },\n\t\t\t\t],\n\t\t\t\tbaseList3: [\n\t\t\t\t\t// {\n\t\t\t\t\t//   title: \"训练计时器\",\n\t\t\t\t\t//   name: require('@/static/icon/clock.png'),\n\t\t\t\t\t// },\n\t\t\t\t\t// {\n\t\t\t\t\t//   title: \"饮食计划\",\n\t\t\t\t\t//   name: require('@/static/icon/plan.png'),\n\t\t\t\t\t// },\n\t\t\t\t\t// {\n\t\t\t\t\t//   title: \"训练计划\",\n\t\t\t\t\t//   name: require('@/static/icon/plan_b.png'),\n\t\t\t\t\t// },\n\t\t\t\t],\n\t\t\t};\n\t\t},\n\t\tonShow() {\n\t\t\t// 更新状态示例，请勿删除，项目可删\n\t\t\t// console.log(this.$store.getters.getStatus.user)\n\t\t\t// this.$store.dispatch('setStatus', {user: false})\n\t\t\tif (uni.getStorageSync(\"token\")) {\n\t\t\t\tthis.loadData();\n\t\t\t\tthis.isLogin = true;\n\t\t\t} else {\n\t\t\t\tthis.isLogin = false;\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t({\n\t\t\t\tminiProgram: {\n\t\t\t\t\tversion: this.version\n\t\t\t\t},\n\t\t\t} = {\n\t\t\t\t...uni.getAccountInfoSync(),\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\tonReady() {},\n\t\tmethods: {\n\t\t\ttoZudui(id) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/pages/share/index?id=\" + id,\n\t\t\t\t});\n\t\t\t},\n\t\t\tloadData() {\n\t\t\t\t// 获取用户信息\n\t\t\t\tapi\n\t\t\t\t\t.getInfo({\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tcompanyId: 1,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tmethod: \"GET\",\n\t\t\t\t\t})\n\t\t\t\t\t.then((ret) => {\n\t\t\t\t\t\tconsole.log(ret);\n\t\t\t\t\t\tif (ret.code == 200) {\n\t\t\t\t\t\t\tthis.userId = ret.user.userId;\n\t\t\t\t\t\t\tuni.setStorageSync(\"userInfo\", ret.user);\n\t\t\t\t\t\t\tuni.setStorageSync(\"wxUserInfo\", ret.wxUser);\n\t\t\t\t\t\t\tuni.setStorageSync(\"userRoles\", ret.roles);\n\t\t\t\t\t\t\tthis.user = ret.user;\n\t\t\t\t\t\t\tthis.isLogin = true\n\t\t\t\t\t\t\tthis.user.avatar_url = ret.user.avatar;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t},\n\t\t\ttoTeamList(index) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/pages/team/index?index=\" + index,\n\t\t\t\t});\n\t\t\t},\n\t\t\tlogOut() {\n\t\t\t\tif (this.user?.xcx_openid) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\tmask: false,\n\t\t\t\t\t\ticon: \"success\",\n\t\t\t\t\t\ttitle: \"退出成功\",\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthis.isLogin = false;\n\t\t\t\tuni.removeStorageSync(\"userInfo\");\n\t\t\t\tuni.removeStorageSync(\"wxUserInfo\");\n\t\t\t\tuni.removeStorageSync(\"token\");\n\t\t\t\tuni.removeStorageSync(\"userRoles\");\n\t\t\t\tthis.user = {};\n\t\t\t},\n\t\t\tupdateApp() {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tconst updateManager = uni.getUpdateManager();\n\t\t\t\tupdateManager.onCheckForUpdate(function(res) {\n\t\t\t\t\tif (res.hasUpdate) {\n\t\t\t\t\t\tupdateManager.onUpdateReady(function() {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: \"更新提示\",\n\t\t\t\t\t\t\t\tcontent: \"新版本已经准备好，是否重启应用？\",\n\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\tupdateManager.applyUpdate();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\tupdateManager.onUpdateFailed(function() {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: \"已经有新版本了哟~\",\n\t\t\t\t\t\t\t\tcontent: \"新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~\",\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: \"已经是最新版本了！\",\n\t\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\t\tmask: true,\n\t\t\t\t\t\t\tduration: 800,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tasync goAuthorize() {\n\t\t\t\tlet _self = this\n\t\t\t\tuni.login({\n\t\t\t\t\tprovider: \"weixin\",\n\t\t\t\t\tsuccess: (e) => {\n\t\t\t\t\t\tuni.getUserInfo({\n\t\t\t\t\t\t\tprovider: \"weixin\",\n\t\t\t\t\t\t\tsuccess: (infoRes) => {\n\t\t\t\t\t\t\t\tconsole.log(infoRes);\n\t\t\t\t\t\t\t\t//this.userName = infoRes.userInfo.nickName;\n\t\t\t\t\t\t\t\tconsole.log(\"用户昵称为：\" + infoRes.userInfo.nickName);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log(\"123132\", e);\n\t\t\t\t\t\t// 登录 - 绑定手机\n\t\t\t\t\t\tapi\n\t\t\t\t\t\t\t.login({\n\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\tcompanyId: 1,\n\t\t\t\t\t\t\t\t\tjsCode: e.code,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tmethod: \"GET\",\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.then((res) => {\n\t\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\t\t\t\tuni.setStorageSync(\"token\", res.token);\n\t\t\t\t\t\t\t\t\t// 获取用户信息\n\t\t\t\t\t\t\t\t\tapi\n\t\t\t\t\t\t\t\t\t\t.getInfo({\n\t\t\t\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\t\t\t\tcompanyId: 1,\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tmethod: \"GET\",\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t.then((ret) => {\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('ret', ret);\n\t\t\t\t\t\t\t\t\t\t\tif (ret.code == 200) {\n\t\t\t\t\t\t\t\t\t\t\t\t_self.userId = ret.user.userId;\n\t\t\t\t\t\t\t\t\t\t\t\t_self.user = ret.user\n\t\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync(\"userInfo\", ret.user);\n\t\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync(\"wxUserInfo\", ret.wxUser);\n\t\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync(\"userRoles\", ret.roles);\n\t\t\t\t\t\t\t\t\t\t\t\t_self.loadData();\n\t\t\t\t\t\t\t\t\t\t\t\t_self.isLogin = true;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tclickGrid(e, index) {\n\t\t\t\tconst data = this.$data['baseList' + index][e]; //{name:'...',title:'..',url: '..'}\n\t\t\t\tconsole.log(data, data.url);\n\t\t\t\tif (!data.url) {\n\t\t\t\t\tthis.popupShow = true\n\t\t\t\t} else {\n\t\t\t\t\tuni[data?.navType || 'navigateTo']({\n\t\t\t\t\t\turl: data.url\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoEdit() {\n\t\t\t\tif (this.isLogin) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: \"/pages/user/edit\",\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tif (uni.getStorageSync(\"token\")) {\n\t\t\t\tthis.loadData();\n\t\t\t}\n\t\t\tuni.stopPullDownRefresh();\n\t\t},\n\t};\n</script>\n\n<style lang=\"scss\">\n\t.user-top {\n\t\tpadding-bottom: 40upx;\n\t\tborder-bottom: 2upx solid #dddddd;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t.user-avatar {\n\t\t\tline-height: 0;\n\t\t\tmin-width: 144upx;\n\t\t\tmargin-right: 36upx;\n\n\t\t\timg {\n\t\t\t\twidth: 144upx;\n\t\t\t\theight: 144upx;\n\t\t\t\tborder-radius: 20upx;\n\t\t\t}\n\t\t}\n\n\t\t.user-info {\n\t\t\tflex: 1;\n\n\t\t\t.user-nickName {\n\t\t\t\tmargin-bottom: 10upx;\n\t\t\t\tfont-size: 38upx;\n\t\t\t\tcolor: #000;\n\t\t\t}\n\t\t}\n\t}\n\n\t.user-tips {\n\t\tpadding: 20upx 0 0 0;\n\t\tfont-size: 26upx;\n\t\tcolor: #666;\n\t}\n\n\t.link-list-wrap {\n\t\t.link-list {\n\t\t\tbackground-color: #ffffff;\n\t\t\tpadding: 20upx 40upx;\n\t\t\tborder-radius: 40upx;\n\t\t}\n\t}\n\n\t.login-btn-wrap {\n\t\twidth: 200upx;\n\t}\n\n\t.card {\n\t\tmargin-bottom: 50upx;\n\t\tpadding: 40upx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 40upx;\n\t}\n\n\twx-button.contact-btn::after,\n\tuni-button.contact-btn::after {\n\t\tcontent: \"\";\n\t\tborder: none !important;\n\t\toutline: none !important;\n\t}\n</style>", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=137d5072&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=137d5072&\""], "names": ["_api", "_interopRequireDefault", "require", "_constant", "e", "__esModule", "default", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_regeneratorRuntime", "components", "zwTabBar", "data", "loading", "user", "is<PERSON>ogin", "popupShow", "teamList", "version", "nicknameStatus", "baseList1", "name", "title", "url", "baseList2", "baseList3", "onShow", "uni", "getStorageSync", "loadData", "onLoad", "_uni$getAccountInfoSy", "_objectSpread", "getAccountInfoSync", "miniProgram", "onReady", "methods", "<PERSON><PERSON><PERSON><PERSON>", "id", "navigateTo", "_this", "api", "getInfo", "companyId", "method", "then", "ret", "console", "log", "code", "userId", "setStorageSync", "wxUser", "roles", "avatar_url", "avatar", "toTeamList", "index", "logOut", "_this$user", "xcx_openid", "showToast", "mask", "icon", "removeStorageSync", "updateApp", "updateManager", "getUpdateManager", "onCheckForUpdate", "res", "hasUpdate", "onUpdateReady", "showModal", "content", "success", "confirm", "applyUpdate", "onUpdateFailed", "duration", "goAuthorize", "_this2", "_asyncToGenerator", "mark", "_callee", "_self", "wrap", "_callee$", "_context", "prev", "next", "login", "provider", "getUserInfo", "infoRes", "userInfo", "nick<PERSON><PERSON>", "jsCode", "token", "stop", "clickGrid", "$data", "navType", "goEdit", "onPullDownRefresh", "stopPullDownRefresh", "_vue", "_index", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}