<theme-wrap scoped-slots-compiler="augmented" vue-id="dcf11be6-1" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast vue-id="{{('dcf11be6-2')+','+('dcf11be6-1')}}" data-ref="toast" class="data-v-e1399738 vue-ref" bind:__l="__l"></u-toast><view class="data-v-e1399738"><u-navbar vue-id="{{('dcf11be6-3')+','+('dcf11be6-1')}}" title="团课详情" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-e1399738" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-e1399738"><u-form vue-id="{{('dcf11be6-4')+','+('dcf11be6-1')}}" model="{{form}}" labelWidth="140" data-ref="uForm" class="data-v-e1399738 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e1399738"><u-form-item vue-id="{{('dcf11be6-5')+','+('dcf11be6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="title" label="团课标题" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-6')+','+('dcf11be6-5')}}" readonly="{{true}}" inputAlign="right" border="none" placeholder=" " value="{{form.title}}" data-event-opts="{{[['^input',[['__set_model',['$0','title','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-7')+','+('dcf11be6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="attendance" label="最多人数" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-8')+','+('dcf11be6-7')}}" readonly="{{true}}" inputAlign="right" type="number" border="none" placeholder=" " value="{{form.attendance}}" data-event-opts="{{[['^input',[['__set_model',['$0','attendance','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-9')+','+('dcf11be6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="classTime" label="课程时间" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><view class="w-100 u-text-right u-font-30 data-v-e1399738">{{''+(form.classTime||" ")+''}}</view></u-form-item><u-form-item vue-id="{{('dcf11be6-10')+','+('dcf11be6-4')}}" required="{{true}}" borderBottom="{{true}}" prop="phone" label="联系人号码" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-11')+','+('dcf11be6-10')}}" readonly="{{true}}" inputAlign="right" border="none" placeholder=" " value="{{form.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-12')+','+('dcf11be6-4')}}" required="{{true}}" prop="price" label="团课价格" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-13')+','+('dcf11be6-12')}}" readonly="{{true}}" inputAlign="right" type="digital" border="none" placeholder=" " value="{{form.price}}" data-event-opts="{{[['^input',[['__set_model',['$0','price','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-14')+','+('dcf11be6-4')}}" required="{{true}}" prop="coachName" label="授课教练" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-15')+','+('dcf11be6-14')}}" readonly="{{true}}" inputAlign="right" type="digital" border="none" placeholder=" " value="{{form.coachName}}" data-event-opts="{{[['^input',[['__set_model',['$0','coachName','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-16')+','+('dcf11be6-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="beforeTimeBooking" label="禁止预约(课程开始前)" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-17')+','+('dcf11be6-16')}}" readonly="{{true}}" inputAlign="right" border="none" placeholder="分钟" value="{{form.beforeTimeBooking}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeBooking','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-18')+','+('dcf11be6-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="beforeTimeCancel" label="禁止取消(课程开始前)" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-19')+','+('dcf11be6-18')}}" readonly="{{true}}" inputAlign="right" type="digital" border="none" placeholder="分钟" value="{{form.beforeTimeCancel}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeCancel','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-20')+','+('dcf11be6-4')}}" labelWidth="200" required="{{true}}" borderBottom="{{true}}" prop="minAttendance" label="最低开课人数" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-21')+','+('dcf11be6-20')}}" readonly="{{true}}" inputAlign="right" type="digital" border="none" placeholder="人数(人)" value="{{form.minAttendance}}" data-event-opts="{{[['^input',[['__set_model',['$0','minAttendance','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('dcf11be6-22')+','+('dcf11be6-4')}}" labelWidth="200" required="{{true}}" prop="beforeTimeClose" label="未满足开课人数关闭课程时间" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('dcf11be6-23')+','+('dcf11be6-22')}}" readonly="{{true}}" inputAlign="right" type="digital" border="none" placeholder="(分钟)" value="{{form.beforeTimeClose}}" data-event-opts="{{[['^input',[['__set_model',['$0','beforeTimeClose','$event',[]],['form']]]]]}}" class="data-v-e1399738" bind:__l="__l"></u-input></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e1399738"><u-form-item vue-id="{{('dcf11be6-24')+','+('dcf11be6-4')}}" required="{{true}}" prop="banner" labelPosition="top" label="团课图片" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><view class="img-wrap u-flex u-m-t-20 u-row-center u-col-center border-16 overflow-hidden u-relative data-v-e1399738" style="height:180rpx;width:180rpx;border:1px solid #ddd;"><view class="u-relative w-100 h-100 data-v-e1399738"><image class="w-100 data-v-e1399738" src="{{form.banner}}" mode="widthFix" data-event-opts="{{[['tap',[['previewBanner',['$event']]]]]}}" catchtap="__e"></image></view></view></u-form-item></view><view class="u-m-b-34 u-p-34 border-16 bg-fff data-v-e1399738"><u-form-item vue-id="{{('dcf11be6-25')+','+('dcf11be6-4')}}" required="{{true}}" prop="remark" labelPosition="top" label="课程介绍" class="data-v-e1399738" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-p-20 data-v-e1399738">{{''+(form.remark||"")+''}}</view></u-form-item></view></u-form></view></view></theme-wrap>