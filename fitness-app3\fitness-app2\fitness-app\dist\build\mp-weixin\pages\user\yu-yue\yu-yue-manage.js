"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/user/yu-yue/yu-yue-manage"],{26418:function(t,e,n){var o=n(51372)["default"],a=n(81715)["createPage"];n(96910);r(n(923));var i=r(n(29384));function r(t){return t&&t.__esModule?t:{default:t}}o.__webpack_require_UNI_MP_PLUGIN__=n,a(i.default)},29384:function(t,e,n){n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return d}});var o,a={uNavbar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(n.bind(n,66372))},uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(n,43399))},uLoadmore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(n.bind(n,67727))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("0efa40b0-1")),o=n?{color:t.$getSSP("0efa40b0-1","content")["buttonTextColor"]}:null,a=n?t.$getSSP("0efa40b0-1","content"):null,i=n?{"font-weight":"bold"}:null,r=n?t.$getSSP("0efa40b0-1","content"):null,u=n?t.list.length:null,s=n&&u?t.__map(t.list,(function(e,n){var o=t.__get_orig(e),a=t.returnStatusName(e.status);return{$orig:o,m3:a}})):null,l=n&&!u?!t.list.length&&!t.loading:null,c=n?t.__map(Array(3),(function(e,n){var o=t.__get_orig(e),a=Array(3);return{$orig:o,l1:a}})):null;t.$mp.data=Object.assign({},{$root:{m0:n,a0:o,m1:a,a1:i,m2:r,g0:u,l0:s,g1:l,l2:c}})},r=[],u=n(77947),s=u["default"],l=n(18535),c=(0,l["default"])(s,i,r,!1,null,null,null,!1,a,o),d=c.exports},77947:function(t,e,n){var o=n(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=i(n(68466));function i(t){return t&&t.__esModule?t:{default:t}}function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function u(t,e,n){return(e=s(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function s(t){var e=l(t,"string");return"symbol"==r(e)?e:e+""}function l(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["default"]={data:function(){return u(u(u(u(u(u(u(u(u({currentType:0,list:[],currentTab:0,isJiaoLian:0,tabList:[{name:"待审核",id:0},{name:"已同意",id:1},{name:"已拒绝",id:2}]},"list",[]),"currentPage",1),"totalPages",1),"loading",!1),"loadStatus","loadmore"),"userId",""),"coachId",""),"isCoach",!1),"status","")},onPullDownRefresh:function(){this.currentPage=1,this.loadData(),this.$nextTick((function(){o.stopPullDownRefresh()}))},onLoad:function(t){this.isJiaoLian=+(null===t||void 0===t?void 0:t.isJiaoLian)||0,this.currentTab=+(null===t||void 0===t?void 0:t.index)||0,this.status=this.tabList[this.currentTab].id,this.loading=!0;var e=o.getStorageSync("userRoles");1==e.includes("shop_1_coach")?(this.isCoach=!0,this.coachId=o.getStorageSync("wxUserInfo").memberId,this.userId=""):(this.isCoach=!1,this.userId=o.getStorageSync("wxUserInfo").memberId,this.coachId=""),this.loadData()},onShow:function(){},methods:{changeCurrentType:function(t){this.currentType=t,this.currentPage=1,this.totalPages=1,this.loadData()},clickTab:function(t){var e=t.id;t.index;this.status=e,this.currentPage=1,this.loadData()},disagree:function(t){var e=this;o.showModal({title:"确认",content:"是否拒绝用户取消预约申请？",success:function(n){n.confirm&&a.default.getMyBookingAdminDisagree({data:{memberBookingIds:t}}).then((function(t){200==t.code&&(o.showToast({title:"取消预约成功",icon:"none"}),e.currentPage=1,e.loadData())}))}})},agree:function(t){var e=this;o.showModal({title:"确认",content:"是否同意用户取消预约申请？",success:function(n){n.confirm&&a.default.getMyBookingAdminAgree({data:{memberBookingIds:t}}).then((function(t){200==t.code&&(o.showToast({title:"确认预约成功",icon:"none"}),e.currentPage=1,e.loadData())}))}})},loadData:function(){this.getCoachList()},loadClick:function(){"loadmore"===this.loadStatus&&this.loadData()},getCoachList:function(){var t=this;console.log("am I coach",this.isCoach),a.default.getAdminYuyue({data:{superCancelStatus:this.status,pageNo:this.currentPage,pageSize:20,sort:"bookingTime desc"}}).then((function(e){t.list=e.rows,t.loadStatus=20*(t.currentPage-1)+(t.list.length||0)<e.total?"loadmore":"nomore","loadmore"===t.loadStatus&&t.currentPage++,t.$nextTick((function(){t.loading=!1}))})).catch((function(e){t.loadStatus="loadmore"}))},returnStatusName:function(t){var e="";switch(t){case"0":e="预约中";break;case"1":e="已预约";break;case"2":e="已签到";break;case"3":e="已取消";break;case"4":e="已失效";break;default:break}return e},handleManage:function(){o.navigateTo({url:"/pages/user/yu-yue/yu-yue-manage"})}}}}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(26418)}));t.O()}]);