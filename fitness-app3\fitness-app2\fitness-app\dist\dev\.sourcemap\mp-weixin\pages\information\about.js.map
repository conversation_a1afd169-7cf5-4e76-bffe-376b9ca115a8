{"version": 3, "file": "pages/information/about.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uZAEN;AACP,KAAK;AACL;AACA,aAAa,+VAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC1CA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,OAAA;IAqDA;EACA;EACAC,MAAA,WAAAA,OAAA;EACAC,MAAA,WAAAA,OAAAC,OAAA,GAEA;EACAC,OAAA,GAEA;AACA;;;;;;;;;;ACvGA;;;;;;;;;;;;;;;ACAAb,mBAAA;AAGA,IAAAc,IAAA,GAAAf,sBAAA,CAAAC,mBAAA;AACA,IAAAe,MAAA,GAAAhB,sBAAA,CAAAC,mBAAA;AAAgD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAHhD;AACAe,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLkG;AAClH;AACA,CAAyD;AACL;AACpD,CAAkE;;;AAGlE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBkf,CAAC,+DAAe,ydAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,i4BAAG,EAAC", "sources": ["webpack:///./src/pages/information/about.vue?b593", "uni-app:///src/pages/information/about.vue", "webpack:///./src/pages/information/about.vue?2459", "uni-app:///src/main.js", "webpack:///./src/pages/information/about.vue?2c77", "webpack:///./src/pages/information/about.vue?53e0", "webpack:///./src/pages/information/about.vue?2680", "webpack:///./src/pages/information/about.vue?18d2"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uNoNetwork: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-no-network/u-no-network\" */ \"uview-ui/components/u-no-network/u-no-network.vue\"\n      )\n    },\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uNotify: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notify/u-notify\" */ \"uview-ui/components/u-notify/u-notify.vue\"\n      )\n    },\n    uLoadingPage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-page/u-loading-page\" */ \"uview-ui/components/u-loading-page/u-loading-page.vue\"\n      )\n    },\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-parse/u-parse\" */ \"uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"6b8d2c65-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"6b8d2c65-1\", \"content\")[\"buttonTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"6b8d2c65-1\", \"content\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <themeWrap>\n    <template #content=\"{ navBarColor, buttonTextColor, buttonLightBgColor }\">\n\n      <view class=\"page\">\n        <!-- 主页navbar -->\n        <u-navbar :titleStyle=\"{ color: buttonTextColor }\" :bgColor=\"navBarColor\" :placeholder=\"true\" title=\"隐私政策\"\n          :autoBack=\"true\" :border=\"false\" :safeAreaInsetTop=\"true\">\n        </u-navbar>\n        <u-no-network></u-no-network>\n        <u-toast ref=\"uToast\" />\n        <u-notify ref=\"uNotify\" />\n\n        <view class=\"loading\" v-if=\"loading\">\n          <u-loading-page :loading=\"loading\"></u-loading-page>\n        </view>\n        <view class=\"content\" v-else>\n          <view class=\"content-index\" v-if=\"!isEmpty\">\n            <view class=\"section-title\">{{ title }}</view>\n            <u-parse class=\"content-text\" :content=\"content\" :selectable=\"true\" :tagStyle=\"parseTagStyle\"></u-parse>\n          </view>\n          <view class=\"empty-wrap u-p-t-80 u-flex-col u-row-center u-col-center\" v-else>\n            <image src=\"@/static/images/icons/empty.png\" mode=\"scaleToFill\"\n              style=\"width: 400rpx; max-height: 440rpx;\" />\n            <view class=\"u-p-t-40 u-tips-color\"> 暂无内容 </view>\n          </view>\n        </view>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\n  import api from \"@/common/api\";\n  export default {\n    data() {\n      return {\n        loading: false,\n        isEmpty: false,\n        permalink: \"\",\n        title: \"\",\n        content: `《用户服务协议及隐私保护政策》<br>\n一、协议的目的与范围<br>\n1.1 规范用户使用本网站服务的行为。<br>\n1.2 明确隐私保护政策并告知用户隐私信息的采集及使用情况。<br>\n1.3 本协议合用于所有使用本网站服务的用户。<br>\n二、用户的权利与义务<br>\n2.1 用户必须提供真实准确的个人信息，如有变更应及时更新。<br>\n2.2 用户不得利用本网站服务从事任何违法行为。<br>\n2.3 用户应妥善保管自己的登录账号和密码，不得将其转让或者\n出借。<br>\n2.4 用户不得干扰本网站的正常运营，包括但不限于发送垃圾\n邮件、病毒等。<br>\n2.5 用户在本网站发表的所有内容应符合国家相关法律法规及\n社会公德。<br>\n三、隐私保护与信息采集<br>\n3.1 本网站承诺保护用户的个人隐私信息，不会将其泄露、出\n售或者提供给任何第三方。<br>\n3.2 本网站可能采集用户的个人信息，包括但不限于姓名、地\n址、、电子邮件地址等。<br>\n3.3 本网站可能采集用户的行为信息，包括但不限于浏览记录、\n搜索记录等。<br>\n3.4 本网站可能使用 cookie 等技术为用户提供更好的服务。<br>\n四、责任限制<br>\n4.1 用户因自身行为造成的任何损失，本网站不承担责任。<br>\n4.2 由于第三方原因造成的任何损失，本网站也不承担责任。<br>\n4.3 本网站不保证服务的稳定性、安全性、合用性和准确性。<br>\n4.4 用户使用本网站服务及因此而参加某项活动所导致的任何\n后果均由用户自行承担，本网站不承担任何责任。<br>\n五、协议的修改与解除<br>\n5.1 本协议的内容、生效时间及修订方式均由本网站决定。<br>\n5.2 用户可以根据本网站的操作流程进行协议的解除。<br>\n六、争议解决<br>\n6.1 本协议的解释、执行和争议解决必须遵循中华人民共和国\n法律法规的规定。<br>\n6.2 双方因本协议的履行及执行发生争议的，应商议解决；协\n商不成的，应向本网站所在地人民法院提起诉讼。<br>\n所涉及附件如下：<br>\n附件一：隐私政策声明<br>\n所涉及的法律名词及注释：<br>\n1. 中华人民共和国法律法规：指中华人民共和国现行有效的法\n律、行政法规、部门规章以及国务院决定等具有法律效力的规范性\n文件。<br>\n2. 人民法院：指根据中华人民共和国宪法及法律设立的审判机\n关。<br>\n3. 商议：指当事人通过和谈商议相互商议，寻求一致意见的过\n程。<br>\n在实际执行过程中可能遇到的艰难及解决办法：<br>\n1. 各种争议的解决方法：在遇到任何争议时，用户可通过商议\n解决，商议不成可向本网站所在地人民法院提起诉讼。<br>\n2. 隐私泄露问题的解决方法：本网站将加强技术保障措施，防\n止用户隐私信息的泄露、出售或者提供给任何第三方。<br>\n3. 采集个人信息合法性问题解决方法：本网站将遵守中华人民\n共和国法律法规的规定，确保用户个人信息的合法采集和使用。`,\n      };\n    },\n    onShow() {},\n    onLoad(options) {\n      \n    },\n    methods: {\n      \n    },\n  };\n</script>\n\n<style lang=\"scss\">\n  .content {\n    margin: 40upx 30upx;\n    min-height: 90vh;\n    height: auto;\n    border-radius: 36rpx;\n    background-color: #fff;\n\n    .content-index {\n      min-height: 80vh;\n      padding: 30upx 40rpx;\n      border-radius: 10upx;\n    }\n\n    .section-title {\n      font-size: 46rpx;\n    }\n  }\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/information/about.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./about.vue?vue&type=template&id=6690ecc1&\"\nvar renderjs\nimport script from \"./about.vue?vue&type=script&lang=js&\"\nexport * from \"./about.vue?vue&type=script&lang=js&\"\nimport style0 from \"./about.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/information/about.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=template&id=6690ecc1&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "data", "loading", "isEmpty", "permalink", "title", "content", "onShow", "onLoad", "options", "methods", "_vue", "_about", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}