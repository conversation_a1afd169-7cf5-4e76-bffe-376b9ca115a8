<theme-wrap scoped-slots-compiler="augmented" vue-id="cda3ce2e-1" class="data-v-f6ca0290" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-f6ca0290" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('cda3ce2e-2')+','+('cda3ce2e-1')}}" title="会员卡明细详情" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-f6ca0290" bind:__l="__l"></u-navbar><view class="formView data-v-f6ca0290"><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">所属场馆:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-3')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.shopName}}" data-event-opts="{{[['^input',[['__set_model',['$0','shopName','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">会员昵称:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-4')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.memberName}}" data-event-opts="{{[['^input',[['__set_model',['$0','memberName','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">会员卡名称:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-5')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.cardName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardName','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">会员卡类型:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-6')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.cardType1}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardType1','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">有效天数:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-7')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.validDays}}" data-event-opts="{{[['^input',[['__set_model',['$0','validDays','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">有效次数:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-8')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.validTimes}}" data-event-opts="{{[['^input',[['__set_model',['$0','validTimes','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">会员卡价格:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-9')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.cardPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardPrice','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">付费价格:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-10')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.payPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','payPrice','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">剩余天数:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-11')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.restDays}}" data-event-opts="{{[['^input',[['__set_model',['$0','restDays','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">剩余次数:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-12')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.restTimes}}" data-event-opts="{{[['^input',[['__set_model',['$0','restTimes','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">付款时间:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-13')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.payTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','payTime','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view><view class="formList data-v-f6ca0290"><view class="data-v-f6ca0290">状态:</view><u--input bind:input="__e" vue-id="{{('cda3ce2e-14')+','+('cda3ce2e-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.status1}}" data-event-opts="{{[['^input',[['__set_model',['$0','status1','$event',[]],['formList']]]]]}}" class="data-v-f6ca0290" bind:__l="__l"></u--input></view></view><view class="whiteView data-v-f6ca0290"></view><view class="bottonBtn u-flex data-v-f6ca0290"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-f6ca0290" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">返回</view></view></view></theme-wrap>