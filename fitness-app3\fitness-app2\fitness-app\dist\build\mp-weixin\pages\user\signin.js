(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/user/signin"],{57547:function(){},83566:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return m}});var r,o={uToast:function(){return n.e("node-modules/uview-ui/components/u-toast/u-toast").then(n.bind(n,67559))},uNavbar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(n.bind(n,66372))},uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(n,43399))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("2a457929-1")),r=n?{color:t.$getSSP("2a457929-1","content")["navBarTextColor"]}:null,o=n?t.$getSSP("2a457929-1","content"):null,a=n?t.$getSSP("2a457929-1","content"):null,i=n?t.formatNum(t.m):null,u=n?t.__map(t.dates,(function(e,n){var r=t.__get_orig(e),o=t.isMarkDay(e.year,e.month,e.date)&&e.isCurM,a=t.isToday(e.year,e.month,e.date),i=t.isWorkDay(e.year,e.month,e.date),u=Number(e.date);return{$orig:r,m4:o,m5:a,m6:i,m7:u}})):null;t._isMounted||(t.e0=function(e){return t.uni.navigateBack()}),t.$mp.data=Object.assign({},{$root:{m0:n,a0:r,m1:o,m2:a,m3:i,l0:u}})},i=[],u=n(86086),c=u["default"],s=n(57547),h=n.n(s),f=(h(),n(18535)),l=(0,f["default"])(c,a,i,!1,null,"6f3d8f53",null,!1,o,r),m=l.exports},86086:function(t,e,n){"use strict";var r=n(81715)["default"];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=i(n(68466));function i(t){return t&&t.__esModule?t:{default:t}}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",h=i.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,i=Object.create(o.prototype),u=new M(r||[]);return a(i,"_invoke",{value:O(t,n,u)}),i}function m(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var y="suspendedStart",d="suspendedYield",p="executing",v="completed",g={};function w(){}function b(){}function _(){}var x={};f(x,c,(function(){return this}));var k=Object.getPrototypeOf,D=k&&k(k(j([])));D&&D!==n&&r.call(D,c)&&(x=D);var L=_.prototype=w.prototype=Object.create(x);function E(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function N(t,e){function n(a,i,u,c){var s=m(t[a],t,i);if("throw"!==s.type){var h=s.arg,f=h.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):e.resolve(f).then((function(t){h.value=t,u(h)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function O(e,n,r){var o=y;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===a)throw i;return{value:t,done:!0}}for(r.method=a,r.arg=i;;){var u=r.delegate;if(u){var c=S(u,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===y)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var s=m(e,n,r);if("normal"===s.type){if(o=r.done?v:d,s.arg===g)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=v,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var a=m(o,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,g;var i=a.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function j(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:b,configurable:!0}),b.displayName=f(_,h,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,h,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(N.prototype),f(N.prototype,s,(function(){return this})),e.AsyncIterator=N,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new N(l(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},E(L),f(L,h,"Generator"),f(L,c,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=j,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return u.type="throw",u.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),P(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;P(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:j(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function c(t,e,n,r,o,a,i){try{var u=t[a](i),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(t){c(a,r,o,i,u,"next",t)}function u(t){c(a,r,o,i,u,"throw",t)}i(void 0)}))}}e["default"]={name:"ren-calendar",data:function(){return{list1:[{name:"每日签到"},{name:"团课签到"}],markDays:[],weekstart:0,disabledAfter:!1,collapsible:!0,open:!0,weektext:["日","一","二","三","四","五","六"],y:(new Date).getFullYear(),m:(new Date).getMonth()+1,dates:[{}],positionTop:0,monthOpen:!0,choose:""}},created:function(){var t=this;return s(u().mark((function e(){return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.dates=t.monthDay(t.y,t.m),console.log(t.monthDay(t.y,t.m)),t.getdata(1),!t.open&&t.toggle();case 4:case"end":return e.stop()}}),e)})))()},mounted:function(){this.choose=this.getToday().date},computed:{weekDay:function(){return this.weektext.slice(this.weekstart).concat(this.weektext.slice(0,this.weekstart))},height:function(){return this.dates.length/7*80+"rpx"}},methods:{getdata:function(t){var e=this;return s(u().mark((function n(){var o;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,a.default.mySignIn({data:{year:e.y,month:e.m,type:t,shopId:r.getStorageSync("nowShopId")}});case 2:o=n.sent,console.log(o),e.markDays=o.data;case 5:case"end":return n.stop()}}),n)})))()},clicktab:function(t){console.log(t),"团课签到"==t.name?this.getdata(2):this.getdata(1)},formatNum:function(t){var e=Number(t);return e<10?"0"+e:e},getToday:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth(),r=t.getDate(),o=(new Date).getDay(),a=["日","一","二","三","四","五","六"],i="星期"+a[o],u={date:e+"-"+this.formatNum(n+1)+"-"+this.formatNum(r),week:i};return u},monthDay:function(t,e){for(var n=[],r=Number(e),o=new Date(t,r-1,1).getDay(),a=new Date(t,r,0).getDate(),i=new Date(t,r-2,0).getDate(),u=7==this.weekstart?0:this.weekstart,c=function(){return o==u?0:o>u?o-u:7-u+o}(),s=7-(c+a)%7,h=1;h<=c;h++)n.push({date:this.formatNum(i-c+h),day:u+h-1||7,month:r-1>=0?this.formatNum(r-1):12,year:r-1>=0?t:t-1});for(var f=1;f<=a;f++)n.push({date:this.formatNum(f),day:f%7+o-1||7,month:this.formatNum(r),year:t,isCurM:!0});for(var l=1;l<=s;l++)n.push({date:this.formatNum(l),day:(a+c+u+l-1)%7||7,month:r+1<=11?this.formatNum(r+1):0,year:r+1<=11?t:t+1});return n},isWorkDay:function(t,e,n){return!0},isFutureDay:function(t,e,n){var r="".concat(t,"/").concat(e,"/").concat(n),o=new Date(r.replace(/-/g,"/")),a=o.getTime(),i=(new Date).getTime();return a>i},isMarkDay:function(t,e,n){for(var r=!1,o=0;o<this.markDays.length;o++){var a="".concat(t,"-").concat(e,"-").concat(n);if(this.markDays[o].signInTime==a){r=!0;break}}return r},isToday:function(t,e,n){var r=t+"-"+e+"-"+n,o=this.getToday().date;return r==o},toggle:function(){var t=this;if(this.monthOpen=!this.monthOpen,this.monthOpen)this.positionTop=0;else{var e=-1;this.dates.forEach((function(n,r){t.isToday(n.year,n.month,n.date)&&(e=r)})),this.positionTop=80*-((Math.ceil((e+1)/7)||1)-1)}},selectOne:function(t,e){},changYearMonth:function(t,e){this.dates=this.monthDay(t,e),this.y=t,this.m=e},changeMonth:function(t){"pre"==t?this.m+1==2?(this.m=12,this.y=this.y-1):this.m=this.m-1:this.m+1==13?(this.m=1,this.y=this.y+1):this.m=this.m+1,this.dates=this.monthDay(this.y,this.m)}}}},98215:function(t,e,n){"use strict";var r=n(51372)["default"],o=n(81715)["createPage"];n(96910);i(n(923));var a=i(n(83566));function i(t){return t&&t.__esModule?t:{default:t}}r.__webpack_require_UNI_MP_PLUGIN__=n,o(a.default)}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(98215)}));t.O()}]);