(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-image/u-image"],{3309:function(){},11590:function(i,n,t){"use strict";t.r(n),t.d(n,{__esModule:function(){return d.__esModule},default:function(){return g}});var e,o={uTransition:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-transition/u-transition")]).then(t.bind(t,68270))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(t,78278))}},r=function(){var i=this,n=i.$createElement,t=(i._self._c,i.__get_style([i.wrapStyle,i.backgroundStyle])),e=i.isError||"circle"==i.shape?null:i.$u.addUnit(i.radius),o=i.isError?null:i.$u.addUnit(i.width),r=i.isError?null:i.$u.addUnit(i.height),u=i.showLoading&&i.loading&&"circle"!=i.shape?i.$u.addUnit(i.radius):null,d=i.showLoading&&i.loading?i.$u.addUnit(i.width):null,a=i.showLoading&&i.loading?i.$u.addUnit(i.height):null,s=i.showError&&i.isError&&!i.loading&&"circle"!=i.shape?i.$u.addUnit(i.radius):null,l=i.showError&&i.isError&&!i.loading?i.$u.addUnit(i.width):null,c=i.showError&&i.isError&&!i.loading?i.$u.addUnit(i.height):null;i.$mp.data=Object.assign({},{$root:{s0:t,g0:e,g1:o,g2:r,g3:u,g4:d,g5:a,g6:s,g7:l,g8:c}})},u=[],d=t(91697),a=d["default"],s=t(3309),l=t.n(s),c=(l(),t(18535)),h=(0,c["default"])(a,r,u,!1,null,"9c8ca008",null,!1,o,e),g=h.exports},91697:function(i,n,t){"use strict";var e=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var o=r(t(75515));function r(i){return i&&i.__esModule?i:{default:i}}n["default"]={name:"u-image",mixins:[e.$u.mpMixin,e.$u.mixin,o.default],data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{},show:!1}},watch:{src:{immediate:!0,handler:function(i){i?(this.isError=!1,this.loading=!0):(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var i={};return i.width=this.$u.addUnit(this.width),i.height=this.$u.addUnit(this.height),i.borderRadius="circle"==this.shape?"10000px":e.$u.addUnit(this.radius),i.overflow=+this.radius>0?"hidden":"visible",e.$u.deepMerge(i,e.$u.addStyle(this.customStyle))}},mounted:function(){this.show=!0},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(i){this.loading=!1,this.isError=!0,this.$emit("error",i)},onLoadHandler:function(i){this.loading=!1,this.isError=!1,this.$emit("load",i),this.removeBgColor()},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-image/u-image-create-component"],{},function(i){i("81715")["createComponent"](i(11590))}]);