<theme-wrap scoped-slots-compiler="augmented" vue-id="72d25f36-1" class="data-v-4a01e30e" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-4a01e30e"><u-navbar vue-id="{{('72d25f36-2')+','+('72d25f36-1')}}" title="设备列表" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-4a01e30e" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-4a01e30e"><block wx:if="{{$root.g0}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item})}}" class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-4a01e30e" bindtap="__e"><view class="u-flex u-col-center u-row-start data-v-4a01e30e" style="flex-wrap:no-wrap;overflow:hidden;"><view class="w-100 u-p-l-20 data-v-4a01e30e"><view class="u-line-1 w-100 data-v-4a01e30e">{{''+item.name+''}}</view><view class="u-tips-color u-font-26 data-v-4a01e30e" style="margin-top:30rpx;">{{'sn：'+item.sn+''}}</view><view class="u-tips-color u-font-26 data-v-4a01e30e" style="margin-top:10rpx;">{{'是否在线：'+(item.state==0?'在线':'离线')+''}}</view><view class="u-tips-color u-font-26 data-v-4a01e30e" style="margin-top:10rpx;">{{'设备状态：'+(item.status==0?'正常':'停用')+''}}</view><view class="u-tips-color u-font-26 data-v-4a01e30e" style="margin-top:10rpx;">{{'最后在线时间：'+item.lastLinkTime+''}}</view></view></view><view class="btn-wrap data-v-4a01e30e"><view data-event-opts="{{[['tap',[['start',['$0'],[[['list','',index]]]]]]]}}" class="{{['data-v-4a01e30e',item.status==0?'u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 border-8 btc text-no-wrap lbc u-font-26 font-bold':'u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10']}}" style="border:1px solid;border-color:buttonLightBgColor;" catchtap="__e">开门</view><view data-event-opts="{{[['tap',[['jumpface',['$0'],[[['list','',index]]]]]]]}}" class="u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10 data-v-4a01e30e" style="border:1px solid;border-color:buttonLightBgColor;" catchtap="__e">人脸下发</view><view data-event-opts="{{[['tap',[['del',['$0'],[[['list','',index]]]]]]]}}" class="u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10 data-v-4a01e30e" style="border:1px solid;border-color:buttonLightBgColor;" catchtap="__e">删除</view></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-4a01e30e"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-4a01e30e"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-4a01e30e">暂无活动</view></view></block></view><view class="bottom-blk bg-fff w-100 u-p-40 data-v-4a01e30e"><u-button vue-id="{{('72d25f36-3')+','+('72d25f36-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['toAddCourse']]]]}}" bind:click="__e" class="data-v-4a01e30e" bind:__l="__l" vue-slots="{{['default']}}">添加设备</u-button></view></view></theme-wrap>