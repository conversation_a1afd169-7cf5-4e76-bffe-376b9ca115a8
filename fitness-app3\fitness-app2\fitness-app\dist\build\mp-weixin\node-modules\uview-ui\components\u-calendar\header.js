(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-calendar/header"],{10700:function(){},38956:function(e,t,n){"use strict";var u;n.r(t),n.d(t,{__esModule:function(){return i.__esModule},default:function(){return p}});var a,l=function(){var e=this,t=e.$createElement;e._self._c},o=[],i=n(44430),c=i["default"],s=n(10700),d=n.n(s),r=(d(),n(18535)),f=(0,r["default"])(c,l,o,!1,null,"3af90f89",null,!1,u,a),p=f.exports},44430:function(e,t,n){"use strict";var u=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;t["default"]={name:"u-calendar-header",mixins:[u.$u.mpMixin,u.$u.mixin],props:{title:{type:String,default:""},subtitle:{type:String,default:""},showTitle:{type:Boolean,default:!0},showSubtitle:{type:Boolean,default:!0}},data:function(){return{}},methods:{name:function(){}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-calendar/header-create-component"],{},function(e){e("81715")["createComponent"](e(38956))}]);