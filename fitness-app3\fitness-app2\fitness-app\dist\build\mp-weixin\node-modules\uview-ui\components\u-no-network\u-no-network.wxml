<u-overlay vue-id="0c4aa10d-1" show="{{!isConnected}}" zIndex="{{zIndex}}" customStyle="{{({backgroundColor:'#fff',display:'flex',justifyContent:'center'})}}" data-event-opts="{{[['^touchmove',[['noop',['$event']]]]]}}" catch:touchmove="__e" class="data-v-6e0907b3" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-no-network data-v-6e0907b3"><u-icon class="u-no-network__error-icon data-v-6e0907b3" vue-id="{{('0c4aa10d-2')+','+('0c4aa10d-1')}}" name="{{image}}" size="150" imgMode="widthFit" bind:__l="__l"></u-icon><text class="u-no-network__tips data-v-6e0907b3">{{tips}}</text><view class="u-no-network__retry data-v-6e0907b3"><u-button vue-id="{{('0c4aa10d-3')+','+('0c4aa10d-1')}}" size="mini" text="重试" type="primary" plain="{{true}}" data-event-opts="{{[['^click',[['retry']]]]}}" bind:click="__e" class="data-v-6e0907b3" bind:__l="__l"></u-button></view></view></u-overlay>