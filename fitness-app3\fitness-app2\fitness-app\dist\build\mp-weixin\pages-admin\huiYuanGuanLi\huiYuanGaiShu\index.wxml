<theme-wrap scoped-slots-compiler="augmented" vue-id="3e3d8e86-1" class="data-v-76d5c8e5" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-76d5c8e5" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('3e3d8e86-2')+','+('3e3d8e86-1')}}" title="会员概况" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-76d5c8e5" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-76d5c8e5"><view class="u-p-t-20 u-p-b-20 u-p-r-40 u-p-l-40 w-100 border-16 u-m-b-20 bg-fff data-v-76d5c8e5"><u-cell-group vue-id="{{('3e3d8e86-3')+','+('3e3d8e86-1')}}" border="{{false}}" class="data-v-76d5c8e5" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('3e3d8e86-4')+','+('3e3d8e86-3')}}" title="会员卡（月卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-5')+','+('3e3d8e86-3')}}" title="会员卡（100天卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-6')+','+('3e3d8e86-3')}}" title="会员卡（455天卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-7')+','+('3e3d8e86-3')}}" title="私教课（10次卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-8')+','+('3e3d8e86-3')}}" title="私教课（20次卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-9')+','+('3e3d8e86-3')}}" title="私教课（30次卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-10')+','+('3e3d8e86-3')}}" title="私教课（50次卡）" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell><u-cell vue-id="{{('3e3d8e86-11')+','+('3e3d8e86-3')}}" title="会员卡（1天卡）" border="{{false}}" isLink="{{true}}" arrow-direction="right" url="{{src}}" class="data-v-76d5c8e5" bind:__l="__l"></u-cell></u-cell-group></view></view></view></theme-wrap>