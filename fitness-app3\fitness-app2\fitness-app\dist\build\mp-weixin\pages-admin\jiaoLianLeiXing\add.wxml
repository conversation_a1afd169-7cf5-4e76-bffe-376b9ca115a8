<theme-wrap scoped-slots-compiler="augmented" vue-id="a5c64810-1" class="data-v-4087fe2b" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-4087fe2b" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('a5c64810-2')+','+('a5c64810-1')}}" title="课程详情" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-4087fe2b" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-4087fe2b"><view class="formView data-v-4087fe2b"><view class="formList data-v-4087fe2b"><view class="data-v-4087fe2b">教练类型名称:</view><u--input bind:input="__e" vue-id="{{('a5c64810-3')+','+('a5c64810-1')}}" border="{{false}}" value="{{formList.coachTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','coachTypeName','$event',[]],['formList']]]]]}}" class="data-v-4087fe2b" bind:__l="__l"></u--input></view></view></view><view class="bottonBtn u-flex data-v-4087fe2b"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-4087fe2b" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增类型</view></view></view></theme-wrap>