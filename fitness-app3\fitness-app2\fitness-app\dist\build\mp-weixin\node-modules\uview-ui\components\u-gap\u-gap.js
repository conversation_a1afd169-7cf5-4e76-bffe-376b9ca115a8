(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-gap/u-gap"],{9932:function(e,t,n){"use strict";var u;n.r(t),n.d(t,{__esModule:function(){return l.__esModule},default:function(){return f}});var o,a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__get_style([e.gapStyle]));e.$mp.data=Object.assign({},{$root:{s0:n}})},i=[],l=n(85181),s=l["default"],c=n(60408),r=n.n(c),d=(r(),n(18535)),p=(0,d["default"])(s,a,i,!1,null,"206e829c",null,!1,u,o),f=p.exports},60408:function(){},85181:function(e,t,n){"use strict";var u=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var o=a(n(86422));function a(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-gap",mixins:[u.$u.mpMixin,u.$u.mixin,o.default],computed:{gapStyle:function(){var e={backgroundColor:this.bgColor,height:u.$u.addUnit(this.height),marginTop:u.$u.addUnit(this.marginTop),marginBottom:u.$u.addUnit(this.marginBottom)};return u.$u.deepMerge(e,u.$u.addStyle(this.customStyle))}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-gap/u-gap-create-component"],{},function(e){e("81715")["createComponent"](e(9932))}]);