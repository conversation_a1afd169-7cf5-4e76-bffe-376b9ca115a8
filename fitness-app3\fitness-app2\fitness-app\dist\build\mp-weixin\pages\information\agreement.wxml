<theme-wrap scoped-slots-compiler="augmented" vue-id="d3149bf0-1" bind:__l="__l" vue-slots="{{['content']}}"><view class="page" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('d3149bf0-2')+','+('d3149bf0-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="会员入会协议" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><u-no-network vue-id="{{('d3149bf0-3')+','+('d3149bf0-1')}}" bind:__l="__l"></u-no-network><u-toast class="vue-ref" vue-id="{{('d3149bf0-4')+','+('d3149bf0-1')}}" data-ref="uToast" bind:__l="__l"></u-toast><u-notify class="vue-ref" vue-id="{{('d3149bf0-5')+','+('d3149bf0-1')}}" data-ref="uNotify" bind:__l="__l"></u-notify><block wx:if="{{loading}}"><view class="loading"><u-loading-page vue-id="{{('d3149bf0-6')+','+('d3149bf0-1')}}" loading="{{loading}}" bind:__l="__l"></u-loading-page></view></block><block wx:else><view class="content"><block wx:if="{{!isEmpty}}"><view class="content-index"><view class="section-title">{{title}}</view><u-parse class="content-text" vue-id="{{('d3149bf0-7')+','+('d3149bf0-1')}}" content="{{content}}" selectable="{{true}}" tagStyle="{{parseTagStyle}}" bind:__l="__l"></u-parse></view></block><block wx:else><view class="empty-wrap u-p-t-80 u-flex-col u-row-center u-col-center"><image style="width:400rpx;max-height:440rpx;" src="/static/images/icons/empty.png" mode="scaleToFill"></image><view class="u-p-t-40 u-tips-color">暂无内容</view></view></block></view></block></view></theme-wrap>