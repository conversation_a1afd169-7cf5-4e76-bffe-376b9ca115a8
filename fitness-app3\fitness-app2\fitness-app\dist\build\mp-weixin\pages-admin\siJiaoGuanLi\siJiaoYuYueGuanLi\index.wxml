<theme-wrap scoped-slots-compiler="augmented" vue-id="8559a144-1" class="data-v-39cf3550" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-39cf3550"><u-navbar vue-id="{{('8559a144-2')+','+('8559a144-1')}}" title="私教预约管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-39cf3550" bind:__l="__l"></u-navbar></view><view class="tabsView u-border-bottom data-v-39cf3550"><u-tabs vue-id="{{('8559a144-3')+','+('8559a144-1')}}" activeStyle="{{$root.a1}}" lineColor="{{$root.m3['buttonLightBgColor']}}" list="{{list1}}" data-event-opts="{{[['^click',[['click']]]]}}" bind:click="__e" class="data-v-39cf3550" bind:__l="__l"></u-tabs></view><scroll-view scroll-y="true" class="data-v-39cf3550"><view class="reservation data-v-39cf3550"><view class="title data-v-39cf3550">未处理预约</view><view class="reservationView border-16 data-v-39cf3550"><view class="resTop u-flex data-v-39cf3550"><view class="u-flex u-flex-1 data-v-39cf3550"><view class="u-m-r-10 title data-v-39cf3550">减脂</view><view class="resIcon data-v-39cf3550" style="{{'background:'+($root.m4['buttonLightBgColor'])+';'+('color:'+($root.m5['buttonTextColor'])+';')}}">代</view></view><view class="u-flex data-v-39cf3550"><view class="u-m-r-10 title data-v-39cf3550">克里斯丁</view><u-avatar vue-id="{{('8559a144-4')+','+('8559a144-1')}}" src="{{src}}" size="32" class="data-v-39cf3550" bind:__l="__l"></u-avatar></view></view><view class="resCenter data-v-39cf3550"><view class="value bold data-v-39cf3550">昨天 18:30~19:30</view></view><view class="resBottom u-flex data-v-39cf3550"><view class="u-flex u-flex-1 data-v-39cf3550"><u-avatar vue-id="{{('8559a144-5')+','+('8559a144-1')}}" src="{{src}}" size="32" class="data-v-39cf3550" bind:__l="__l"></u-avatar><view class="u-m-l-10 title data-v-39cf3550">张三</view></view><view class="u-flex data-v-39cf3550"><view data-event-opts="{{[['tap',[['refuse']]]]}}" class="moreBtn u-m-r-10 data-v-39cf3550" style="{{'color:'+($root.m6['buttonLightBgColor'])+';'+('border-color:'+($root.m7['buttonLightBgColor'])+';')}}" bindtap="__e">拒绝</view><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="addHuiYuan data-v-39cf3550" style="{{'background:'+($root.m8['buttonLightBgColor'])+';'+('color:'+($root.m9['buttonTextColor'])+';')+('border-color:'+($root.m10['buttonLightBgColor'])+';')}}" bindtap="__e">确认</view></view></view></view></view></scroll-view><u-modal vue-id="{{('8559a144-6')+','+('8559a144-1')}}" show="{{showRefuse}}" title="请输入拒绝理由" class="data-v-39cf3550" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-39cf3550"><u--input bind:input="__e" vue-id="{{('8559a144-7')+','+('8559a144-6')}}" placeholder value="{{refuseText}}" data-event-opts="{{[['^input',[['__set_model',['','refuseText','$event',[]]]]]]}}" class="data-v-39cf3550" bind:__l="__l"></u--input></view></u-modal></view></theme-wrap>