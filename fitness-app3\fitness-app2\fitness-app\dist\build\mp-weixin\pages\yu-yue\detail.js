(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/yu-yue/detail"],{7307:function(t,e,s){"use strict";var o=s(51372)["default"],n=s(81715)["createPage"];s(96910);u(s(923));var i=u(s(35144));function u(t){return t&&t.__esModule?t:{default:t}}o.__webpack_require_UNI_MP_PLUGIN__=s,n(i.default)},35144:function(t,e,s){"use strict";s.r(e),s.d(e,{__esModule:function(){return a.__esModule},default:function(){return m}});var o,n={uNavbar:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(s.bind(s,66372))},uLoadingIcon:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(s.bind(s,94597))},uButton:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-button/u-button")]).then(s.bind(s,65610))},uPopup:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(s.bind(s,85432))},uSearch:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-search/u-search")]).then(s.bind(s,9450))},uList:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-list/u-list")]).then(s.bind(s,60312))},uListItem:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-list-item/u-list-item")]).then(s.bind(s,75539))},uCell:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-cell/u-cell")]).then(s.bind(s,68675))}},i=function(){var t=this,e=t.$createElement,s=(t._self._c,t.$hasSSP("563484e7-1")),o=s?{color:t.$getSSP("563484e7-1","content")["buttonTextColor"]}:null,n=s?t.$getSSP("563484e7-1","content"):null,i=s?t.courses.length:null,u=s&&null!==t.currentIndex&&!t.isCoach?t.$getSSP("563484e7-1","content"):null,a=s&&null!==t.currentIndex&&!t.isCoach&&null!==t.currentIndex?t.$getSSP("563484e7-1","content"):null,c=s&&null!==t.currentIndex&&t.isCoach&&null!==t.currentIndex?t.$getSSP("563484e7-1","content"):null;t._isMounted||(t.e0=function(e){t.showUserSelect=!1}),t.$mp.data=Object.assign({},{$root:{m0:s,a0:o,m1:n,g0:i,m2:u,m3:a,m4:c}})},u=[],a=s(48086),c=a["default"],d=s(64513),r=s.n(d),l=(r(),s(18535)),h=(0,l["default"])(c,i,u,!1,null,null,null,!1,n,o),m=h.exports},48086:function(t,e,s){"use strict";var o=s(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s(68466));function i(t){return t&&t.__esModule?t:{default:t}}var u=function(){s.e("components/calendar").then(function(){return resolve(s(21877))}.bind(null,s))["catch"](s.oe)},a=function(){s.e("components/official-qrcode").then(function(){return resolve(s(65602))}.bind(null,s))["catch"](s.oe)};e["default"]={components:{calendar:u,officialQrcode:a},data:function(){return{list:[],loading:!1,nickname:"",currentIndex:null,courses:[{text:"00:00",id:1,status:2},{text:"00:30",id:2,status:3},{text:"01:00",id:3,status:1},{text:"01:30",id:4,status:1},{text:"02:00",id:5,status:1},{text:"02:30",id:6,status:1},{text:"03:00",id:7,status:1},{text:"03:30",id:8,status:1},{text:"04:00",id:9,status:1},{text:"04:30",id:10,status:1},{text:"05:00",id:11,status:1},{text:"05:30",id:12,status:1},{text:"06:00",id:13,status:1},{text:"06:30",id:14,status:1},{text:"07:00",id:15,status:1},{text:"07:30",id:16,status:1},{text:"08:00",id:17,status:1},{text:"08:30",id:18,status:1},{text:"09:00",id:19,status:1},{text:"09:30",id:20,status:1},{text:"10:00",id:21,status:1},{text:"10:30",id:22,status:1},{text:"11:00",id:23,status:1},{text:"11:30",id:24,status:1},{text:"12:00",id:25,status:1},{text:"12:30",id:26,status:1},{text:"13:00",id:27,status:1},{text:"13:30",id:28,status:1},{text:"14:00",id:29,status:1},{text:"14:30",id:30,status:1},{text:"15:00",id:31,status:1},{text:"15:30",id:32,status:1},{text:"16:00",id:33,status:1},{text:"16:30",id:34,status:1},{text:"17:00",id:35,status:1},{text:"17:30",id:36,status:1},{text:"18:00",id:37,status:1},{text:"18:30",id:38,status:1},{text:"19:00",id:39,status:1},{text:"19:30",id:40,status:1},{text:"20:00",id:41,status:1},{text:"20:30",id:42,status:1},{text:"21:00",id:43,status:1},{text:"21:30",id:44,status:1},{text:"22:00",id:45,status:1},{text:"22:30",id:46,status:1},{text:"23:00",id:47,status:1},{text:"23:30",id:48,status:1}],course_id:"",activeDate:"",currentPage:"",totalPages:"",detail:{courseName:""},courseId:"",courseName:"",memberId:"",memberName:"",shopId:"",shopName:"",showQrcode:!1,isCoach:!1,userList:[],phoneKey:"",showUserSelect:!1,userId:""}},onLoad:function(){var t=this;this.shopId=o.getStorageSync("nowShopId"),this.shopName=o.getStorageSync("nowShopName");var e=o.getStorageSync("userRoles");1==e.includes("shop_1_coach")?this.isCoach=!0:this.isCoach=!1;var s=this.getOpenerEventChannel();s.on("jiaoLianInfo",(function(e){console.log("教练信息：",e),t.activeDate=e.time,t.courseId=e.courseId,t.courseName=e.courseName,t.memberId=e.coachId,t.memberName=e.coachName,t.loadData()}))},onShow:function(){},methods:{getUserList:function(){n.default.getUserList({phone:this.phoneKey}).then((function(t){console.log(t)})).catch((function(t){console.log(t)}))},handleUserItem:function(t){var e=this;o.showModal({title:"确认",content:"确认给".concat(this.userList[t].nickName,"预约"),success:function(s){s.confirm&&e.bookingHelp(e.userList[t].memberId)}})},appointment:function(){var t=this;o.getStorageSync("wxUserInfo");if(this.courseId)if(this.courseId)if(this.activeDate){this.disabled=!0;var e=this.activeDate+" "+this.courses[this.currentIndex].text;n.default.postTrainerBooking({data:{bookingTime:e,courseId:this.courseId,courseName:this.courseName,coachId:this.memberId,coachName:this.memberName,shopId:this.shopId,shopName:this.shopName},method:"POST"}).then((function(e){t.disabled=!1,200==e.code&&(t.$u.toast("预约成功！"),setTimeout((function(){o.navigateBack()}),2e3))})).catch((function(e){t.disabled=!1}))}else this.$u.toast("请先选择预约日期！");else this.$u.toast("请先选择预约课程！");else this.$u.toast("请先选择预约课程！")},appointmentHelp:function(){this.courseId&&this.courseId?this.activeDate?this.showUserSelect=!0:this.$u.toast("请先选择预约日期！"):this.$u.toast("请先选择预约课程！")},bookingHelp:function(t){var e=this;this.disabled=!0;var s=this.activeDate+" "+this.courses[this.currentIndex].text;n.default.bookingHelp({data:{bookingTime:s,courseId:this.courseId,courseName:this.courseName,coachId:this.memberId,coachName:this.memberName,shopId:this.shopId,shopName:this.shopName,memberId:t},method:"POST"}).then((function(t){e.disabled=!1,200==t.code&&(e.$u.toast("预约成功！"),setTimeout((function(){o.navigateBack()}),2e3))})).catch((function(t){e.disabled=!1}))},changeActive:function(t){this.activeDate=t,this.currentPage=1,this.totalPages=1,console.log(this.activeDate),this.loadData()},changeCurrentIndex:function(t,e){1===e&&(this.currentIndex=t)},toBuyHuiYuanKa:function(){o.navigateTo({url:"/pages/huiYuanKa/index?id="+this.shopId})},loadData:function(){var t=this;o.showLoading({mask:!0,title:"数据加载中，请稍后……"}),n.default.getTrainerDetails({courseId:this.courseId,method:"GET"}).then((function(e){o.hideLoading(),200==e.code&&(t.detail=e.data,n.default.getCoachDetails({data:{memberId:t.memberId,shopId:t.shopId}}).then((function(e){200==e.code&&t.dataProcessing(e.data)})))})).catch((function(t){o.hideLoading()}))},dataProcessing:function(t){var e=t.workStartTime,s=t.workEndTime,o=new Date;o.getHours(),o.getMinutes();console.log(e),console.log(s);for(var n=0;n<this.courses.length;n++)"Y"==t.isReset?this.courses[n].status=4:e>this.courses[n].text?(console.log(777),this.courses[n].status=4):s<this.courses[n].text&&(this.courses[n].status=4)},handleOfficial:function(){this.showQrcode=!0},onOfficialClose:function(){this.showQrcode=!1,n.default.getInfo({data:{companyId:1},method:"GET"}).then((function(t){200==t.code&&o.setStorageSync("wxUserInfo",t.wxUser)}))}}}},64513:function(){}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(7307)}));t.O()}]);