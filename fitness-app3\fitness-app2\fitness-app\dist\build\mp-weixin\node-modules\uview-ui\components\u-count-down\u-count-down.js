(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-count-down/u-count-down"],{75014:function(t,i,e){"use strict";var n;e.r(i),e.d(i,{__esModule:function(){return s.__esModule},default:function(){return l}});var u,o=function(){var t=this,i=t.$createElement;t._self._c},a=[],s=e(94615),m=s["default"],r=e(75609),c=e.n(r),h=(c(),e(18535)),f=(0,h["default"])(m,o,a,!1,null,"3ff4d8a8",null,!1,n,u),l=f.exports},75609:function(){},94615:function(t,i,e){"use strict";var n=e(81715)["default"];Object.defineProperty(i,"__esModule",{value:!0}),i["default"]=void 0;var u=a(e(45828)),o=e(32099);function a(t){return t&&t.__esModule?t:{default:t}}i["default"]={name:"u-count-down",mixins:[n.$u.mpMixin,n.$u.mixin,u.default],data:function(){return{timer:null,timeData:(0,o.parseTimeData)(0),formattedTime:"0",runing:!1,endTime:0,remainTime:0}},watch:{time:function(t){this.reset()}},mounted:function(){this.init()},methods:{init:function(){this.reset()},start:function(){this.runing||(this.runing=!0,this.endTime=Date.now()+this.remainTime,this.toTick())},toTick:function(){this.millisecond?this.microTick():this.macroTick()},macroTick:function(){var t=this;this.clearTimeout(),this.timer=setTimeout((function(){var i=t.getRemainTime();(0,o.isSameSecond)(i,t.remainTime)&&0!==i||t.setRemainTime(i),0!==t.remainTime&&t.macroTick()}),30)},microTick:function(){var t=this;this.clearTimeout(),this.timer=setTimeout((function(){t.setRemainTime(t.getRemainTime()),0!==t.remainTime&&t.microTick()}),50)},getRemainTime:function(){return Math.max(this.endTime-Date.now(),0)},setRemainTime:function(t){this.remainTime=t;var i=(0,o.parseTimeData)(t);this.$emit("change",i),this.formattedTime=(0,o.parseFormat)(this.format,i),t<=0&&(this.pause(),this.$emit("finish"))},reset:function(){this.pause(),this.remainTime=this.time,this.setRemainTime(this.remainTime),this.autoStart&&this.start()},pause:function(){this.runing=!1,this.clearTimeout()},clearTimeout:function(t){function i(){return t.apply(this,arguments)}return i.toString=function(){return t.toString()},i}((function(){clearTimeout(this.timer),this.timer=null}))},beforeDestroy:function(){this.clearTimeout()}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-count-down/u-count-down-create-component"],{},function(t){t("81715")["createComponent"](t(75014))}]);