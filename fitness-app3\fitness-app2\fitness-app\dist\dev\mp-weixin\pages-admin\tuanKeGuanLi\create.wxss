/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages-admin/tuanKeGuanLi/create.vue?vue&type=style&index=0&id=5a448d44&scoped=true&lang=scss& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-5a448d44, .theme-color.u-content-color.data-v-5a448d44 {
  color: var(--base-color);
}
.theme-bg-color.data-v-5a448d44, .bgc.data-v-5a448d44 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-5a448d44, .nbc.data-v-5a448d44 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-5a448d44, .bc.data-v-5a448d44 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-5a448d44, .lbc.data-v-5a448d44 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-5a448d44, .btc.data-v-5a448d44 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-5a448d44, .ltc.data-v-5a448d44 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-5a448d44 {
  background: var(--scroll-item-bg-color);
}
.textareaTitle.data-v-5a448d44 {
  border-radius: 8rpx 8rpx 0 0;
  padding: 10rpx 30rpx;
  background-color: #e6e6e6;
}
.container.data-v-5a448d44  {
  min-height: 50vh;
}
.container.data-v-5a448d44  .u-form-item__body__right__message {
  text-align: end !important;
}
.upload-img-container.data-v-5a448d44 {
  display: flex;
  align-items: center;
}
.upload-img-box.data-v-5a448d44 {
  display: inline-flex;
  width: 180rpx;
  height: 180rpx;
  border: 1px solid #ddd;
  border-radius: 16rpx;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
  margin-right: 20rpx;
  vertical-align: top;
}
