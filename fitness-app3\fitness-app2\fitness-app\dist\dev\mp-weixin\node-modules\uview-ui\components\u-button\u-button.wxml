<button class="{{['u-button','u-reset-button','data-v-3bf2dba7',bemClass]}}" style="{{$root.s0}}" hover-start-time="{{$root.m0}}" hover-stay-time="{{$root.m1}}" form-type="{{formType}}" open-type="{{openType}}" app-parameter="{{appParameter}}" hover-stop-propagation="{{hoverStopPropagation}}" send-message-title="{{sendMessageTitle}}" send-message-path="{{sendMessagePath}}" lang="{{lang}}" data-name="{{dataName}}" session-from="{{sessionFrom}}" send-message-img="{{sendMessageImg}}" show-message-card="{{showMessageCard}}" hover-class="{{!disabled&&!loading?'u-button--active':''}}" data-event-opts="{{[['getphonenumber',[['getphonenumber',['$event']]]],['getuserinfo',[['getuserinfo',['$event']]]],['error',[['error',['$event']]]],['opensetting',[['opensetting',['$event']]]],['launchapp',[['launchapp',['$event']]]],['agreeprivacyauthorization',[['agreeprivacyauthorization',['$event']]]],['tap',[['clickHandler',['$event']]]]]}}" bindgetphonenumber="__e" bindgetuserinfo="__e" binderror="__e" bindopensetting="__e" bindlaunchapp="__e" bindagreeprivacyauthorization="__e" bindtap="__e"><block wx:if="{{loading}}"><u-loading-icon vue-id="113d4a8d-1" mode="{{loadingMode}}" size="{{loadingSize*1.15}}" color="{{loadingColor}}" class="data-v-3bf2dba7" bind:__l="__l"></u-loading-icon><text class="u-button__loading-text data-v-3bf2dba7" style="{{'font-size:'+(textSize+'px')+';'}}">{{loadingText||text}}</text></block><block wx:else><block wx:if="{{icon}}"><u-icon vue-id="113d4a8d-2" name="{{icon}}" color="{{iconColorCom}}" size="{{textSize*1.35}}" customStyle="{{({marginRight:'2px'})}}" class="data-v-3bf2dba7" bind:__l="__l"></u-icon></block><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><text class="u-button__text data-v-3bf2dba7" style="{{'font-size:'+(textSize+'px')+';'}}">{{text}}</text></block></block></button>