<view class="main data-v-4f27517f"><view data-event-opts="{{[['tap',[['showModal',['$event']]]]]}}" class="input data-v-4f27517f" bindtap="__e"><input style="{{(disabled?'color:#c0c4cc':'')}}" placeholder="{{placeholder}}" placeholder-style="color: rgba(102, 102, 102, 0.25);" placeholder-class="zqs-select-placeholder-class" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['','_value','$event',[]]]]]]}}" value="{{_value}}" bindinput="__e" class="data-v-4f27517f"/><block wx:if="{{showArrow&&!_value}}"><image class="selector-icon data-v-4f27517f" src="/static/right_icon.png"></image></block></view><view data-event-opts="{{[['tap',[['hideModal',['$event']]]]]}}" class="{{['select-modal','data-v-4f27517f',isShowModal?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="select-dialog data-v-4f27517f" style="{{'background-color:'+(bgColor)+';'}}" catchtap="__e"><view class="title-main data-v-4f27517f"><text class="title-detail data-v-4f27517f">{{title}}</text></view><block wx:if="{{showSearch}}"><view class="search-box data-v-4f27517f"><input class="search-input data-v-4f27517f" confirm-type="search" placeholder="输入内容进行模糊查询" placeholder-style="color:rgba(102, 102, 102, 0.25);" data-event-opts="{{[['input',[['__set_model',['','searchInput','$event',[]]]]]]}}" value="{{searchInput}}" bindinput="__e"/><block wx:if="{{showSearchBtn}}"><text data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-text data-v-4f27517f" bindtap="__e">搜索</text></block></view></block><view class="select-content data-v-4f27517f"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',index]]]]]]]}}" class="select-item data-v-4f27517f" style="{{(item.m0?'color:'+selectColor+';background-color:'+selectBgColor+';':'color:'+color+';')}}" bindtap="__e"><view class="title data-v-4f27517f">{{item.m1}}</view><block wx:if="{{item.m2}}"><text class="selectIcon icongou data-v-4f27517f"></text></block></view></block></view><view class="select-bar bg-white data-v-4f27517f"><button class="mini-btn action data-v-4f27517f" plain="true" type="default" size="default" data-event-opts="{{[['tap',[['empty',['$event']]]]]}}" bindtap="__e">{{''+emptyText+''}}</button><button class="mini-btn action data-v-4f27517f" type="primary" size="default" data-event-opts="{{[['tap',[['confirmClick',['$event']]]]]}}" bindtap="__e">{{''+confirmText+''}}</button></view></view></view></view>