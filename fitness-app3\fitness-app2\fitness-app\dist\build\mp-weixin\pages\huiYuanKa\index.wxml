<theme-wrap scoped-slots-compiler="augmented" vue-id="7d0354b1-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('7d0354b1-2')+','+('7d0354b1-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="会员卡" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="container"><view hidden="{{!(loading)}}"><block wx:for="{{$root.l1}}" wx:for-item="j" wx:for-index="idx" wx:key="idx"><view clas="u-p-t-40 u-p-b-40 u-font-36 font-bold"><view class="placeholder-w-1 u-m-b-40 u-m-t-40" style="height:50rpx;"></view><view class="border-16 bg-fff u-m-b-40 u-p-40 u-flex"><block wx:for="{{j.l0}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="u-p-r-10 u-p-l-10 u-flex-1"><view class="placeholder" style="aspect-ratio:3/2;"></view></view></block></view></view></block></view><view hidden="{{!($root.g0)}}" class="w-100"><block wx:for="{{$root.l3}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="w-100"><view class="u-p-t-40 u-font-36 font-bold">{{''+i.$orig.type_text+''}}</view><view class="u-m-t-40"><u-grid vue-id="{{('7d0354b1-3-'+index)+','+('7d0354b1-1')}}" border="{{false}}" col="2" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{i.l2}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><u-grid-item bind:click="__e" vue-id="{{('7d0354b1-4-'+index+'-'+idx)+','+('7d0354b1-3-'+index)}}" data-event-opts="{{[['^click',[['toHuiYuanKa',['$0'],[[['list','',index],['list','',idx,'memberCardId']]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="w-100 u-p-10"><view class="u-relative w-100 border-16 overflow-hidden" style="line-height:0;"><image class="w-100 border-16" style="height:200rpx;" src="{{item.f0}}" mode="widthFix"></image><view class="u-font-28 fc-fff u-p-20 w-100 font-bold u-absolute u-text-center expired-blk u-row-between"><view class="u-flex w-100 u-row-between u-col-end"><view class="u-font-30 fc-fff">{{item.$orig.validDays+' 天'}}</view><view><view class="u-font-28" style="text-decoration:line-through;">{{'￥'+item.m2+''}}</view><view class="ltc u-m-t-10">￥<text class="u-font-40 font-bold">{{item.$orig.cardPrice}}</text></view></view></view><block wx:if="{{item.$orig.validTimes>0}}"><view class="u-flex"><view class="u-font-30 ltc">{{item.$orig.validTimes+"次"}}</view></view></block></view></view></view></u-grid-item></block></u-grid></view></view></block></view><view hidden="{{!($root.g1)}}" class="u-flex-col u-row-center u-col-center w-100 u-p-t-80"><image style="width:360rpx;height:380rpx;" src="/static/images/empty/list.png" mode="widthFix"></image><view class="u-font-30 u-m-t-10 u-tips-color">暂无数据</view></view></view><button class="lbc reset-button share-wrap u-p-24" open-type="share" hover-class="navigator-hover"><u-icon vue-id="{{('7d0354b1-5')+','+('7d0354b1-1')}}" name="share" color="#fff" size="30" bind:__l="__l"></u-icon></button></view></theme-wrap>