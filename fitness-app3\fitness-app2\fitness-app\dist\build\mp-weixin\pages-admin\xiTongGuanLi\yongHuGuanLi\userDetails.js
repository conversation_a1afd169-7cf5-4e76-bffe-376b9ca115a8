(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages-admin/xiTongGuanLi/yongHuGuanLi/userDetails"],{1043:function(){},33024:function(e,n,o){"use strict";var t=o(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var u=i(o(68466));function i(e){return e&&e.__esModule?e:{default:e}}n["default"]={data:function(){return{loading:!0,btnLoading:!1,showBirthday:!1,gender:"男",form:{age:"",height:"",weight:"",bf:"",BMI:"",lbm:"",waistToHipRatio:"",chestGirth:"",waistCircumference:"",hipCircumference:"",smm:""},rules:[],btnStyle:{borderRadius:"16rpx",height:"94rpx",color:"#fff",fontSize:"32rpx",fontWeight:"bold"}}},onLoad:function(e){var n;e.list&&(this.form=JSON.parse(e.list)),console.log(this.form);var o=(null===(n=this.form)||void 0===n?void 0:n.sex)||0;this.gender=["男","女","未知"][o],this.getUserIndicator()},onReady:function(){console.log(this.$refs.uForm),this.$refs.uForm.setRules(this.rules)},methods:{getUserIndicator:function(){var e=this;u.default.getOtherUserIndicator({memberId:this.form.memberId}).then((function(n){console.log(n),200==n.code&&(e.form=n.data)}))},submit:function(){var e=this;t.showLoading({mask:!0}),this.btnLoading=!0,u.default.putOtherUserIndicator({data:this.form,method:"PUT"}).then((function(n){console.log(n),200==n.code?(t.hideLoading(),t.navigateBack()):(t.hideLoading(),e.btnLoading=!1,e.$refs.uNotify.show({message:"修改失败!",type:"error",duration:"1500"}))})).catch((function(n){t.hideLoading(),e.btnLoading=!1,e.$refs.uNotify.show({message:"修改失败!",type:"error",duration:"1500"})}))}}}},59031:function(e,n,o){"use strict";var t=o(51372)["default"],u=o(81715)["createPage"];o(96910);r(o(923));var i=r(o(60719));function r(e){return e&&e.__esModule?e:{default:e}}t.__webpack_require_UNI_MP_PLUGIN__=o,u(i.default)},60719:function(e,n,o){"use strict";o.r(n),o.d(n,{__esModule:function(){return a.__esModule},default:function(){return f}});var t,u={uNavbar:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(o.bind(o,66372))},uNoNetwork:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-no-network/u-no-network")]).then(o.bind(o,43763))},uToast:function(){return o.e("node-modules/uview-ui/components/u-toast/u-toast").then(o.bind(o,67559))},uNotify:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-notify/u-notify")]).then(o.bind(o,11380))},uForm:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-form/u-form")]).then(o.bind(o,9506))},uFormItem:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-form-item/u-form-item")]).then(o.bind(o,60088))},"u-Input":function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u--input/u--input")]).then(o.bind(o,59242))},uButton:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-button/u-button")]).then(o.bind(o,65610))}},i=function(){var e=this,n=e.$createElement,o=(e._self._c,e.$hasSSP("a51610c2-1")),t=o?{color:e.$getSSP("a51610c2-1","content")["navBarTextColor"]}:null,u=o?e.$getSSP("a51610c2-1","content"):null,i=o?e.$getSSP("a51610c2-1","content"):null,r=o?e.$getSSP("a51610c2-1","content"):null;e._isMounted||(e.e0=function(n){return e.uni.navigateBack()}),e.$mp.data=Object.assign({},{$root:{m0:o,a0:t,m1:u,m2:i,m3:r}})},r=[],a=o(33024),s=a["default"],d=o(1043),c=o.n(d),l=(c(),o(18535)),m=(0,l["default"])(s,i,r,!1,null,null,null,!1,u,t),f=m.exports}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],(function(){return n(59031)}));e.O()}]);