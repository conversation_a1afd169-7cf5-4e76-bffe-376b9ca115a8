{"version": 3, "file": "pages/huiYuanKa/index.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACnCA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MACAC,MAAA;MACAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,iBAAA,WAAAA,kBAAA;IAAA,IAAAC,mBAAA;IACA,IAAAC,MAAA,KAAAD,mBAAA,GAAAE,GAAA,CAAAC,cAAA,4BAAAH,mBAAA,uBAAAA,mBAAA,CAAAI,QAAA;IACA;MACAC,KAAA;MACAC,IAAA,sCAAAV,MAAA,iBAAAK,MAAA;MACAM,QAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAAC,GAAA;IACA,KAAAb,MAAA,GAAAa,GAAA,CAAAC,EAAA,IAAAR,GAAA,CAAAC,cAAA;IACA;IACA,KAAAN,OAAA,GAAAY,GAAA,CAAAZ,OAAA;IACA;IACA,KAAAC,OAAA,GAAAW,GAAA,CAAAX,OAAA;IACA,KAAAa,QAAA;EACA;EACAC,MAAA,WAAAA,OAAA;EACAC,OAAA;IACAF,QAAA,WAAAA,SAAA;MAAA,IAAAG,KAAA;MACAC,YAAA,CAAAC,eAAA;QACApB,MAAA,OAAAA,MAAA;QACAH,IAAA;UACAK,OAAA,OAAAA,OAAA;UACAmB,OAAA;UACAC,QAAA;QACA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA,CAAAI,IAAA;UACA,IAAAC,MAAA,GAAAL,GAAA,CAAAI,IAAA,CAAAE,MAAA,WAAAC,SAAA,EAAAC,IAAA,EAAAC,MAAA,EAAAC,KAAA;YACA;YACA;YACA;YACA;YACA,IAAAC,KAAA,GAAAjB,KAAA,CAAAkB,cAAA,CAAAJ,IAAA,CAAAK,QAAA;YACA,IAAAF,KAAA;YACA,IAAAJ,SAAA,CAAAI,KAAA;cACAJ,SAAA,CAAAI,KAAA,EAAAG,IAAA,CAAAN,IAAA;YACA;cACAD,SAAA,CAAAI,KAAA,KAAAH,IAAA;YACA;YACA,OAAAD,SAAA;UACA;UACAL,OAAA,CAAAC,GAAA,CAAAE,MAAA;UACA,IAAAU,IAAA;UACA,SAAAC,GAAA,IAAAX,MAAA;YACAU,IAAA,CAAAD,IAAA;cACAxC,IAAA,EAAA+B,MAAA,CAAAW,GAAA;cACAC,SAAA,EAAAD;YACA;UACA;UACAd,OAAA,CAAAC,GAAA,CAAAY,IAAA;UACArB,KAAA,CAAApB,IAAA,GAAAyC,IAAA;UACArB,KAAA,CAAAwB,SAAA;YACAxB,KAAA,CAAAnB,OAAA;UACA;QACA;MACA;IACA;IACA;IACA4C,QAAA,WAAAA,SAAAC,GAAA;MACA,IAAAA,GAAA;QACA;QACA,IAAAC,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,GAAA;QACA,IAAAC,GAAA,GAAAD,GAAA;UACA,OAAAA,GAAA;QACA;UACA,OAAAC,GAAA;QACA;MACA;QACA,OAAAD,GAAA;MACA;IACA;IACAR,cAAA,WAAAA,eAAAQ,GAAA;MACAlB,OAAA,CAAAC,GAAA,CAAAiB,GAAA;MACA,QAAAA,GAAA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MACA;IACA;IACAI,WAAA,WAAAA,YAAAlC,EAAA;MACAR,GAAA,CAAA2C,UAAA;QACAC,GAAA,gCAAAC,MAAA,CAAArC,EAAA,eAAAqC,MAAA,MAAAlD,OAAA;MACA;IACA;EACA;AACA;;;;;;;;;;ACzKA;;;;;;;;;;;;;;;ACAAR,mBAAA;AAGA,IAAA2D,IAAA,GAAA5D,sBAAA,CAAAC,mBAAA;AACA,IAAA4D,MAAA,GAAA7D,sBAAA,CAAAC,mBAAA;AAA8C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH9C;AACA4D,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLkG;AAClH;AACA,CAAyD;AACL;AACpD,CAAkE;;;AAGlE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBkf,CAAC,+DAAe,ydAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,i4BAAG,EAAC", "sources": ["webpack:///./src/pages/huiYuanKa/index.vue?43b7", "uni-app:///src/pages/huiYuanKa/index.vue", "webpack:///./src/pages/huiYuanKa/index.vue?9268", "uni-app:///src/main.js", "webpack:///./src/pages/huiYuanKa/index.vue?ae82", "webpack:///./src/pages/huiYuanKa/index.vue?ee45", "webpack:///./src/pages/huiYuanKa/index.vue?b4c6", "webpack:///./src/pages/huiYuanKa/index.vue?3045"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uGrid: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-grid/u-grid\" */ \"uview-ui/components/u-grid/u-grid.vue\"\n      )\n    },\n    uGridItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-grid-item/u-grid-item\" */ \"uview-ui/components/u-grid-item/u-grid-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"7d0354b1-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"7d0354b1-1\", \"content\")[\"buttonTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"7d0354b1-1\", \"content\") : null\n  var l1 = m0\n    ? _vm.__map(Array(3), function (j, idx) {\n        var $orig = _vm.__get_orig(j)\n        var l0 = Array(3)\n        return {\n          $orig: $orig,\n          l0: l0,\n        }\n      })\n    : null\n  var g0 = m0 ? !_vm.loading && _vm.list.length : null\n  var l3 = m0\n    ? _vm.__map(_vm.list, function (i, index) {\n        var $orig = _vm.__get_orig(i)\n        var l2 = _vm.__map(i.list, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var f0 = _vm._f(\"Img\")(item.cover)\n          var m2 = _vm.rawPrice(item.cardPrice)\n          return {\n            $orig: $orig,\n            f0: f0,\n            m2: m2,\n          }\n        })\n        return {\n          $orig: $orig,\n          l2: l2,\n        }\n      })\n    : null\n  var g1 = m0 ? !_vm.loading && !_vm.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        l1: l1,\n        g0: g0,\n        l3: l3,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <themeWrap>\n    <template #content=\"{ navBarColor, buttonTextColor }\">\n      <u-navbar :titleStyle=\"{ color: buttonTextColor }\" :bgColor=\"navBarColor\" :placeholder=\"true\" title=\"会员卡\"\n        :autoBack=\"true\" :border=\"false\" :safeAreaInsetTop=\"true\">\n      </u-navbar>\n      <view class=\"container\">\n        <view v-show=\"loading\">\n          <view v-for=\"(j, idx) in Array(3)\" :key=\"idx\" clas=\"u-p-t-40 u-p-b-40 u-font-36 font-bold\">\n            <view class=\"placeholder-w-1 u-m-b-40 u-m-t-40\" style=\"height: 50rpx\"></view>\n            <view class=\"border-16 bg-fff u-m-b-40 u-p-40 u-flex\">\n              <view class=\"u-p-r-10 u-p-l-10 u-flex-1\" v-for=\"(i, index) in Array(3)\" :key=\"index\">\n                <view class=\"placeholder\" style=\"aspect-ratio: 3/2\"></view>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view v-show=\"!loading && list.length\" class=\"w-100\">\n          <view class=\"w-100\" v-for=\"(i, index) in list\" :key=\"index\">\n            <view class=\"u-p-t-40 u-font-36 font-bold\"> {{ i.type_text }} </view>\n            <view class=\"u-m-t-40\">\n              <u-grid :border=\"false\" col=\"2\">\n                <u-grid-item @click=\"toHuiYuanKa(item.memberCardId)\" v-for=\"(item, idx) in i.list\" :key=\"idx\">\n                  <view class=\"w-100 u-p-10\">\n                    <view class=\"u-relative w-100 border-16 overflow-hidden\" style=\"line-height: 0\">\n                      <image :src=\"item.cover | Img\" mode=\"widthFix\" class=\"w-100 border-16\" style=\"height: 200rpx\" />\n                      <view\n                        class=\"u-font-28 fc-fff u-p-20 w-100 font-bold u-absolute u-text-center expired-blk u-row-between\">\n                        <view class=\"u-flex w-100 u-row-between u-col-end\">\n                          <view class=\"u-font-30 fc-fff\">{{ item.validDays }} 天\n                          </view>\n                          <view>\n                            <view class=\"u-font-28\" style=\"text-decoration: line-through\">\n                              ￥{{ rawPrice(item.cardPrice) }}\n                            </view>\n                            <view class=\"ltc u-m-t-10\">\n                              ￥\n                              <text class=\"u-font-40 font-bold\">{{ item.cardPrice }}</text>\n                            </view>\n                          </view>\n                        </view>\n                        <view class=\"u-flex\" v-if=\"item.validTimes > 0\">\n                          <view class=\"u-font-30 ltc\">{{ item.validTimes }}次</view>\n                        </view>\n                      </view>\n                    </view>\n                  </view>\n                </u-grid-item>\n              </u-grid>\n            </view>\n          </view>\n        </view>\n        <view v-show=\"!loading && !list.length\" class=\"u-flex-col u-row-center u-col-center w-100 u-p-t-80\">\n          <image src=\"@/static/images/empty/list.png\" mode=\"widthFix\" style=\"width: 360rpx; height: 380rpx\" />\n          <view class=\"u-font-30 u-m-t-10 u-tips-color\">暂无数据</view>\n        </view>\n      </view>\n      <button open-type=\"share\" class=\"lbc reset-button share-wrap u-p-24\" hover-class=\"navigator-hover\">\n        <u-icon name=\"share\" color=\"#fff\" size=\"30\"></u-icon>\n      </button>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\n  import api from \"@/common/api\";\n  export default {\n    data() {\n      return {\n        list: [],\n        loading: true,\n        shopId: '',\n        salerId: '',\n        coachId: '',\n      };\n    },\n    onShareAppMessage() {\n      const userId = uni.getStorageSync('wxUserInfo')?.memberId || ''\n      return {\n        title: '会员卡',\n        path: '/pages/huiYuanKa/index?id=' + this.shopId + '&salerId=' + userId,\n        imageUrl: '/static/images/share/share.jpg'\n      }\n    },\n    onLoad(obj) {\n      this.shopId = obj.id || uni.getStorageSync(\"nowShopId\");\n      // 分销人员id\n      this.salerId = obj.salerId || '';\n      // 教练id\n      this.coachId = obj.coachId || '';\n      this.loadData()\n    },\n    onShow() {},\n    methods: {\n      loadData() {\n        api.getUserCardList({\n          shopId: this.shopId,\n          data: {\n            coachId: this.coachId,\n            pageNum: 1,\n            pageSize: 999\n          }\n        }).then((res) => {\n          if (res.code == 200) {\n            console.log(res.rows);\n            let result = res.rows.reduce((initArray, item, xindex, array) => {\n              //initArray 初始值 或者是上一次调用返回的值\n              //item 数组中当前被处理的元素\n              //xindex 当前元素的下标\n              //array 被reduce的数组  即上面代码中的数组a\n              let index = this.returnCardName(item.cardType);\n              if (index == '1') {}\n              if (initArray[index]) {\n                initArray[index].push(item)\n              } else {\n                initArray[index] = [item]\n              }\n              return initArray\n            }, []);\n            console.log(result)\n            let temp = []\n            for (let key in result) {\n              temp.push({\n                list: result[key],\n                type_text: key\n              })\n            }\n            console.log(temp);\n            this.list = temp\n            this.$nextTick(()=>{\n              this.loading = false\n            })\n          }\n        })\n      },\n      // 计算原价\n      rawPrice(val) {\n        if (val > 0) {\n          // 打八折\n          let num = Math.floor(val / 0.8);\n          if (num < val) {\n            return val\n          } else {\n            return num\n          }\n        } else {\n          return val\n        }\n      },\n      returnCardName(val) {\n        console.log(val);\n        switch (val){\n          case '1':\n            return '会员卡'\n            break;\n          case '2':\n            return '私教课'\n            break;\n          default:\n            return '其他类型'\n            break;\n        }\n      },\n      toHuiYuanKa(id) {\n        uni.navigateTo({\n          url: `/pages/huiYuanKa/detail?id=${id}&salerId=${this.salerId}`,\n        });\n      },\n    },\n  };\n</script>\n<style lang=\"scss\">\n  .expired-blk {\n    top: 0;\n    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent 50%, rgba(0, 0, 0, 0.6) 100%);\n    height: 100%;\n    border-radius: 0 0 16rpx 16rpx;\n    line-height: 100%;\n    display: flex;\n    flex-direction: column-reverse;\n  }\n  .share-wrap{\n    position: fixed;\n    right: 30rpx;\n    bottom: 60rpx;\n    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);\n    border-radius: 50%;\n  }\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/huiYuanKa/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1cce8af5&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/huiYuanKa/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1cce8af5&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "data", "list", "loading", "shopId", "salerId", "coachId", "onShareAppMessage", "_uni$getStorageSync", "userId", "uni", "getStorageSync", "memberId", "title", "path", "imageUrl", "onLoad", "obj", "id", "loadData", "onShow", "methods", "_this", "api", "getUserCardList", "pageNum", "pageSize", "then", "res", "code", "console", "log", "rows", "result", "reduce", "initArray", "item", "xindex", "array", "index", "returnCardName", "cardType", "push", "temp", "key", "type_text", "$nextTick", "rawPrice", "val", "num", "Math", "floor", "toHuiYuanKa", "navigateTo", "url", "concat", "_vue", "_index", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}