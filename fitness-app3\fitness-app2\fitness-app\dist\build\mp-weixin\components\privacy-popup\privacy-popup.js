(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["components/privacy-popup/privacy-popup"],{8287:function(t,n,o){"use strict";var e=o(51372)["default"],i=o(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;n["default"]={name:"PrivacyPopup",data:function(){return{privacyContractName:"",showPrivacy:!0,isRead:!1,resolvePrivacyAuthorization:null}},props:{position:{type:String,default:"center"}},computed:{privacyClass:function(){return"bottom"===this.position?"privacy privacy-bottom":"privacy"},contentClass:function(){return"bottom"===this.position?"content content-bottom":"content"}},mounted:function(){},methods:{openPrivacyContract:function(){var t=this;e.openPrivacyContract({success:function(){t.isRead=!0},fail:function(){i.showToast({title:"遇到错误",icon:"error"})}})},exitMiniProgram:function(){e.exitMiniProgram()},handleAgreePrivacyAuthorization:function(){this.showPrivacy=!1,"function"===typeof this.resolvePrivacyAuthorization&&this.resolvePrivacyAuthorization({buttonId:"agree-btn",event:"agree"})}}}},57596:function(t,n,o){"use strict";var e;o.r(n),o.d(n,{__esModule:function(){return r.__esModule},default:function(){return v}});var i,a=function(){var t=this,n=t.$createElement;t._self._c},c=[],r=o(8287),u=r["default"],s=o(81785),p=o.n(s),l=(p(),o(18535)),f=(0,l["default"])(u,a,c,!1,null,"11ddd6f5",null,!1,e,i),v=f.exports},81785:function(){}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["components/privacy-popup/privacy-popup-create-component"],{},function(t){t("81715")["createComponent"](t(57596))}]);