<theme-wrap scoped-slots-compiler="augmented" vue-id="3290c38c-1" class="data-v-64ba54ba" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><view class="data-v-64ba54ba"><u-navbar vue-id="{{('3290c38c-2')+','+('3290c38c-1')}}" title="我的团课" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-64ba54ba" bind:__l="__l"></u-navbar></view><view class="container u-p-t-40 bottom-placeholder data-v-64ba54ba"><block wx:if="{{$root.g0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-p-r-40 u-p-l-40 u-p-t-30 u-p-b-30 bg-fff u-m-b-20 u-m-t-20 border-16 w-100 u-flex u-row-between data-v-64ba54ba"><view class="u-flex u-col-center u-row-start data-v-64ba54ba" style="flex-wrap:no-wrap;overflow:hidden;"><view class="overflow-hidden flex-0 border-16 data-v-64ba54ba" style="width:140rpx;height:140rpx;line-height:0;"><image class="h-100 data-v-64ba54ba" src="{{item.$orig.banner}}" mode="heightFix"></image></view><view class="w-100 u-p-l-20 data-v-64ba54ba"><view class="u-line-1 w-100 data-v-64ba54ba">{{''+item.$orig.title+''}}</view><view class="u-flex u-tips-color u-font-26 u-p-t-10 u-p-b-10 text-no-wrap data-v-64ba54ba"><view class="u-p-r-20 data-v-64ba54ba">{{"总人数："+(item.$orig.attendance||0)}}</view><view class="data-v-64ba54ba">{{"最少开课数："+(item.$orig.minAttendance||0)}}</view></view><view class="u-tips-color u-font-26 data-v-64ba54ba">{{'开课时间：'+(item.$orig.classTime||'')+''}}</view><view class="u-tips-color u-font-26 data-v-64ba54ba">{{'教练名称：'+(item.$orig.coachName||'')+''}}</view></view></view><view class="btn-wrap data-v-64ba54ba"><block wx:if="{{type!='add'}}"><navigator class="u-p-t-10 u-p-b-10 u-p-r-18 u-p-l-18 border-8 btc text-no-wrap lbc u-font-26 font-bold data-v-64ba54ba" url="{{'/pages-admin/tuanKeGuanLi/edit?list='+item.g1}}">编辑</navigator></block><block wx:if="{{type!='add'}}"><navigator class="u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10 data-v-64ba54ba" style="border:1px solid;border-color:buttonLightBgColor;" url="{{'/pages-admin/tuanKeGuanLi/admin-list?list='+item.g2}}">查看</navigator></block><block wx:if="{{type=='add'}}"><navigator class="u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10 data-v-64ba54ba" style="border:1px solid;border-color:buttonLightBgColor;" url="{{'/pages-admin/tuanKeGuanLi/create?list='+item.g3+'&type=add'}}">添加</navigator></block><block wx:if="{{type=='week'}}"><view data-event-opts="{{[['tap',[['del',['$0'],[[['list','',index]]]]]]]}}" class="u-p-t-10 u-p-b-10 ltc u-p-r-18 u-p-l-18 border-8 text-no-wrap u-font-26 font-bold u-m-t-10 data-v-64ba54ba" style="border:1px solid;border-color:buttonLightBgColor;" bindtap="__e">删除</view></block></view></view></block></block><block wx:else><view class="w-100 u-p-t-80 u-flex-col u-row-center u-col-center data-v-64ba54ba"><image style="width:360rpx;height:360rpx;" src="/static/images/empty/order.png" mode="width" class="data-v-64ba54ba"></image><view class="u-p-t-10 u-font-30 u-tips-color data-v-64ba54ba">暂无课程</view></view></block></view><view class="bottom-blk bg-fff w-100 u-p-40 data-v-64ba54ba"><block wx:if="{{type!='add'}}"><u-button vue-id="{{('3290c38c-3')+','+('3290c38c-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['toAddCourse']]]]}}" bind:click="__e" class="data-v-64ba54ba" bind:__l="__l" vue-slots="{{['default']}}">添加团课</u-button></block></view></view></theme-wrap>