(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-badge/u-badge"],{13519:function(e,t,u){"use strict";var o;u.r(t),u.d(t,{__esModule:function(){return a.__esModule},default:function(){return f}});var s,i=function(){var e=this,t=e.$createElement,u=(e._self._c,e.show&&(0!==Number(e.value)||e.showZero||e.isDot)),o=u?e.__get_style([e.$u.addStyle(e.customStyle),e.badgeStyle]):null;e.$mp.data=Object.assign({},{$root:{m0:u,s0:o}})},n=[],a=u(22393),l=a["default"],r=u(48013),c=u.n(r),d=(c(),u(18535)),h=(0,d["default"])(l,i,n,!1,null,"231d324e",null,!1,o,s),f=h.exports},22393:function(e,t,u){"use strict";var o=u(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var s=i(u(78733));function i(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-badge",mixins:[o.$u.mpMixin,s.default,o.$u.mixin],computed:{boxStyle:function(){var e={};return e},badgeStyle:function(){var e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){var t=this.offset[0],u=this.offset[1]||t;e.top=o.$u.addUnit(t),e.right=o.$u.addUnit(u)}return e},showValue:function(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}}}},48013:function(){}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-badge/u-badge-create-component"],{},function(e){e("81715")["createComponent"](e(13519))}]);