(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-overlay/u-overlay"],{3507:function(){},15550:function(e,n,t){"use strict";t.r(n),t.d(n,{__esModule:function(){return a.__esModule},default:function(){return m}});var o,u={uTransition:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-transition/u-transition")]).then(t.bind(t,68270))}},i=function(){var e=this,n=e.$createElement;e._self._c},l=[],a=t(22213),c=a["default"],r=t(3507),s=t.n(r),d=(s(),t(18535)),f=(0,d["default"])(c,i,l,!1,null,"70152aa1",null,!1,u,o),m=f.exports},22213:function(e,n,t){"use strict";var o=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var u=i(t(28898));function i(e){return e&&e.__esModule?e:{default:e}}n["default"]={name:"u-overlay",mixins:[o.$u.mpMixin,o.$u.mixin,u.default],computed:{overlayStyle:function(){var e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":"rgba(0, 0, 0, ".concat(this.opacity,")")};return o.$u.deepMerge(e,o.$u.addStyle(this.customStyle))}},methods:{clickHandler:function(){this.$emit("click")}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-overlay/u-overlay-create-component"],{},function(e){e("81715")["createComponent"](e(15550))}]);