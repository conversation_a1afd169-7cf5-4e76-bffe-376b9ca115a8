(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-parse/node/node"],{1433:function(t,e,i){"use strict";var o=i(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=function(){Promise.resolve().then(function(){return resolve(i(98216))}.bind(null,i))["catch"](i.oe)};e["default"]={name:"node",options:{virtualHost:!0},data:function(){return{ctrl:{}}},props:{name:String,attrs:{type:Object,default:function(){return{}}},childs:Array,opts:Array},components:{node:n},mounted:function(){for(this.root=this.$parent;"mp-html"!=this.root.$options.name;this.root=this.root.$parent);},beforeDestroy:function(){},methods:{toJSON:function(){},play:function(t){if(this.root.pauseVideo){for(var e=!1,i=t.target.id,n=this.root._videos.length;n--;)this.root._videos[n].id==i?e=!0:this.root._videos[n].pause();if(!e){var r=o.createVideoContext(i,this);r.id=i,this.root._videos.push(r)}}},imgTap:function(t){var e=this.childs[t.currentTarget.dataset.i];if(e.a)return this.linkTap(e.a);e.attrs.ignore||(this.root.$emit("imgTap",e.attrs),this.root.previewImg&&o.previewImage({current:parseInt(e.attrs.i),urls:this.root.imgList}))},imgLongTap:function(t){},imgLoad:function(t){var e=t.currentTarget.dataset.i;this.childs[e].w?(this.opts[1]&&!this.ctrl[e]||-1==this.ctrl[e])&&this.$set(this.ctrl,e,1):this.$set(this.ctrl,e,t.detail.width)},linkTap:function(t){var e=t.currentTarget?this.childs[t.currentTarget.dataset.i].attrs:t,i=e.href;this.root.$emit("linkTap",e),i&&("#"==i[0]?this.root.navigateTo(i.substring(1)).catch((function(){})):i.includes("://")?this.root.copyLink&&o.setClipboardData({data:i,success:function(){return o.showToast({title:"链接已复制"})}}):o.navigateTo({url:i,fail:function(){o.switchTab({url:i,fail:function(){}})}}))},mediaError:function(t){var e=t.currentTarget.dataset.i,i=this.childs[e];if("video"==i.name||"audio"==i.name){var o=(this.ctrl[e]||0)+1;if(o>i.src.length&&(o=0),o<i.src.length)return this.$set(this.ctrl,e,o)}else"img"==i.name&&this.opts[2]&&this.$set(this.ctrl,e,-1);this.root&&this.root.$emit("error",{source:i.name,attrs:i.attrs,errMsg:t.detail.errMsg})}}}},93345:function(){},98216:function(t,e,i){"use strict";var o;i.r(e),i.d(e,{__esModule:function(){return s.__esModule},default:function(){return m}});var n=function(){var t=this,e=t.$createElement;t._self._c},r=[],s=i(1433),a=s["default"],u=i(93345),c=i.n(u),l=(c(),i(18535));function h(t){t.options.wxsCallMethods||(t.options.wxsCallMethods=[])}var d,f=h,p=(0,l["default"])(a,n,r,!1,null,null,null,!1,o,d);"function"===typeof f&&f(p);var m=p.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-parse/node/node-create-component"],{},function(t){t("81715")["createComponent"](t(98216))}]);