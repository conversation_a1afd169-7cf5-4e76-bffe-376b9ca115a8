<theme-wrap scoped-slots-compiler="augmented" vue-id="1be05e2d-1" class="data-v-eb1b3e82" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-eb1b3e82" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('1be05e2d-2')+','+('1be05e2d-1')}}" title="场馆角色管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-eb1b3e82" bind:__l="__l"></u-navbar><block wx:if="{{$root.g0>0}}"><view class="container u-p-t-40 u-p-b-40 data-v-eb1b3e82"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 data-v-eb1b3e82" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view><view class="u-p-t-20 u-p-b-20 u-p-r-40 u-p-l-40 w-100 border-16 u-m-b-20 bg-fff data-v-eb1b3e82"><u-cell-group vue-id="{{('1be05e2d-3')+','+('1be05e2d-1')}}" border="{{false}}" class="data-v-eb1b3e82" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{roleList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('1be05e2d-4-'+index)+','+('1be05e2d-3')}}" title="{{list.roleName}}" value="" label=" " center="{{true}}" isLink="{{true}}" data-event-opts="{{[['^click',[['setValue']]]]}}" bind:click="__e" class="data-v-eb1b3e82" bind:__l="__l"></u-cell></block></u-cell-group></view></view></block><block wx:else><u-empty vue-id="{{('1be05e2d-5')+','+('1be05e2d-1')}}" marginTop="150" mode="data" class="data-v-eb1b3e82" bind:__l="__l"></u-empty></block><u-picker vue-id="{{('1be05e2d-6')+','+('1be05e2d-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-eb1b3e82" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-eb1b3e82"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-eb1b3e82" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增角色</view></view></view></theme-wrap>