<theme-wrap scoped-slots-compiler="augmented" vue-id="d531f4f0-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('d531f4f0-2')+','+('d531f4f0-1')}}" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" placeholder="{{true}}" title="会员卡" autoBack="{{true}}" border="{{false}}" safeAreaInsetTop="{{true}}" bind:__l="__l"></u-navbar><view class="container u-p-t-40 bottom-placeholder"><view class="huiyuan-item w-100 u-flex u-row-start u-col-start"><view class="u-flex-9 u-relative"><view class="u-relative overflow-hidden" style="line-height:0;"><image class="w-100 border-16" style="height:280rpx;" src="{{$root.f0}}" mode="widthFix"></image></view></view><view class="u-flex-8 u-flex-col u-p-l-30"><view class="u-font-38 font-bold u-m-b-10">{{''+detail.cardName+''}}</view><view class="u-font-26 u-m-b-10 u-tips-color w-100">{{'有效期：'+(detail.validDays<=0?'无限期':detail.validDays+'天')+''}}</view><view class="u-font-26 u-tips-color" style="text-decoration:line-through;">原价：￥<text class="u-m-l-10 u-m-r-10">{{$root.m2+''}}</text>元</view><view><text class="ltc u-font-26">价格：￥<text class="u-font-40 u-m-r-10 font-bold">{{detail.cardPrice}}</text>元</text></view></view></view><block wx:if="{{detail.is_xuFei}}"><view class="u-p-40 bg-fff u-m-t-40 u-m-b-40 border-16"><view class="font-bold u-m-b-30 u-font-36">续费优享</view><view class="u-font-28 u-flex u-row-between w-100"><view>本卡支持连续包月</view><view class="ltc">￥<text class="font-bold u-font-40 u-m-r-10 u-m-l-10">{{detail.cardPrice}}</text>元/月</view></view><view class="u-m-t-10 u-flex u-font-24 u-tips-color u-col-start"><u-icon vue-id="{{('d531f4f0-3')+','+('d531f4f0-1')}}" name="error-circle" size="14" bind:__l="__l"></u-icon><text class="u-m-l-10">注：每月到期自动续费，可在“我的”页面中的会员卡取消续费。</text></view></view></block><view class="u-p-40 bg-fff u-m-t-40 u-m-b-40 border-16"><view class="font-bold u-m-b-30 u-font-36">简介</view><view class="u-font-28">{{''+(detail.description||"")+''}}</view></view><view class="u-p-40 bg-fff u-m-t-40 u-m-b-40 border-16"><view class="font-bold u-m-b-30 u-font-36">须知</view><block wx:if="{{tips}}"><u-parse class="content-text" vue-id="{{('d531f4f0-4')+','+('d531f4f0-1')}}" content="{{tips}}" selectable="{{true}}" tagStyle="{{parseTagStyle}}" bind:__l="__l"></u-parse></block></view></view><view class="bottom-blk bg-fff w-100 u-p-40"><view class="u-p-b-20 font-bold u-flex"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['u-flex','u-row-start','u-col-start',(shake)?'shake':'']}}" bindtap="__e"><view class="check-box-wrap u-m-r-20 overflow-hidden border-8" style="{{'background-color:'+(checkboxValue1?$root.m3['buttonLightBgColor']:'#fff')+';'+('border-color:'+($root.m4['buttonLightBgColor'])+';')}}"><u-icon vue-id="{{('d531f4f0-5')+','+('d531f4f0-1')}}" name="checkbox-mark" color="#fff" size="16" bind:__l="__l"></u-icon></view><view class="check-box-content u-font-28">请先查看并同意<text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-m-l-10 ltc" catchtap="__e">合同条款</text></view></view></view><u-button vue-id="{{('d531f4f0-6')+','+('d531f4f0-1')}}" color="{{$root.m5['buttonLightBgColor']}}" loading="{{disabled}}" loadingText="支付中..." shape="circle" customStyle="{{({fontWeight:'bold',fontSize:'36rpx'})}}" data-event-opts="{{[['^click',[['pay']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">立即支付</u-button></view><u-popup vue-id="{{('d531f4f0-7')+','+('d531f4f0-1')}}" show="{{showContractModal}}" mode="center" safeAreaInsetBottom="{{false}}" round="16" data-event-opts="{{[['^close',[['e2']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-p-40" style="height:75vh;width:85vw;overflow:scroll;"><u-parse class="content-text" vue-id="{{('d531f4f0-8')+','+('d531f4f0-7')}}" content="{{contract}}" selectable="{{true}}" tagStyle="{{parseTagStyle}}" bind:__l="__l"></u-parse></view></u-popup><button class="lbc reset-button share-wrap u-p-24" open-type="share" hover-class="navigator-hover"><u-icon vue-id="{{('d531f4f0-9')+','+('d531f4f0-1')}}" name="share" color="#fff" size="30" bind:__l="__l"></u-icon></button></view></theme-wrap>