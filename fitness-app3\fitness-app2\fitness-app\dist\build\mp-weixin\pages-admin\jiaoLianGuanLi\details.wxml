<theme-wrap scoped-slots-compiler="augmented" vue-id="7c8f4ba6-1" class="data-v-23549a14" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-23549a14" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('7c8f4ba6-2')+','+('7c8f4ba6-1')}}" title="修改教练管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-23549a14" bind:__l="__l"></u-navbar><view class="formView data-v-23549a14"><view class="formTextarea border-8 data-v-23549a14"><view class="textareaTitle u-flex data-v-23549a14"><view class="u-flex-1 data-v-23549a14">教练背景:</view><u-icon vue-id="{{('7c8f4ba6-3')+','+('7c8f4ba6-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseImage',[1]]]]]}}" bind:click="__e" class="data-v-23549a14" bind:__l="__l"></u-icon></view><view class="formLogo u-flex data-v-23549a14"><view class="imgView data-v-23549a14"><u--image vue-id="{{('7c8f4ba6-4')+','+('7c8f4ba6-1')}}" showLoading="{{true}}" src="{{$root.f0}}" width="200rpx" height="120rpx" radius="4" class="data-v-23549a14" bind:__l="__l"></u--image></view></view></view><view class="formTextarea border-8 data-v-23549a14"><view class="textareaTitle u-flex data-v-23549a14"><view class="u-flex-1 data-v-23549a14">教练头像:</view><u-icon vue-id="{{('7c8f4ba6-5')+','+('7c8f4ba6-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseImage',[2]]]]]}}" bind:click="__e" class="data-v-23549a14" bind:__l="__l"></u-icon></view><view class="formLogo u-flex data-v-23549a14"><view class="imgView data-v-23549a14"><u--image vue-id="{{('7c8f4ba6-6')+','+('7c8f4ba6-1')}}" showLoading="{{true}}" src="{{$root.f1}}" width="200rpx" height="120rpx" radius="4" class="data-v-23549a14" bind:__l="__l"></u--image></view></view></view><view class="formTextarea border-8 data-v-23549a14"><view class="textareaTitle u-flex data-v-23549a14"><view class="u-flex-1 data-v-23549a14">教练照片:</view><u-icon vue-id="{{('7c8f4ba6-7')+','+('7c8f4ba6-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseImage',[3]]]]]}}" bind:click="__e" class="data-v-23549a14" bind:__l="__l"></u-icon></view><view class="formLogo u-flex data-v-23549a14"><view class="imgView data-v-23549a14"><u--image vue-id="{{('7c8f4ba6-8')+','+('7c8f4ba6-1')}}" showLoading="{{true}}" src="{{$root.f2}}" width="200rpx" height="120rpx" radius="4" class="data-v-23549a14" bind:__l="__l"></u--image></view></view></view><view class="formTextarea border-8 data-v-23549a14"><view class="textareaTitle u-flex data-v-23549a14"><view class="u-flex-1 data-v-23549a14">教练资质:</view><u-icon vue-id="{{('7c8f4ba6-9')+','+('7c8f4ba6-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseZizhi']]]]}}" bind:click="__e" class="data-v-23549a14" bind:__l="__l"></u-icon></view><view class="formLogo u-flex data-v-23549a14"><block wx:for="{{$root.l0}}" wx:for-item="list" wx:for-index="idx" wx:key="*this"><view class="imgView data-v-23549a14"><u--image vue-id="{{('7c8f4ba6-10-'+idx)+','+('7c8f4ba6-1')}}" showLoading="{{true}}" src="{{list.f3}}" width="200rpx" height="120rpx" radius="4" class="data-v-23549a14" bind:__l="__l"></u--image></view></block></view></view><view class="formList data-v-23549a14"><view class="data-v-23549a14">上班时间:</view><u-picker vue-id="{{('7c8f4ba6-11')+','+('7c8f4ba6-1')}}" show="{{showStart}}" columns="{{courses}}" keyName="text" data-event-opts="{{[['^confirm',[['changeWorkStart']]]]}}" bind:confirm="__e" class="data-v-23549a14" bind:__l="__l"></u-picker><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-flex-1 data-v-23549a14" bindtap="__e"><u--input bind:input="__e" vue-id="{{('7c8f4ba6-12')+','+('7c8f4ba6-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.workStartTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','workStartTime','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l"></u--input></view></view><view class="formList data-v-23549a14"><view class="data-v-23549a14">下班时间:</view><u-picker vue-id="{{('7c8f4ba6-13')+','+('7c8f4ba6-1')}}" show="{{showEnd}}" columns="{{courses}}" keyName="text" data-event-opts="{{[['^confirm',[['changeWorkEnd']]]]}}" bind:confirm="__e" class="data-v-23549a14" bind:__l="__l"></u-picker><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="u-flex-1 data-v-23549a14" bindtap="__e"><u--input bind:input="__e" vue-id="{{('7c8f4ba6-14')+','+('7c8f4ba6-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.workEndTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','workEndTime','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l"></u--input></view></view><view class="formList data-v-23549a14"><view class="data-v-23549a14">教练类型:</view><u-picker vue-id="{{('7c8f4ba6-15')+','+('7c8f4ba6-1')}}" show="{{showType}}" columns="{{coachType}}" keyName="coachTypeName" data-event-opts="{{[['^confirm',[['changeCoachType']]]]}}" bind:confirm="__e" class="data-v-23549a14" bind:__l="__l"></u-picker><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="u-flex-1 data-v-23549a14" bindtap="__e"><u--input bind:input="__e" vue-id="{{('7c8f4ba6-16')+','+('7c8f4ba6-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.coachTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','coachTypeName','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l"></u--input></view></view><view class="formList data-v-23549a14"><view class="data-v-23549a14">荣誉:</view><u--input bind:input="__e" vue-id="{{('7c8f4ba6-17')+','+('7c8f4ba6-1')}}" border="{{false}}" value="{{formList.honor}}" data-event-opts="{{[['^input',[['__set_model',['$0','honor','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l"></u--input></view><view class="formTextarea border-8 data-v-23549a14"><view class="textareaTitle u-flex data-v-23549a14"><view class="u-flex-1 data-v-23549a14">格言:</view></view><view class="formLogo data-v-23549a14"><u--textarea bind:input="__e" vue-id="{{('7c8f4ba6-18')+','+('7c8f4ba6-1')}}" placeholder="请输入格言" count="{{true}}" maxlength="{{250}}" value="{{formList.aphorism}}" data-event-opts="{{[['^input',[['__set_model',['$0','aphorism','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l"></u--textarea></view></view><view class="formTextarea border-8 data-v-23549a14"><view class="textareaTitle u-flex data-v-23549a14"><view class="u-flex-1 data-v-23549a14">教练特色:</view></view><view class="formLogo u-flex data-v-23549a14"><view class="u-flex u-m-t-20 u-m-b-40 data-v-23549a14" style="flex-wrap:wrap;"><block wx:for="{{tagList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['clickTag',['$0'],[[['tagList','',index]]]]]]]}}" class="{{['my-tag','data-v-23549a14',(list.checked==true)?'activeTag':'']}}" style="margin-left:20rpx;" bindtap="__e">{{list.dictValue}}</view></block></view></view></view><view class="formList data-v-23549a14"><view class="data-v-23549a14">是否休息:</view><u-radio-group bind:input="__e" vue-id="{{('7c8f4ba6-19')+','+('7c8f4ba6-1')}}" placement="row" value="{{formList.isRest}}" data-event-opts="{{[['^input',[['__set_model',['$0','isRest','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{roleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('7c8f4ba6-20-'+index)+','+('7c8f4ba6-19')}}" customStyle="{{({marginBottom:'8px'})}}" label="{{item.label}}" name="{{item.value}}" class="data-v-23549a14" bind:__l="__l"></u-radio></block></u-radio-group></view><view class="formList data-v-23549a14"><view class="data-v-23549a14">是否明星教练:</view><u-radio-group bind:input="__e" vue-id="{{('7c8f4ba6-21')+','+('7c8f4ba6-1')}}" placement="row" value="{{formList.isFamous}}" data-event-opts="{{[['^input',[['__set_model',['$0','isFamous','$event',[]],['formList']]]]]}}" class="data-v-23549a14" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{roleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('7c8f4ba6-22-'+index)+','+('7c8f4ba6-21')}}" customStyle="{{({marginBottom:'8px'})}}" label="{{item.label}}" name="{{item.value}}" class="data-v-23549a14" bind:__l="__l"></u-radio></block></u-radio-group></view></view><view class="whiteView data-v-23549a14"></view><view class="bottonBtn u-flex data-v-23549a14"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-23549a14" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">确认修改</view></view></view></theme-wrap>