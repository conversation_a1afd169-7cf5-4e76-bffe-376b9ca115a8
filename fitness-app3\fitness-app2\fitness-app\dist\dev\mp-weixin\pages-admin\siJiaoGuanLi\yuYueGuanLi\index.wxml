<theme-wrap scoped-slots-compiler="augmented" vue-id="f8d453ca-1" class="data-v-4e91344b" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-4e91344b" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('f8d453ca-2')+','+('f8d453ca-1')}}" title="预约管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-4e91344b" bind:__l="__l"></u-navbar><view class="container u-p-t-40 data-v-4e91344b"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 data-v-4e91344b" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view></view><block wx:if="{{$root.g0>0}}"><view class="u-p-l-20 u-p-r-20 data-v-4e91344b"><block wx:for="{{roleList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view class="u-p-t-20 u-p-b-20 u-border-bottom u-flex data-v-4e91344b"><view class="u-flex-1 data-v-4e91344b"><view class="title bold data-v-4e91344b">{{"预约用户："+list.memberName}}</view><view class="value u-flex u-m-t-20 data-v-4e91344b"><view class="u-flex-1 bold data-v-4e91344b">{{"课程名称："+list.courseName}}</view><view class="data-v-4e91344b">{{"预约时间："+list.bookingTime}}</view></view></view></view></block><view class="whiteView data-v-4e91344b"></view></view></block><block wx:else><u-empty vue-id="{{('f8d453ca-3')+','+('f8d453ca-1')}}" marginTop="150" mode="data" class="data-v-4e91344b" bind:__l="__l"></u-empty></block><u-picker vue-id="{{('f8d453ca-4')+','+('f8d453ca-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-4e91344b" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-4e91344b"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-4e91344b" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">在线预约</view></view></view></theme-wrap>