(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/changGuan/index"],{1797:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return d}});var r,o={uNoticeBar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-notice-bar/u-notice-bar")]).then(n.bind(n,19555))},"u-Input":function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u--input/u--input")]).then(n.bind(n,59242))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("ceae42c8-1")),r=n?t.$getSSP("ceae42c8-1","content"):null,o=n?t.$getSSP("ceae42c8-1","content"):null,i=n?t.__map(t.changGuan,(function(e,n){var r=t.__get_orig(e),o=t._f("Img")(e.logo),i=(e.distance/1e3).toFixed(2);return{$orig:r,f0:o,g0:i}})):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:r,m2:o,l0:i}})},a=[],u=n(2113),c=u["default"],l=n(16608),s=n.n(l),f=(s(),n(18535)),h=(0,f["default"])(c,i,a,!1,null,null,null,!1,o,r),d=h.exports},2113:function(t,e,n){"use strict";var r=n(81715)["default"];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;n(77020);var i=a(n(68466));function a(t){return t&&t.__esModule?t:{default:t}}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),u=new $(r||[]);return i(a,"_invoke",{value:k(t,n,u)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",g="suspendedYield",v="executing",y="completed",m={};function w(){}function b(){}function x(){}var _={};f(_,c,(function(){return this}));var L=Object.getPrototypeOf,E=L&&L(L(I([])));E&&E!==n&&r.call(E,c)&&(_=E);var S=x.prototype=w.prototype=Object.create(_);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function n(i,a,u,c){var l=d(t[i],t,a);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):e.resolve(f).then((function(t){s.value=t,u(s)}),(function(t){return n("throw",t,u,c)}))}c(l.arg)}var a;i(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return a=a?a.then(o,o):o()}})}function k(e,n,r){var o=p;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var u=r.delegate;if(u){var c=G(u,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var l=d(e,n,r);if("normal"===l.type){if(o=r.done?y:g,l.arg===m)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(o=y,r.method="throw",r.arg=l.arg)}}}function G(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,G(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=d(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function I(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=x,i(S,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,s,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(P.prototype),f(P.prototype,l,(function(){return this})),e.AsyncIterator=P,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new P(h(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),f(S,s,"Generator"),f(S,c,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=I,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return u.type="throw",u.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),N(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;N(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:I(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function c(t,e,n,r,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function l(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(t){c(i,r,o,a,u,"next",t)}function u(t){c(i,r,o,a,u,"throw",t)}a(void 0)}))}}var s=function(){n.e("pages/changGuan/nav-bar").then(function(){return resolve(n(35605))}.bind(null,n))["catch"](n.oe)};e["default"]={components:{navBar:s},props:{buttonBgColor:{type:String,default:"#fff"},buttonTextColor:{type:String,default:"#000"}},data:function(){return{text1:"如果没有健身房，尝试重新选择一下当前位置，查看其他地方的健身房哦~",address:"当前位置",loading:!0,changGuan:[],x:"",y:""}},mounted:function(){this.positioning(),this.loadData()},onShow:function(){},methods:{positioning:function(){var t=this;r.getLocation({type:"wgs84",success:function(e){console.log(e),t.x=e.longitude,t.y=e.latitude,t.getShopList()},fail:function(t){console.log("失败",t)},complete:function(){r.hideLoading()}})},choseAdvice:function(){var t=this;r.chooseLocation({success:function(e){console.log(e),console.log("经度："+e.longitude),console.log("纬度："+e.latitude),console.log("详细地址："+e.address),console.log("名称："+e.name),t.fx=e.longitude,t.y=e.latitude,t.address=e.address,t.getShopList()}})},getShopList:function(){var t=this;i.default.getShopListForDistance({data:{distance:999999999999,companyId:1,longitude:this.x,latitude:this.y}}).then((function(e){if(r.hideLoading(),200==e.code){var n=e.rows;t.changGuan=n}})).catch((function(t){r.hideLoading()}))},loadData:function(){},changeChangGuan:function(t){var e=this;return l(u().mark((function n(){return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,r.$emit("changeChangGuan",e.changGuan[t]);case 2:e.$nextTick(l(u().mark((function t(){return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,r.navigateBack();case 2:case"end":return t.stop()}}),t)}))));case 3:case"end":return n.stop()}}),n)})))()}}}},16608:function(){},46610:function(t,e,n){"use strict";var r=n(51372)["default"],o=n(81715)["createPage"];n(96910);a(n(923));var i=a(n(1797));function a(t){return t&&t.__esModule?t:{default:t}}r.__webpack_require_UNI_MP_PLUGIN__=n,o(i.default)}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(46610)}));t.O()}]);