{"version": 3, "file": "pages/ruChang/qrcode.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACjCA,IAAAA,gBAAA,GAAAC,sBAAA,CAAAC,mBAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,mBAAA;AACA,IAAAE,IAAA,GAAAH,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,MAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,SAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IACA,KAAAC,QAAA;EACA;EACAC,MAAA,WAAAA,OAAA;IACA,KAAAL,MAAA,GAAAM,GAAA,CAAAC,cAAA;IACA,UAAAP,MAAA;MACAM,GAAA,CAAAE,SAAA;QACAC,KAAA;QACAC,IAAA;QACAC,QAAA;QACAC,IAAA;QACAC,QAAA,WAAAA,SAAA;UACAC,UAAA;YACAR,GAAA,CAAAS,YAAA;UACA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAZ,QAAA,WAAAA,SAAA;MAAA,IAAAa,KAAA;MACAC,YAAA,CAAAC,gBAAA;QACAvB,IAAA;MACA,GAAAwB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA;UACA,IAAAI,aAAA,GAAAJ,GAAA,CAAAK,IAAA;UACA,IAAAD,aAAA,CAAAE,MAAA;YACA,IAAAF,aAAA,IAAAG,QAAA;cACAX,KAAA,CAAAhB,QAAA,GAAAwB,aAAA,IAAAxB,QAAA;cACAgB,KAAA,CAAAY,cAAA,CAAAJ,aAAA,IAAAxB,QAAA;YACA;cACAgB,KAAA,CAAAlB,OAAA;YACA;UACA;YACAkB,KAAA,CAAAlB,OAAA;UACA;QACA;MACA;IACA;IACA8B,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,IAAA;MACA,IAAAE,EAAA,OAAAlC,wBAAA;MACAkC,EAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAJ,IAAA;MACAE,EAAA,CAAAG,IAAA;MACA,IAAAC,GAAA,GAAAJ,EAAA,CAAAK,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACA;MACA,IAAAC,SAAA,kCAAAC,aAAA,CAAAC,MAAA,CAAAN,GAAA;MACA,KAAAlC,SAAA,GAAAsC,SAAA;MACA,KAAAG,SAAA;QACAZ,MAAA,CAAAhC,OAAA;MACA;IACA;EACA;AACA;;;;;;;;;;ACvGA;;;;;;;;;;;;;;;ACAAT,mBAAA;AAGA,IAAAsD,IAAA,GAAAvD,sBAAA,CAAAC,mBAAA;AACA,IAAAuD,OAAA,GAAAxD,sBAAA,CAAAC,mBAAA;AAA6C,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH7C;AACAqD,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLmG;AACnH;AACA,CAA0D;AACL;AACrD,CAAmE;;;AAGnE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBmf,CAAC,+DAAe,0dAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,k4BAAG,EAAC", "sources": ["webpack:///./src/pages/ruChang/qrcode.vue?10ba", "uni-app:///src/pages/ruChang/qrcode.vue", "webpack:///./src/pages/ruChang/qrcode.vue?c3b0", "uni-app:///src/main.js", "webpack:///./src/pages/ruChang/qrcode.vue?fb17", "webpack:///./src/pages/ruChang/qrcode.vue?b20d", "webpack:///./src/pages/ruChang/qrcode.vue?84a8", "webpack:///./src/pages/ruChang/qrcode.vue?d4aa"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"bc804e3c-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"bc804e3c-1\", \"content\")[\"buttonTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"bc804e3c-1\", \"content\") : null\n  var m2 =\n    m0 && !_vm.loading && !_vm.memberId\n      ? _vm.$getSSP(\"bc804e3c-1\", \"content\")\n      : null\n  var m3 =\n    m0 && !_vm.loading && !_vm.memberId\n      ? _vm.$getSSP(\"bc804e3c-1\", \"content\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n\t<themeWrap>\n\t\t<template #content=\"{ navBarColor, buttonTextColor, buttonLightBgColor }\">\n\t\t\t<!-- 主页navbar -->\n\t\t\t<u-navbar :titleStyle=\"{ color: buttonTextColor }\" :bgColor=\"navBarColor\" :placeholder=\"true\" title=\"入场二维码\"\n\t\t\t\t:autoBack=\"true\" :border=\"false\" :safeAreaInsetTop=\"true\">\n\t\t\t</u-navbar>\n\t\t\t<view v-if=\"!loading&&!memberId\" class=\"u-p-t-40 u-p-r-40 u-p-l-40 ltc u-text-center w-100\">\n\t\t\t\t<u-icon name=\"error-circle\" :color=\"buttonLightBgColor\" labelPos=\"right\"\n\t\t\t\t\t:labelColor=\"buttonLightBgColor\" size=\"21\" labelSize=\"16\" space=\"10\"\n\t\t\t\t\tlabel=\"非会员无法使用此功能，请购买会员卡。\"></u-icon>\n\t\t\t</view>\n\t\t\t<view class=\"content u-p-t-80 u-flex u-row-center u-col-center\">\n\t\t\t\t<view class=\"u-flex-col u-m-t-80 u-row-center u-col-center\">\n\t\t\t\t\t<view class=\"bg-fff u-p-20 border-24\" style=\"box-sizing: border-box\">\n\t\t\t\t\t\t<view class=\"placeholder\" style=\"line-height: 0; width: 70vw; height: 70vw\">\n\t\t\t\t\t\t\t<image v-show=\"!loading\" :src=\"qrcodeUrl\" mode=\"aspectFit\"\n\t\t\t\t\t\t\t\tstyle=\"width: 100%; height: 100%;\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<navigator :url=\"`/pages/huiYuanKa/index?id=${shopId}`\"\n\t\t\t\t\t\tclass=\"u-font-36 font-bold fc-fff lbc u-m-t-80 w-100 u-text-center u-p-t-20 u-p-b-20 border-24\">\n\t\t\t\t\t\t购买会员卡\n\t\t\t\t\t</navigator>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</themeWrap>\n</template>\n\n<script>\n\timport qrcode from 'qrcode-generator';\n\timport Base64 from 'base-64';\n\timport api from \"@/common/api\";\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlist: [],\n\t\t\t\tqrcode: \"\",\n\t\t\t\tloading: true,\n\t\t\t\tshopId: '',\n\t\t\t\tmemberId: null,\n\t\t\t\tqrcodeUrl: ''\n\t\t\t};\n\t\t},\n\t\tonShow() {\n\t\t\tthis.loadData();\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.shopId = uni.getStorageSync('nowShopId')\n\t\t\tif (!this.shopId) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择门店',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1200,\n\t\t\t\t\tmask: true,\n\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t}, 1200)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tloadData() {\n\t\t\t\tapi.getMyCardPayment({\n\t\t\t\t\tdata: {},\n\t\t\t\t}).then((res) => {\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\tconst huiYuanKaList = res.rows;\n\t\t\t\t\t\tif (huiYuanKaList.length > 0) {\n\t\t\t\t\t\t\tif (huiYuanKaList[0].restDays > 0) {\n\t\t\t\t\t\t\t\tthis.memberId = huiYuanKaList[0].memberId\n\t\t\t\t\t\t\t\tthis.generateQrcode(huiYuanKaList[0].memberId);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgenerateQrcode(text) {\n\t\t\t\tif (!text) return;\n\t\t\t\tconst qr = qrcode(0, 'L');\n\t\t\t\tqr.addData(String(text));\n\t\t\t\tqr.make();\n\t\t\t\tconst svg = qr.createSvgTag({\n\t\t\t\t\tcellColor: '#000',\n\t\t\t\t\tmargin: 0\n\t\t\t\t});\n\t\t\t\t// 使用 base-64 库进行 Base64 编码\n\t\t\t\tconst svgBase64 = 'data:image/svg+xml;base64,' + Base64.encode(svg);\n\t\t\t\tthis.qrcodeUrl = svgBase64;\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t};\n</script>\n<style lang=\"scss\">\n\t.content {\n\t\theight: 100%;\n\t\twidth: 100vw;\n\t}\n</style>", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/ruChang/qrcode.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./qrcode.vue?vue&type=template&id=47483ca4&\"\nvar renderjs\nimport script from \"./qrcode.vue?vue&type=script&lang=js&\"\nexport * from \"./qrcode.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qrcode.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ruChang/qrcode.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrcode.vue?vue&type=template&id=47483ca4&\""], "names": ["_qrcodeGenerator", "_interopRequireDefault", "require", "_base", "_api", "e", "__esModule", "default", "data", "list", "qrcode", "loading", "shopId", "memberId", "qrcodeUrl", "onShow", "loadData", "onLoad", "uni", "getStorageSync", "showToast", "title", "icon", "duration", "mask", "complete", "setTimeout", "navigateBack", "methods", "_this", "api", "getMyCardPayment", "then", "res", "code", "console", "log", "huiYuanKaList", "rows", "length", "restDays", "generateQrcode", "text", "_this2", "qr", "addData", "String", "make", "svg", "createSvgTag", "cellColor", "margin", "svgBase64", "Base64", "encode", "$nextTick", "_vue", "_qrcode", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}