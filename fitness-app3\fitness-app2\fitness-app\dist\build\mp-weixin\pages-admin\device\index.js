(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages-admin/device/index"],{1385:function(t,e,n){"use strict";var o=n(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=i(n(68466));function i(t){return t&&t.__esModule?t:{default:t}}e["default"]={data:function(){return{currentPage:1,total:1,list:[],limit:100,type:""}},onLoad:function(t){this.type=null===t||void 0===t?void 0:t.type,console.log(11,t.type)},onShow:function(){var t=getCurrentPages(),e=t[t.length-1],n=e.options;this.type=null===n||void 0===n?void 0:n.type,console.log(1,n.type),this.loadData()},onReachBottom:function(){o.showToast({title:"加载中",mask:!0,icon:"loading"}),this.total>this.currentPage?(this.currentPage++,this.loadData()):o.showToast({title:"没有更多数据了",icon:"none"})},methods:{jumpface:function(t){o.navigateTo({url:"/pages-admin/device/faceindex?id=".concat(t.id,"&type=edit")})},start:function(t){var e="openDoor";a.default[e]({data:{deviceId:t.id},method:"post"}).then((function(t){console.log(t)}))},jump:function(t){o.navigateTo({url:"/pages-admin/device/info?list=".concat(t,"&type=edit")})},del:function(t){var e=this;o.showModal({title:"确认删除",content:"您确定要删除该项吗？",success:function(n){n.confirm&&a.default["deldevice"]({data:{ids:t.id},method:"delete"}).then((function(t){console.log(t.rows),e.loadData()}))}})},loadData:function(){var t=this,e="getdevicelist";a.default[e]({data:{pageNum:this.currentPage,pageSize:this.limit}}).then((function(e){console.log(e.rows),1==t.currentPage?t.list=e.rows:t.list=t.list.concat(e.rows),t.total=Math.floor(e.total/t.limit)+1,t.$nextTick((function(){o.hideToast()}))}))},toAddCourse:function(){o.navigateTo({url:"/pages-admin/device/info"})}}}},39446:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return l.__esModule},default:function(){return v}});var o,a={uNavbar:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(n.bind(n,66372))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(n,65610))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$hasSSP("72d25f36-1")),o=n?{color:t.$getSSP("72d25f36-1","content")["navBarTextColor"]}:null,a=n?t.$getSSP("72d25f36-1","content"):null,i=n?t.$getSSP("72d25f36-1","content"):null,u=n?t.list.length:null,l=n?t.$getSSP("72d25f36-1","content"):null;t._isMounted||(t.e0=function(e){return t.uni.navigateBack()},t.e1=function(e,n){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];n=a.item;t.jump(JSON.stringify(n))}),t.$mp.data=Object.assign({},{$root:{m0:n,a0:o,m1:a,m2:i,g0:u,m3:l}})},u=[],l=n(1385),c=l["default"],r=n(83866),d=n.n(r),s=(d(),n(18535)),f=(0,s["default"])(c,i,u,!1,null,"4a01e30e",null,!1,a,o),v=f.exports},44132:function(t,e,n){"use strict";var o=n(51372)["default"],a=n(81715)["createPage"];n(96910);u(n(923));var i=u(n(39446));function u(t){return t&&t.__esModule?t:{default:t}}o.__webpack_require_UNI_MP_PLUGIN__=n,a(i.default)},83866:function(){}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(44132)}));t.O()}]);