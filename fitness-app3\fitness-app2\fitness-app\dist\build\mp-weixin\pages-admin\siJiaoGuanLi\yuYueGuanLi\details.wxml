<theme-wrap scoped-slots-compiler="augmented" vue-id="352adeeb-1" class="data-v-5399b1a2" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-5399b1a2" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('352adeeb-2')+','+('352adeeb-1')}}" title="会员预约" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-5399b1a2" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-5399b1a2"><view class="formView data-v-5399b1a2"><view data-event-opts="{{[['tap',[['choseUser',['$event']]]]]}}" class="formList data-v-5399b1a2" bindtap="__e"><view class="data-v-5399b1a2">选择会员:</view><u--input bind:input="__e" vue-id="{{('352adeeb-3')+','+('352adeeb-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择会员" value="{{formList.nickName}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickName','$event',[]],['formList']]]]]}}" class="data-v-5399b1a2" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="formList data-v-5399b1a2" bindtap="__e"><view class="data-v-5399b1a2">选择会员卡:</view><u--input bind:input="__e" vue-id="{{('352adeeb-4')+','+('352adeeb-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择会员卡" value="{{formList.cardName}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardName','$event',[]],['formList']]]]]}}" class="data-v-5399b1a2" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="formList data-v-5399b1a2" bindtap="__e"><view class="data-v-5399b1a2">选择教练:</view><u--input bind:input="__e" vue-id="{{('352adeeb-5')+','+('352adeeb-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择教练" value="{{formList.coachName}}" data-event-opts="{{[['^input',[['__set_model',['$0','coachName','$event',[]],['formList']]]]]}}" class="data-v-5399b1a2" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="formList data-v-5399b1a2" bindtap="__e"><view class="data-v-5399b1a2">选择课程:</view><u--input bind:input="__e" vue-id="{{('352adeeb-6')+','+('352adeeb-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择课程" value="{{formList.courseName}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseName','$event',[]],['formList']]]]]}}" class="data-v-5399b1a2" bind:__l="__l"></u--input></view><block alt="授课时间" class="data-v-5399b1a2"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between data-v-5399b1a2"><text class="font-bold u-font-36 data-v-5399b1a2">授课时间</text></view><view class="w-100 bg-fff border-16 overflow-hidden data-v-5399b1a2" style="margin-bottom:180rpx;"><calendar bind:changeActive="__e" vue-id="{{('352adeeb-7')+','+('352adeeb-1')}}" data-event-opts="{{[['^changeActive',[['changeActive']]]]}}" class="data-v-5399b1a2" bind:__l="__l"></calendar><block wx:if="{{$root.g0}}"><view class="u-border-top u-p-t-20 u-p-b-20 u-relative data-v-5399b1a2"><view hidden="{{!(loading)}}" class="u-absolute u-flex u-row-center u-col-center w-100 h-100 data-v-5399b1a2" style="top:0;left:0;"><u-loading-icon vue-id="{{('352adeeb-8')+','+('352adeeb-1')}}" mode="circle" loading="{{true}}" class="data-v-5399b1a2" bind:__l="__l"></u-loading-icon></view><view class="courses-wrap data-v-5399b1a2"><block wx:for="{{courses}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="course-blk u-p-10 data-v-5399b1a2"><view data-event-opts="{{[['tap',[['changeCurrentIndex',[index,'$0'],[[['courses','',index,'status']]]]]]]}}" class="{{['w-100','u-p-t-20','u-relative','u-p-b-20','u-text-center','course-item','border-16','data-v-5399b1a2',(i.status!==1)?'disabled':'',(index==currentIndex)?'active':'',(i.status===2)?'expired':'',(i.status===3)?'full':'',(i.status===4)?'rest':'']}}" bindtap="__e">{{''+i.text+''}}</view></view></block></view></view></block><block wx:else><view class="w-100 u-p-t-80 u-border-top u-flex-col u-row-center u-col-center u-p-b-80 data-v-5399b1a2"><image style="width:300rpx;height:300rpx;" src="/static/images/empty/order.png" mode="widthFix" class="data-v-5399b1a2"></image><view class="u-fotn-30 u-tips-color data-v-5399b1a2">暂无排期</view></view></block></view></block></view></view><u-picker vue-id="{{('352adeeb-9')+','+('352adeeb-1')}}" show="{{showType}}" columns="{{actions}}" keyName="cardName" data-event-opts="{{[['^confirm',[['typeSelect']]],['^cancel',[['e4']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-5399b1a2" bind:__l="__l"></u-picker><u-picker vue-id="{{('352adeeb-10')+','+('352adeeb-1')}}" show="{{showMember}}" columns="{{actionsMember}}" keyName="coachName" data-event-opts="{{[['^confirm',[['memberSelect']]],['^cancel',[['e5']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-5399b1a2" bind:__l="__l"></u-picker><u-picker vue-id="{{('352adeeb-11')+','+('352adeeb-1')}}" show="{{showLX}}" columns="{{actionsLX}}" keyName="courseName" data-event-opts="{{[['^confirm',[['courseSelect']]],['^cancel',[['e6']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-5399b1a2" bind:__l="__l"></u-picker><u-picker vue-id="{{('352adeeb-12')+','+('352adeeb-1')}}" show="{{showTime}}" columns="{{courses}}" keyName="text" data-event-opts="{{[['^confirm',[['timeSelect']]],['^cancel',[['e7']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-5399b1a2" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-5399b1a2"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-5399b1a2" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">预约</view></view></view></theme-wrap>