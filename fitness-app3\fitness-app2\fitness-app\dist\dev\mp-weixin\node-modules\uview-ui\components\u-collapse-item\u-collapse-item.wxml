<view class="u-collapse-item data-v-3bf52cdf"><u-cell vue-id="134b7fd5-1" title="{{title}}" value="{{value}}" label="{{label}}" icon="{{icon}}" isLink="{{isLink}}" clickable="{{clickable}}" border="{{parentData.border&&showBorder}}" arrowDirection="{{expanded?'up':'down'}}" disabled="{{disabled}}" data-event-opts="{{[['^click',[['clickHandler']]]]}}" bind:click="__e" class="data-v-3bf52cdf" bind:__l="__l"></u-cell><view class="u-collapse-item__content data-v-3bf52cdf vue-ref" animation="{{animationData}}" data-ref="animation"><view class="u-collapse-item__content__text content-class data-v-3bf52cdf vue-ref" id="{{elId}}" data-ref="{{elId}}"><slot></slot></view></view><block wx:if="{{parentData.border}}"><u-line vue-id="134b7fd5-2" class="data-v-3bf52cdf" bind:__l="__l"></u-line></block></view>