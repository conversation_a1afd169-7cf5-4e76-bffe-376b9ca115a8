<theme-wrap scoped-slots-compiler="augmented" vue-id="6298ffae-1" class="data-v-0174d547" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-0174d547" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('6298ffae-2')+','+('6298ffae-1')}}" title="修改教练管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-0174d547" bind:__l="__l"></u-navbar><view class="formView data-v-0174d547"><view class="formTextarea border-8 data-v-0174d547"><view class="textareaTitle u-flex data-v-0174d547"><view class="u-flex-1 data-v-0174d547">授课设置:</view></view><view class="formLogo data-v-0174d547"><block wx:for="{{formList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('6298ffae-3-'+index)+','+('6298ffae-1')}}" title="{{item.courseName}}" class="data-v-0174d547" bind:__l="__l" vue-slots="{{['right-icon']}}"><view hidden="{{!(item.active!=true)}}" style="color:blue;font-size:24rpx;" slot="right-icon" data-event-opts="{{[['tap',[['confirm',['$0'],[[['formList','',index,'courseId']]]]]]]}}" bindtap="__e" class="data-v-0174d547">授课</view><view hidden="{{!(item.active==true)}}" style="color:blue;font-size:24rpx;" slot="right-icon" data-event-opts="{{[['tap',[['removeCourse',['$0'],[[['formList','',index,'courseId']]]]]]]}}" bindtap="__e" class="data-v-0174d547">取消授课</view></u-cell></block></view></view></view><view class="whiteView data-v-0174d547"></view><view class="bottonBtn u-flex data-v-0174d547"><view data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="confirmBtn data-v-0174d547" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">返回</view></view></view></theme-wrap>