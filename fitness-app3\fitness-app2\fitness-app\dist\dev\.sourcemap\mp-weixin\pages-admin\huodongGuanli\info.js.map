{"version": 3, "file": "pages-admin/huodong<PERSON>/info.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,+VAEN;AACP,KAAK;AACL;AACA,aAAa,+aAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,4PAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACoEA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAT,CAAA,EAAAU,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAb,CAAA,OAAAY,MAAA,CAAAE,qBAAA,QAAAV,CAAA,GAAAQ,MAAA,CAAAE,qBAAA,CAAAd,CAAA,GAAAU,CAAA,KAAAN,CAAA,GAAAA,CAAA,CAAAW,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAhB,CAAA,EAAAU,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAP,CAAA,YAAAO,CAAA;AAAA,SAAAS,cAAApB,CAAA,aAAAU,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAG,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAxB,CAAA,EAAAU,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAA1B,CAAA,EAAAY,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAF,OAAA,CAAAG,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAA3B,CAAA,EAAAU,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAV,CAAA;AAAA,SAAA4B,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAEA;EACAC,UAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA,GACA;QACAC,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA,GACA;MACAC,aAAA;MACAC,cAAA;MACAC,YAAA;MACAC,KAAA;MACAC,YAAA;MACAC,QAAA;MACAC,OAAA;MACAC,aAAA;MACAC,MAAA;MACAC,OAAA;MACAC,SAAA;MACAC,MAAA;MACAC,OAAA,GACA;QACAd,KAAA;QACAC,EAAA;MACA;QACAD,KAAA;QACAC,EAAA;MACA,GACA;MACAA,EAAA;MACAc,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,IAAA,EAAA3B,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA;QACA4B,KAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,IAAA;QACAX,SAAA;QACAY,UAAA;QACAb,OAAA;QACAc,QAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;QACAC,IAAA;QACAC,WAAA;QACApB,MAAA;QACAqB,UAAA;QACAC,OAAA;QACAC,MAAA;MAAA,aACA,kBACA,kBACA,oBACA,qBACA,eACA,iBACA,GACA;MACAC,iBAAA;MACAC,KAAA;QACAZ,IAAA,GACA;UACAa,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAZ,WAAA,GACA;UACAU,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAX,YAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,SAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAE,IAAA;MACAC,OAAA,EAAAC,IAAA,CAAAC,GAAA;MACAC,MAAA;MACAC,SAAA;MACAC,YAAA;MACAjB,IAAA;MACAkB,QAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,eAAAF,GAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,QAAA,MAAAtB,KAAA;EACA;EACAuB,MAAA,WAAAA,OAAAC,MAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAlE,mBAAA,GAAAmE,IAAA,UAAAC,QAAA;MAAA,IAAAC,cAAA,EAAAC,SAAA,EAAAC,QAAA,EAAAC,IAAA;MAAA,OAAAxE,mBAAA,GAAAyE,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAZ,KAAA,CAAApB,IAAA,GAAAiC,GAAA,CAAAC,cAAA;YACAd,KAAA,CAAA1C,IAAA,CAAAyD,QAAA,GAAAF,GAAA,CAAAC,cAAA;YACAd,KAAA,CAAA1C,IAAA,CAAAU,MAAA,GAAA6C,GAAA,CAAAC,cAAA;YACAd,KAAA,CAAA3D,EAAA,GAAA0D,MAAA,CAAA1D,EAAA;YAEA,IAAA0D,MAAA,CAAAiB,IAAA,IAAAjB,MAAA,CAAA9B,IAAA;cACAlD,MAAA,CAAAC,IAAA,CAAAiG,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,GAAAtF,OAAA,WAAAyF,GAAA;gBACA,IAAAnB,KAAA,CAAA1C,IAAA,CAAA6D,GAAA,MAAAC,SAAA;kBACApB,KAAA,CAAA1C,IAAA,CAAA6D,GAAA,IAAAF,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAG,GAAA;gBACA;cACA;cACAnB,KAAA,CAAA1C,IAAA,CAAAc,OAAA,GAAA6C,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAA5C,OAAA,CAAAiD,KAAA;cACA7B,OAAA,CAAAC,GAAA,CAAAO,KAAA,CAAA9C,OAAA,EAAA8C,KAAA,CAAA9C,OAAA,IAAAhC,MAAA,WAAAoG,IAAA;gBAAA,OAAAA,IAAA,CAAAjF,EAAA,IAAA4E,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAA/C,IAAA;cAAA,IAAAgD,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAA/C,IAAA;cACA+B,KAAA,CAAA1C,IAAA,CAAAI,QAAA,GAAAsC,KAAA,CAAA9C,OAAA,IAAAhC,MAAA,WAAAoG,IAAA;gBAAA,OAAAA,IAAA,CAAAjF,EAAA,IAAA4E,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAA/C,IAAA;cAAA,MAAA7B,KAAA;cACA;cACA4D,KAAA,CAAA1C,IAAA,CAAAQ,WAAA,GAAAyD,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAlD,WAAA;cACAkC,KAAA,CAAA1C,IAAA,CAAAS,YAAA,GAAAwD,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAjD,YAAA;cACAiC,KAAA,CAAA1C,IAAA,CAAAqB,SAAA,GAAA4C,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAArC,SAAA;cACAqB,KAAA,CAAA1C,IAAA,CAAAjB,EAAA,GAAAkF,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAA3E,EAAA;cACA2D,KAAA,CAAA1C,IAAA,CAAAC,KAAA,GAAA0D,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAzD,KAAA,GAAA0D,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAzD,KAAA;cACA6C,cAAA;cACAa,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAlE,MAAA,CAAA0E,WAAA,CAAA9F,OAAA,WAAA+F,GAAA;gBACArB,cAAA,CAAA/E,IAAA,CAAAqG,MAAA,CAAAD,GAAA,CAAAE,YAAA;cACA;cACA3B,KAAA,CAAA1D,aAAA,GAAA8D,cAAA;cACAa,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAtE,YAAA,CAAA2E,KAAA;cACArB,KAAA,CAAAzD,cAAA,GAAA0E,IAAA,CAAAC,KAAA,CAAAnB,MAAA,CAAAiB,IAAA,EAAAtE,YAAA,CAAA2E,KAAA,MAAAO,GAAA,WAAAC,GAAA;gBAAA,OAAAH,MAAA,CAAAG,GAAA;cAAA;cACA7B,KAAA,CAAAb,QAAA,GAAAY,MAAA,CAAA9B,IAAA;cACAuB,OAAA,CAAAC,GAAA,CAAAO,KAAA,CAAA1C,IAAA,EAAA0C,KAAA,CAAAb,QAAA,EAAAa,KAAA,CAAAzD,cAAA;YACA;YACAuF,YAAA,CAAAC,eAAA;cACA/D,MAAA,EAAA6C,GAAA,CAAAC,cAAA;cACA5E,IAAA;gBACA8F,OAAA;gBACAC,OAAA;gBACAC,QAAA;cACA;YACA,GAAAC,IAAA,WAAAV,GAAA;cACAzB,KAAA,CAAAZ,aAAA,GAAAqC,GAAA,CAAAW,IAAA;YACA;YACA5C,OAAA,CAAAC,GAAA,CAAAO,KAAA,CAAAZ,aAAA;YACAiB,SAAA,GAAAQ,GAAA,CAAAC,cAAA,cAAAd,KAAA,CAAAqC,CAAA;YACA/B,QAAA,GAAAO,GAAA,CAAAC,cAAA,aAAAd,KAAA,CAAAsC,CAAA;YAAA5B,QAAA,CAAAE,IAAA;YAAA,OACAkB,YAAA,CAAAS,sBAAA;cACArG,IAAA;gBACAsG,QAAA;gBACAC,SAAA;gBACApC,SAAA,EAAAA,SAAA;gBAAA;gBACAC,QAAA,EAAAA,QAAA;cACA;YACA;UAAA;YAPAC,IAAA,GAAAG,QAAA,CAAAgC,IAAA;YAQAlD,OAAA,CAAAC,GAAA,CAAAc,IAAA;YACAP,KAAA,CAAArD,QAAA,GAAA4D,IAAA,CAAA6B,IAAA;YACA5C,OAAA,CAAAC,GAAA,CAAAO,KAAA,CAAArD,QAAA;UAAA;UAAA;YAAA,OAAA+D,QAAA,CAAAiC,IAAA;QAAA;MAAA,GAAAxC,OAAA;IAAA;EACA;EACAyC,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA;MACA,IAAApG,KAAA,QAAAa,IAAA,CAAAL,MAAA;MACA;MACA,IAAAR,KAAA,WAAAqG,KAAA,CAAArG,KAAA;QACA;MACA;MACA;MACAA,KAAA,GAAAsG,UAAA,CAAAtG,KAAA;MACA;MACA,IAAAA,KAAA;QACA,KAAAa,IAAA,CAAAL,MAAA;MACA,WAAAR,KAAA;QACA,KAAAa,IAAA,CAAAL,MAAA;MACA;QACA,KAAAK,IAAA,CAAAL,MAAA,GAAAR,KAAA;MACA;IACA;IACAuG,iBAAA,WAAAA,kBAAA;MACA,IAAAlF,WAAA,GAAAiF,UAAA,MAAAzF,IAAA,CAAAQ,WAAA;MACA,IAAAC,YAAA,GAAAgF,UAAA,MAAAzF,IAAA,CAAAS,YAAA;MACA;MACA,IAAAA,YAAA,GAAAD,WAAA;QACA,KAAAmF,EAAA,CAAAC,KAAA;QACA,KAAA5F,IAAA,CAAAS,YAAA;MACA;IACA;IACAoF,aAAA,WAAAA,cAAA;MACA;IAAA,CACA;IACAC,WAAA,WAAAA,YAAA7D,GAAA;MACAC,OAAA,CAAAC,GAAA,WAAAF,GAAA;MACA;IACA;IACA8D,aAAA,WAAAA,cAAAlJ,CAAA;MACAqF,OAAA,CAAAC,GAAA,CAAAtF,CAAA;IACA;IACAmJ,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,IAAA,OAAA1E,IAAA,CAAAyE,IAAA;MACA,IAAAE,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,IAAAH,IAAA,CAAAI,QAAA,QAAAC,QAAA,GAAAC,QAAA;MACA,IAAAC,GAAA,GAAAP,IAAA,CAAAQ,OAAA,GAAAH,QAAA,GAAAC,QAAA;MACA,IAAAG,aAAA,MAAAC,MAAA,CAAAT,IAAA,OAAAS,MAAA,CAAAP,KAAA,OAAAO,MAAA,CAAAH,GAAA;MACA,OAAAE,aAAA;IACA;IACAE,UAAA,WAAAA,WAAAhK,CAAA;MACA,SAAAmD,IAAA,CAAAO,QAAA,QAAAP,IAAA,CAAAM,UAAA;QACA,KAAAqF,EAAA,CAAAC,KAAA;QACA,KAAA5F,IAAA,CAAAO,QAAA;QACA;MACA;MACA,KAAAP,IAAA,CAAAP,OAAA,QAAAO,IAAA,CAAAO,QAAA;MACA,KAAAP,IAAA,CAAAP,OAAA,QAAAuG,UAAA,CAAAnJ,CAAA,CAAAsC,KAAA;MACA,KAAAM,OAAA;IACA;IACAqH,YAAA,WAAAA,aAAAjK,CAAA;MACA,KAAAmD,IAAA,CAAAN,SAAA,QAAAsG,UAAA,CAAAnJ,CAAA,CAAAsC,KAAA;MACA,KAAAO,SAAA;IACA;IACAqH,eAAA,WAAAA,gBAAAC,IAAA,EAAA7H,KAAA,EAAA8H,QAAA;MACA,IAAA9H,KAAA;QACA8H,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACAE,eAAA,WAAAA,gBAAAtK,CAAA;MACAqF,OAAA,CAAAC,GAAA,CAAAtF,CAAA;MACA,IAAAuK,IAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAxK,CAAA,CAAAsB,MAAA,EAAAkJ,CAAA;QACAD,IAAA,CAAArJ,IAAA,CAAAlB,CAAA,CAAAwK,CAAA;MACA;MACA,KAAArH,IAAA,CAAAsH,aAAA,GAAAF,IAAA;MACA,KAAAxF,YAAA;MACA,IAAA/E,CAAA,CAAAsB,MAAA;QACA,KAAA6C,iBAAA,GAAAnE,CAAA,CAAA0J,QAAA;MACA;IACA;IACAgB,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACAhD,YAAA,CAAAiD,SAAA,GAAA5C,IAAA,WAAAV,GAAA;QACAqD,MAAA,CAAAE,YAAA,IAAAvD,GAAA,CAAAW,IAAA;MACA;IACA;IACA6C,YAAA,WAAAA,aAAA9K,CAAA;MACA,KAAAmD,IAAA,CAAAW,IAAA,GAAA9D,CAAA,CAAAsC,KAAA,IAAAJ,EAAA;MACA,KAAAiB,IAAA,CAAAI,QAAA,GAAAvD,CAAA,CAAAsC,KAAA,IAAAL,KAAA;MACA,KAAA6B,IAAA;IACA;IACAiH,aAAA,WAAAA,cAAA/K,CAAA;MACA;MACA,KAAA2C,MAAA;IAEA;IACAqI,eAAA,WAAAA,gBAAAhL,CAAA;MACA;MACA,KAAAmD,IAAA,CAAAL,MAAA,GAAA9C,CAAA,CAAAsC,KAAA,IAAAL,KAAA;MACA,KAAAa,MAAA;IAEA;IAEAmI,aAAA,WAAAA,cAAA;MACAvE,GAAA,CAAAwE,YAAA;QACAC,IAAA,OAAAhI,IAAA,CAAAc,OAAA;QACAmH,gBAAA;UACAC,OAAA,WAAAA,QAAAtJ,IAAA;UACAuJ,IAAA,WAAAA,KAAAC,GAAA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA,GAAAhF,GAAA,CAAAC,cAAA;MACAD,GAAA,CAAAiF,WAAA;QACAC,KAAA;QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QAAA;QACAT,OAAA,WAAAA,QAAA/D,GAAA;UACAZ,GAAA,CAAAqF,WAAA;YACAC,IAAA;YACAC,KAAA;UACA;UACA,IAAAC,aAAA,GAAA5E,GAAA,CAAA4E,aAAA;UACA,IAAAC,aAAA;UACA,IAAAC,WAAA,YAAAA,WAAA;YACA,IAAAD,aAAA,GAAAD,aAAA,CAAA5K,MAAA;cACAoF,GAAA,CAAA2F,UAAA;gBACAC,GAAA,EAAAb,MAAA,CAAAc,UAAA;gBACAC,QAAA,EAAAN,aAAA,CAAAC,aAAA;gBACA3I,IAAA;gBACAiJ,MAAA;kBACAC,aAAA,EAAAhB;gBACA;gBACAL,OAAA,WAAAA,QAAAsB,IAAA;kBACAlB,MAAA,CAAAtI,IAAA,CAAAc,OAAA,CAAA/C,IAAA,CAAA4F,IAAA,CAAAC,KAAA,CAAA4F,IAAA,CAAA5K,IAAA,EAAAuK,GAAA;gBACA;gBACAhB,IAAA,WAAAA,KAAAC,GAAA;kBACAlG,OAAA,CAAAC,GAAA,CAAAiG,GAAA;gBACA;gBACAqB,QAAA,WAAAA,SAAA;kBACAT,aAAA;kBACAC,WAAA;gBACA;cACA;YACA;cACA1F,GAAA,CAAAmG,WAAA;YACA;UACA;UACAT,WAAA;QACA;MACA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACApG,GAAA,CAAAwE,YAAA;QACAC,IAAA,QAAAhI,IAAA,CAAAa,UAAA;QACAoH,gBAAA;UACAC,OAAA,WAAAA,QAAAtJ,IAAA;UACAuJ,IAAA,WAAAA,KAAAC,GAAA;QACA;MACA;IACA;IACAwB,aAAA,WAAAA,cAAA;MACArG,GAAA,CAAAwE,YAAA;QACAC,IAAA,QAAAhI,IAAA,CAAAG,MAAA;QACA8H,gBAAA;UACAC,OAAA,WAAAA,QAAAtJ,IAAA;UACAuJ,IAAA,WAAAA,KAAAC,GAAA;QACA;MACA;IACA;IACAyB,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAvB,KAAA,GAAAhF,GAAA,CAAAC,cAAA;MACAD,GAAA,CAAAiF,WAAA;QACAC,KAAA;QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QAAA;QACAT,OAAA,WAAAA,QAAA/D,GAAA;UACAZ,GAAA,CAAAqF,WAAA;YACAC,IAAA;YACAC,KAAA;UACA;UACA,IAAAC,aAAA,GAAA5E,GAAA,CAAA4E,aAAA;UACAxF,GAAA,CAAA2F,UAAA;YACAC,GAAA,EAAAW,MAAA,CAAAV,UAAA;YACAC,QAAA,EAAAN,aAAA;YACA1I,IAAA;YACAiJ,MAAA;cACAC,aAAA,EAAAhB;YACA;YACAL,OAAA,WAAAA,QAAAsB,IAAA;cACAM,MAAA,CAAA9J,IAAA,CAAAa,UAAA,GAAA8C,IAAA,CAAAC,KAAA,CAAA4F,IAAA,CAAA5K,IAAA,EAAAuK,GAAA;YACA;YACAhB,IAAA,WAAAA,KAAAC,GAAA;cACAlG,OAAA,CAAAC,GAAA,CAAAiG,GAAA;YACA;YACAqB,QAAA,WAAAA,SAAA;cACAlG,GAAA,CAAAmG,WAAA;YACA;UACA;QACA;MACA;IACA;IACAK,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAzB,KAAA,GAAAhF,GAAA,CAAAC,cAAA;MACAD,GAAA,CAAAiF,WAAA;QACAC,KAAA;QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QAAA;QACAT,OAAA,WAAAA,QAAA/D,GAAA;UACAZ,GAAA,CAAAqF,WAAA;YACAC,IAAA;YACAC,KAAA;UACA;UACA,IAAAC,aAAA,GAAA5E,GAAA,CAAA4E,aAAA;UACAxF,GAAA,CAAA2F,UAAA;YACAC,GAAA,EAAAa,MAAA,CAAAZ,UAAA;YACAC,QAAA,EAAAN,aAAA;YACA1I,IAAA;YACAiJ,MAAA;cACAC,aAAA,EAAAhB;YACA;YACAL,OAAA,WAAAA,QAAAsB,IAAA;cACAQ,MAAA,CAAAhK,IAAA,CAAAG,MAAA,GAAAwD,IAAA,CAAAC,KAAA,CAAA4F,IAAA,CAAA5K,IAAA,EAAAuK,GAAA;YACA;YACAhB,IAAA,WAAAA,KAAAC,GAAA;cACAlG,OAAA,CAAAC,GAAA,CAAAiG,GAAA;YACA;YACAqB,QAAA,WAAAA,SAAA;cACAlG,GAAA,CAAAmG,WAAA;YACA;UACA;QACA;MACA;IACA;IACAO,mBAAA,WAAAA,oBAAA;MACA1G,GAAA,CAAAwE,YAAA;QACAC,IAAA,OAAAhI,IAAA,CAAAkK,YAAA;QACAjC,gBAAA;UACAC,OAAA,WAAAA,QAAAtJ,IAAA;UACAuJ,IAAA,WAAAA,KAAAC,GAAA;QACA;MACA;IACA;IACA+B,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAA7B,KAAA,GAAAhF,GAAA,CAAAC,cAAA;MACAD,GAAA,CAAAiF,WAAA;QACAC,KAAA;QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QAAA;QACAT,OAAA,WAAAA,QAAA/D,GAAA;UACAZ,GAAA,CAAAqF,WAAA;YACAC,IAAA;YACAC,KAAA;UACA;UACA,IAAAC,aAAA,GAAA5E,GAAA,CAAA4E,aAAA;UACA,IAAAC,aAAA;UACA,IAAAC,YAAA,YAAAA,WAAA;YACA,IAAAD,aAAA,GAAAD,aAAA,CAAA5K,MAAA;cACAoF,GAAA,CAAA2F,UAAA;gBACAC,GAAA,EAAAiB,MAAA,CAAAhB,UAAA;gBACAC,QAAA,EAAAN,aAAA,CAAAC,aAAA;gBACA3I,IAAA;gBACAiJ,MAAA;kBACAC,aAAA,EAAAhB;gBACA;gBACAL,OAAA,WAAAA,QAAAsB,IAAA;kBACAY,MAAA,CAAApK,IAAA,CAAAkK,YAAA,CAAAnM,IAAA,CAAA4F,IAAA,CAAAC,KAAA,CAAA4F,IAAA,CAAA5K,IAAA,EAAAuK,GAAA;gBACA;gBACAhB,IAAA,WAAAA,KAAAC,GAAA;kBACAlG,OAAA,CAAAC,GAAA,CAAAiG,GAAA;gBACA;gBACAqB,QAAA,WAAAA,SAAA;kBACAT,aAAA;kBACAC,YAAA;gBACA;cACA;YACA;cACA1F,GAAA,CAAAmG,WAAA;YACA;UACA;UACAT,YAAA;QACA;MACA;IACA;IACAoB,UAAA,WAAAA,WAAAxN,CAAA;MACA,KAAAmD,IAAA,CAAAL,MAAA,GAAA9C,CAAA,CAAAsC,KAAA;MACA,KAAAQ,MAAA;IACA;IACA2K,WAAA,WAAAA,YAAAzN,CAAA;MACAqF,OAAA,CAAAC,GAAA,CAAAtF,CAAA;MACA,IAAAqJ,IAAA,OAAA1E,IAAA,CAAA3E,CAAA,CAAAsC,KAAA;MACA,IAAAgH,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,IAAAH,IAAA,CAAAI,QAAA,QAAAC,QAAA,GAAAC,QAAA;MACA,IAAAC,GAAA,GAAAP,IAAA,CAAAQ,OAAA,GAAAH,QAAA,GAAAC,QAAA;MACA,IAAA+D,KAAA,GAAArE,IAAA,CAAAsE,QAAA,GAAAjE,QAAA,GAAAC,QAAA;MACA,IAAAiE,OAAA,GAAAvE,IAAA,CAAAwE,UAAA,GAAAnE,QAAA,GAAAC,QAAA;MACA,IAAAmE,OAAA,GAAAzE,IAAA,CAAA0E,UAAA,GAAArE,QAAA,GAAAC,QAAA;MACA,IAAAG,aAAA,MAAAC,MAAA,CAAAT,IAAA,OAAAS,MAAA,CAAAP,KAAA,OAAAO,MAAA,CAAAH,GAAA,OAAAG,MAAA,CAAA2D,KAAA,OAAA3D,MAAA,CAAA6D,OAAA,OAAA7D,MAAA,CAAA+D,OAAA;MACA,KAAA3K,IAAA,CAAA6K,cAAA,GAAAlE,aAAA;MACA,KAAA3G,IAAA,CAAA8K,aAAA,GAAAnE,aAAA;MACAzE,OAAA,CAAAC,GAAA,MAAAnC,IAAA,KAAA2G,aAAA;MACA,KAAA7G,WAAA;IACA;IACAiL,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAAhL,IAAA,CAAAc,OAAA,CAAAmK,MAAA,CAAAD,KAAA;IACA;IACAE,aAAA,WAAAA,cAAA;MACA,KAAAlL,IAAA,CAAAa,UAAA;IACA;IACAsK,SAAA,WAAAA,UAAA;MACA,KAAAnL,IAAA,CAAAG,MAAA;IACA;IACAiL,eAAA,WAAAA,gBAAAJ,KAAA;MACA,KAAAhL,IAAA,CAAAkK,YAAA,CAAAe,MAAA,CAAAD,KAAA;IACA;IACAK,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAvL,QAAA;MACAmC,OAAA,CAAAC,GAAA,MAAAnC,IAAA;MACA,KAAAqC,KAAA,CAAAC,KAAA,CACAiJ,QAAA,GACA1G,IAAA;QACAtB,GAAA,CAAAiI,SAAA;UACA1C,KAAA;UACA2C,OAAA;UACAvD,OAAA,WAAAA,QAAA/D,GAAA;YACA,IAAAA,GAAA,CAAAuH,OAAA;cACAnI,GAAA,CAAAqF,WAAA;gBACAC,IAAA;cACA;cACAyC,MAAA,CAAAK,MAAA;YACA;cACAL,MAAA,CAAAvL,QAAA;cACAwD,GAAA,CAAAmG,WAAA;YACA;UACA;QACA;MACA,GACAkC,KAAA;QACAN,MAAA,CAAAvL,QAAA;MACA;IACA;IACA4L,MAAA,WAAAA,OAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,UAAA,GAAAnI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAoI,SAAA,MAAA/L,IAAA;MACA8L,UAAA,CAAAhL,OAAA,IAAAgL,UAAA,CAAAhL,OAAA,QAAAgL,UAAA,CAAAhL,OAAA,CAAAkL,IAAA;MACA,IAAAxM,MAAA;QAAA0E,WAAA;MAAA;MACAhC,OAAA,CAAAC,GAAA,MAAAnD,aAAA;MACA,KAAAA,aAAA,CAAAZ,OAAA,WAAAe,KAAA;QACA0M,MAAA,CAAA/J,aAAA,CAAAlE,MAAA,WAAAoG,IAAA;UAAA,OAAAA,IAAA,CAAAK,YAAA,KAAAlF,KAAA;QAAA,GACAf,OAAA,WAAA4F,IAAA;UAAA,OAAAxE,MAAA,CAAA0E,WAAA,CAAAnG,IAAA;YACAsG,YAAA,EAAAL,IAAA,CAAAK,YAAA;YACA4H,QAAA,EAAAjI,IAAA,CAAAiI;UACA;QAAA;MACA;MACAH,UAAA,CAAA1M,YAAA,QAAAH,cAAA,CAAA+M,IAAA;MACAF,UAAA,CAAAtM,MAAA,GAAAA,MAAA;MACA,IAAA2J,GAAA,QAAAtH,QAAA;MACA2C,YAAA,CAAA2E,GAAA;QACAvK,IAAA,EAAAX,aAAA,KACA6N,UAAA,CACA;QACAI,MAAA,OAAArK,QAAA;MACA,GACAgD,IAAA,WAAAV,GAAA;QACAjC,OAAA,CAAAC,GAAA,CAAAgC,GAAA;QACA,IAAAA,GAAA,CAAAgI,IAAA;UACA5I,GAAA,CAAA6I,SAAA;YACAtD,KAAA,KAAAlC,MAAA,CAAAiF,MAAA,CAAAhK,QAAA;YACAwK,IAAA;YACAC,QAAA;YACApE,OAAA,WAAAA,QAAA;UACA;UACAqE,UAAA;YACAhJ,GAAA,CAAAiJ,YAAA;cACAC,KAAA;YACA;UACA;QACA;UACAlJ,GAAA,CAAA6I,SAAA;YACAtD,KAAA,EAAA3E,GAAA,CAAAuI,GAAA;YACAL,IAAA;YACAC,QAAA;YACApE,OAAA,WAAAA,QAAA;UACA;QACA;QACA2D,MAAA,CAAA9L,QAAA;MACA,GACA6L,KAAA,WAAAxD,GAAA;QACAyD,MAAA,CAAA9L,QAAA;MACA;IACA;EACA;AACA;;;;;;;;;;ACxzBA;;;;;;;;;;;;;;;ACAAnD,mBAAA;AAGA,IAAA+P,IAAA,GAAAhQ,sBAAA,CAAAC,mBAAA;AACA,IAAAgQ,KAAA,GAAAjQ,sBAAA,CAAAC,mBAAA;AAAuD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAHvD;AACAgQ,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC;;;;;;;;;;;;;;;;;;;ACL6G;AAC7H;AACA,CAAwD;AACL;AACnD,CAAyF;;;AAGzF;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBif,CAAC,+DAAe,wdAAG,EAAC;;;;;;;;;;;;;;;;;ACAie,CAAC,+DAAe,w5BAAG,EAAC", "sources": ["webpack:///./src/pages-admin/huodong<PERSON>/info.vue?7ebc", "uni-app:///src/pages-admin/huodong<PERSON>/info.vue", "webpack:///./src/pages-admin/huodong<PERSON>/info.vue?a1e5", "uni-app:///src/main.js", "webpack:///./src/pages-admin/huodong<PERSON>/info.vue?c14b", "webpack:///./src/pages-admin/huodong<PERSON>/info.vue?f71c", "webpack:///./src/pages-admin/huodong<PERSON>/info.vue?2fab", "webpack:///./src/pages-admin/huodong<PERSON>/info.vue?fb59"], "sourcesContent": ["var components\ntry {\n  components = {\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form/u-form\" */ \"uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form-item/u-form-item\" */ \"uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    zqsSelect: function () {\n      return import(\n        /* webpackChunkName: \"components/zqs-select/zqs-select\" */ \"@/components/zqs-select/zqs-select.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"7c21b520-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"7c21b520-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"7c21b520-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"7c21b520-1\", \"content\") : null\n  var m3 = m0 ? _vm.$getSSP(\"7c21b520-1\", \"content\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n    _vm.e1 = function ($event) {\n      _vm.startTime = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.startTime = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.startTime = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.endTime = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.endTime = false\n    }\n    _vm.e6 = function ($event) {\n      _vm.endTime = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.type = true\n    }\n    _vm.e8 = function ($event) {\n      _vm.type = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\r\n    <themeWrap>\r\n        <template #content=\"{ navBarColor, navBarTextColor, buttonLightBgColor }\">\r\n            <u-toast ref=\"toast\"></u-toast>\r\n            <view>\r\n                <!-- 顶部菜单栏 -->\r\n                <u-navbar title=\"编辑\" @rightClick=\"uni.navigateBack()\" :titleStyle=\"{ color: navBarTextColor }\"\r\n                    :bgColor=\"navBarColor\" :leftIconColor=\"navBarTextColor\" :autoBack=\"true\" :placeholder=\"true\"\r\n                    :safeAreaInsetTop=\"true\">\r\n                </u-navbar>\r\n            </view>\r\n            <view class=\"container u-p-t-40 bottom-placeholder\">\r\n                <u-form :model=\"form\" ref=\"uForm\" labelWidth=\"140\">\r\n                    <view class=\"u-m-b-34 u-p-34 border-16 bg-fff\">\r\n                        <u-form-item required borderBottom prop=\"name\" label=\"活动名称\">\r\n                            <u-input inputAlign=\"right\" border=\"none\" placeholder=\"请输入活动名称\"\r\n                                v-model=\"form.name\"></u-input>\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"startTime\" label=\"活动开始时间\">\r\n                            <view class=\"w-100 u-text-right u-font-30\" @click=\"startTime = true\"\r\n                                :style=\"{ color: form.startTime ? '#333' : '#c0c4cc' }\">\r\n                                {{ form.startTime || '请选择活动开始时间' }}\r\n                            </view>\r\n                            <u-datetime-picker mode=\"date\" v-model=\"form.startTimes\" :show=\"startTime\"\r\n                                @close=\"startTime = false\" @cancel=\"startTime = false\" format=\"yyyy-MM-dd\"\r\n                                closeOnClickOverlay @confirm=\"startTimecof\" />\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"endTime\" label=\"活动结束时间\">\r\n                            <view class=\"w-100 u-text-right u-font-30\" @click=\"endTime = true\"\r\n                                :style=\"{ color: form.endTime ? '#333' : '#c0c4cc' }\">\r\n                                {{ form.endTime || '请选择活动结束时间' }}\r\n                            </view>\r\n                            <u-datetime-picker mode=\"date\" v-model=\"form.endTimes\" :show=\"endTime\"\r\n                                @close=\"endTime = false\" @cancel=\"endTime = false\" closeOnClickOverlay\r\n                                @confirm=\"endTimecof\" />\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"sourcePrice\" label=\"原价\"> \r\n                            <u-input inputAlign=\"right\" type=\"digit\" border=\"none\" placeholder=\"请输入原价\"\r\n                                v-model=\"form.sourcePrice\"></u-input>\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"specialPrice\" label=\"特价\">\r\n                            <u-input inputAlign=\"right\" type=\"digit\" border=\"none\" placeholder=\"请输入特价\"\r\n                                v-model=\"form.specialPrice\" @blur=\"checkSpecialPrice\"></u-input>\r\n                        </u-form-item>\r\n                        <!-- <u-form-item required borderBottom label=\"场馆\">\r\n                            <u-input inputAlign=\"right\" type=\"number\" disabled border=\"none\" placeholder=\"请输入场馆\"\r\n                                v-model=\"form.shopname\"></u-input>\r\n                        </u-form-item> -->\r\n                        <u-form-item required borderBottom prop=\"type\" label=\"自动成团类型\">\r\n                            <view class=\"w-100 u-text-right u-font-30\" @click=\"type = true\"\r\n                                :style=\"{ color: form.type ? '#333' : '#c0c4cc' }\">\r\n                                {{ form.typename || '请选择自动成团类型' }}\r\n                            </view>\r\n                            <u-picker :show=\"type\" :columns=\"columns\" v-model=\"form.type\" keyName=\"label\"\r\n                                @cancel=\"type = false\" @confirm=\"confirmCoach\"></u-picker>\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"remark\" label=\"描述\">\r\n                            <u-input inputAlign=\"right\"  border=\"none\" placeholder=\"请输入描述\"\r\n                                v-model=\"form.remark\"></u-input>\r\n                        </u-form-item>\r\n                        <!-- <u-form-item required borderBottom prop=\"ruleContent\" label=\"活动规则\">\r\n                            <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入活动规则\"\r\n                                v-model=\"form.ruleContent\"></u-input>\r\n                        </u-form-item> -->\r\n                        <u-form-item borderBottom label=\"拼团礼包\">\r\n                            <zqs-select :multiple=\"true\" :list=\"actionsMember\" label-key=\"cardName\"\r\n                                value-key=\"memberCardId\" placeholder=\" 请选择拼团礼包\" title=\"请选择拼团礼包\" clearable\r\n                                v-model=\"checkUserList\" @search=\"searchEvent\" @change=\"selectChange2\"></zqs-select>\r\n\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"numMax\" label=\"最大参与人数\">\r\n                            <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入最大参与人数\"\r\n                                v-model=\"form.numMax\"></u-input>\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom label=\"成团人数\">\r\n                            <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入成团人数\"\r\n                                v-model=\"form.ctNum\"></u-input>\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"rebateOne\" label=\"一级奖励\">\r\n                            <view class=\"hours\">\r\n                            <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入一级奖励\"\r\n                                v-model=\"form.rebateOne\"></u-input>\r\n                            <text>元</text>\r\n                        </view>\r\n\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"rebateTwo\" label=\"二级奖励\">\r\n                            <view class=\"hours\">\r\n                            <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入二级奖励\"\r\n                                v-model=\"form.rebateTwo\"></u-input>\r\n                            <text>元</text>\r\n                        </view>\r\n\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"rebateThree\" label=\"三级奖励\">\r\n                            <view class=\"hours\">\r\n                                <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入三级奖励\"\r\n                                    v-model=\"form.rebateThree\"></u-input>\r\n                                <text>元</text>\r\n                            </view>\r\n                        </u-form-item>\r\n                        <u-form-item borderBottom label=\"共享场馆\">\r\n                            <zqs-select :multiple=\"true\" :list=\"Shoplist\" label-key=\"shopName\" value-key=\"shopId\"\r\n                                placeholder=\" 请选择共享场馆\" title=\"请选择共享场馆\" clearable v-model=\"checkUserList1\"\r\n                                @search=\"searchEvent\" @change=\"selectChange2\"></zqs-select>\r\n                        </u-form-item>\r\n                        <u-form-item required borderBottom prop=\"ptHour\" label=\"拼团时间\">\r\n                            <!-- <view class=\"w-100 u-text-right u-font-30\" @click=\"ptHour = true\"\r\n                                :style=\"{ color: form.ptHour ? '#333' : '#c0c4cc' }\">\r\n                                {{ form.ptHour || '请选择拼团时间' }}\r\n                            </view> -->\r\n                            <!-- <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入拼团时间\"\r\n                                v-model=\"form.rebateThree\"></u-input> -->\r\n                            <view class=\"hours\">\r\n                                <u-input inputAlign=\"right\" type=\"number\" border=\"none\" placeholder=\"请输入拼团时间\"\r\n                                    v-model=\"form.ptHour\" @input=\"validateInput\"></u-input>\r\n                                <text>小时</text>\r\n                            </view>\r\n                            <!-- <u-picker :show=\"ptHour\" :columns=\"hourlist\" v-model=\"form.ptHour\" keyName=\"label\"\r\n                                @cancel=\"ptHour = false\" @confirm=\"confirmptptHour\"></u-picker> -->\r\n                        </u-form-item>\r\n                    </view>\r\n                    <view class=\"u-m-b-34 u-p-34 border-16 bg-fff\">\r\n                        <u-form-item required prop=\"background\" labelPosition=\"top\" label=\"活动背景图\">\r\n                            <view class=\"upload-img-container\">\r\n                                <view @click.stop=\"chooseBackground\" class=\"upload-img-box\">\r\n                                    <u-icon v-if=\"!form.background\" name=\"camera\" size=\"40\" color=\"#ddd\"></u-icon>\r\n                                    <view class=\"u-relative w-100 h-100\" v-else>\r\n                                        <image :src=\"form.background\" mode=\"widthFix\" class=\"w-100\"\r\n                                            @click.stop=\"previewBackground\" />\r\n                                        <view class=\"u-absolute u-p-10\" v-show=\"form.background\"\r\n                                            @click.stop=\"delBackground\"\r\n                                            style=\"border-radius: 0 0 0 16rpx; right: 0; top: 0; background: #dd524d\">\r\n                                            <u-icon name=\"close\" color=\"#fff\" size=\"13\"></u-icon>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                        </u-form-item>\r\n                    </view>\r\n                    <view class=\"u-m-b-34 u-p-34 border-16 bg-fff\">\r\n                        <u-form-item required prop=\"background\" labelPosition=\"top\" label=\"活动海报图\">\r\n                            <view class=\"upload-img-container\">\r\n                                <view @click.stop=\"chooseposter\" class=\"upload-img-box\">\r\n                                    <u-icon v-if=\"!form.poster\" name=\"camera\" size=\"40\" color=\"#ddd\"></u-icon>\r\n                                    <view class=\"u-relative w-100 h-100\" v-else>\r\n                                        <image :src=\"form.poster\" mode=\"widthFix\" class=\"w-100\"\r\n                                            @click.stop=\"previewposter\" />\r\n                                        <view class=\"u-absolute u-p-10\" v-show=\"form.poster\" @click.stop=\"delposter\"\r\n                                            style=\"border-radius: 0 0 0 16rpx; right: 0; top: 0; background: #dd524d\">\r\n                                            <u-icon name=\"close\" color=\"#fff\" size=\"13\"></u-icon>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                        </u-form-item>\r\n                    </view>\r\n                    <view class=\"u-m-b-34 u-p-34 border-16 bg-fff\">\r\n                        <u-form-item prop=\"banner\" labelPosition=\"top\" label=\"活动信息图\">\r\n                            <view class=\"upload-img-container\">\r\n                                <view v-for=\"(item, index) in form.infoPic\" :key=\"index\" class=\"upload-img-box\">\r\n                                    <view class=\"u-relative w-100 h-100\">\r\n                                        <image :src=\"item\" mode=\"widthFix\" class=\"w-100\" @click.stop=\"previewBanner\" />\r\n                                        <view class=\"u-absolute u-p-10\" @click.stop=\"delBanner(index)\"\r\n                                            style=\"border-radius: 0 0 0 16rpx; right: 0; top: 0; background: #dd524d\">\r\n                                            <u-icon name=\"close\" color=\"#fff\" size=\"13\"></u-icon>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                                <view @click.stop=\"chooseBanner\" class=\"upload-img-box\">\r\n                                    <u-icon name=\"camera\" size=\"40\" color=\"#ddd\"></u-icon>\r\n                                </view>\r\n                            </view>\r\n                        </u-form-item>\r\n                    </view>\r\n                    <!-- <u-form-item required borderBottom prop=\"remainder\" label=\"剩余人数\">\r\n                <u-input\r\n                  inputAlign=\"right\"\r\n                  type=\"number\"\r\n                  border=\"none\"\r\n                  placeholder=\"请输入剩余人数\"\r\n                  v-model=\"form.remainder\"\r\n                ></u-input>\r\n              </u-form-item> -->\r\n                </u-form>\r\n            </view>\r\n            <view class=\"bottom-blk bg-fff w-100 u-p-40\">\r\n                <u-button :color=\"buttonLightBgColor\" shape=\"circle\" :loading=\"disabled\" @click=\"createGroupCourse\"\r\n                    :customStyle=\"{ fontWeight: 'bold', fontSize: '36rpx' }\">\r\n                    {{ typetext == 'edit' ? '保存' : '创建' }}\r\n                </u-button>\r\n            </view>\r\n        </template>\r\n    </themeWrap>\r\n</template>\r\n<script>\r\nimport api from '@/common/api'\r\nimport zqsSelect from '@/components/zqs-select/zqs-select.vue'\r\nexport default {\r\n    components: { zqsSelect },\r\n    data() {\r\n        return {\r\n            hourlist: [\r\n                [{\r\n                    label: '00',\r\n                    id: 1\r\n                }, {\r\n                    label: '01',\r\n                    id: 2\r\n                }, {\r\n                    label: '02',\r\n                    id: 2\r\n                }, {\r\n                    label: '03',\r\n                    id: 2\r\n                }, {\r\n                    label: '04',\r\n                    id: 2\r\n                }, {\r\n                    label: '05',\r\n                    id: 2\r\n                }, {\r\n                    label: '06',\r\n                    id: 2\r\n                }, {\r\n                    label: '07',\r\n                    id: 2\r\n                }, {\r\n                    label: '08',\r\n                    id: 2\r\n                }, {\r\n                    label: '09',\r\n                    id: 2\r\n                }, {\r\n                    label: '10',\r\n                    id: 2\r\n                }, {\r\n                    label: '11',\r\n                    id: 11\r\n                }, {\r\n                    label: '12',\r\n                    id: 12\r\n                }, {\r\n                    label: '13',\r\n                    id: 13\r\n                }, {\r\n                    label: '14',\r\n                    id: 14\r\n                }, {\r\n                    label: '15',\r\n                    id: 15\r\n                }, {\r\n                    label: '16',\r\n                    id: 16\r\n                }, {\r\n                    label: '17',\r\n                    id: 17\r\n                }, {\r\n                    label: '18',\r\n                    id: 18\r\n                }, {\r\n                    label: '19',\r\n                    id: 19\r\n                }, {\r\n                    label: '20',\r\n                    id: 20\r\n                }, {\r\n                    label: '21',\r\n                    id: 21\r\n                }, {\r\n                    label: '22',\r\n                    id: 22\r\n                }, {\r\n                    label: '23',\r\n                    id: 23\r\n                }]\r\n            ],\r\n            checkUserList: [],\r\n            checkUserList1: [],\r\n            importUserId: [],\r\n            value: '',\r\n            shareShopIds: false,\r\n            Shoplist: [],\r\n            ShopIds: [],\r\n            selectedItems: [],\r\n            ptRule: false,\r\n            endTime: false,\r\n            startTime: false,\r\n            ptHour: false,\r\n            columns: [\r\n                [{\r\n                    label: '未拼成退款',\r\n                    id: 1\r\n                }, {\r\n                    label: '自动免拼',\r\n                    id: 2\r\n                }]\r\n            ],\r\n            id: '',\r\n            timePicker: false,\r\n            timePickera: false,\r\n            disabled: '',\r\n            form: {\r\n                ctNum: 2,\r\n                remark: '',\r\n                poster: '',\r\n                typename: '',\r\n                name: '',\r\n                startTime: '',\r\n                startTimes: '',\r\n                endTime: '',\r\n                endTimes: '',\r\n                sourcePrice: '',\r\n                specialPrice: '',\r\n                shopId: '',\r\n                type: '',\r\n                ruleContent: '',\r\n                ptRule: '',\r\n                background: '',\r\n                infoPic: [],\r\n                numMax: '',\r\n                poster: '',\r\n                rebateOne: '',\r\n                rebateTwo: '',\r\n                rebateThree: '',\r\n                shareShopIds: '',\r\n                ptHour: '',\r\n                shopname: ''\r\n            },\r\n            classTimeListName: '',\r\n            rules: {\r\n                name: [\r\n                    {\r\n                        required: true,\r\n                        message: '请输入活动名称',\r\n                        trigger: 'blur',\r\n                    },\r\n                ],\r\n                sourcePrice: [\r\n                    {\r\n                        required: true,\r\n                        message: '请输入原价',\r\n                        trigger: 'blur',\r\n                    },\r\n                ],\r\n                specialPrice: [\r\n                    {\r\n                        required: true,\r\n                        message: '请输入特价',\r\n                        trigger: 'blur',\r\n                    },\r\n                ],\r\n                rebateOne: [\r\n                    {\r\n                        required: true,\r\n                        message: '请输入一级奖励',\r\n                        trigger: 'blur',\r\n                    },\r\n                ],\r\n            },\r\n            user: {},\r\n            minDate: Date.now(),\r\n            detail: {},\r\n            showCoach: false,\r\n            showCalendar: false,\r\n            type: false,\r\n            typetext: '',\r\n            actionsMember: []\r\n        }\r\n    },\r\n    watch: {\r\n        value2(val) {\r\n            console.log('我是更新后的选中数据', val)\r\n        },\r\n    },\r\n    onReady() {\r\n        this.$refs.uForm.setRules(this.rules)\r\n    },\r\n    async onLoad(option) {\r\n        this.user = uni.getStorageSync('userInfo')\r\n        this.form.shopname = uni.getStorageSync('nowShopName')\r\n        this.form.shopId = uni.getStorageSync('nowShopId')\r\n        this.id = option.id\r\n       \r\n        if (option.list && option.type == 'edit') {\r\n            Object.keys(JSON.parse(option.list)).forEach(key => {\r\n                if (this.form[key] !== undefined) {\r\n                    this.form[key] = JSON.parse(option.list)[key];\r\n                }\r\n            });\r\n            this.form.infoPic = JSON.parse(option.list).infoPic.split(',')\r\n            console.log(this.columns, this.columns[0].filter(item => item.id == (JSON.parse(option.list).type)), JSON.parse(option.list).type)\r\n            this.form.typename = this.columns[0].filter(item => item.id == (JSON.parse(option.list).type))[0].label\r\n            // this.form.ptHour = this.hourlist[0].filter(item => item.label == (JSON.parse(option.list).ptHour))[0].label\r\n            this.form.sourcePrice = String(JSON.parse(option.list).sourcePrice)\r\n            this.form.specialPrice = String(JSON.parse(option.list).specialPrice)\r\n            this.form.rebateOne = String(JSON.parse(option.list).rebateOne)\r\n            this.form.id = String(JSON.parse(option.list).id)\r\n            this.form.ctNum = JSON.parse(option.list).ctNum ? JSON.parse(option.list).ctNum : 2\r\n            let actionsMembers = []\r\n            JSON.parse(option.list).ptRule.memberCards.forEach(res => {\r\n                actionsMembers.push(Number(res.memberCardId))\r\n            })\r\n            this.checkUserList = actionsMembers\r\n            JSON.parse(option.list).shareShopIds.split(',')\r\n            this.checkUserList1 = JSON.parse(option.list).shareShopIds.split(',').map(num => Number(num))\r\n            this.typetext = option.type\r\n            console.log(this.form, this.typetext, this.checkUserList1)\r\n        }\r\n        api.getUserCardList({\r\n            shopId: uni.getStorageSync(\"nowShopId\"),\r\n            data: {\r\n                coachId: '',\r\n                pageNum: 1,\r\n                pageSize: 999\r\n            }\r\n        }).then(res => {\r\n            this.actionsMember = res.rows\r\n        })\r\n        console.log(this.actionsMember)\r\n        let longitude = uni.getStorageSync(\"longitude\", this.x);\r\n        let latitude = uni.getStorageSync(\"latitude\", this.y);\r\n        let rslt = await api.getShopListForDistance({\r\n            data: {\r\n                distance: 100000,\r\n                companyId: 1,\r\n                longitude: longitude, // 经度\r\n                latitude: latitude, // 维度\r\n            },\r\n        })\r\n        console.log(rslt, 'rsltrslt')\r\n        this.Shoplist = rslt.rows\r\n        console.log(this.Shoplist)\r\n    },\r\n    methods: {\r\n        validateInput() {\r\n            // 获取输入的值\r\n            let value = this.form.ptHour;\r\n            // 如果值为空或不是数字，直接退出\r\n            if (value === '' || isNaN(value)) {\r\n                return;\r\n            }\r\n            // 转换为数字进行处理\r\n            value = parseFloat(value);\r\n            // 限制输入范围，确保在 1 到 23 之间\r\n            if (value <= 0) {\r\n                this.form.ptHour = 1;  // 最小值为 1\r\n            } else if (value >= 25) {\r\n                this.form.ptHour = 24; // 最大值为 23\r\n            } else {\r\n                this.form.ptHour = value;\r\n            }\r\n        },\r\n        checkSpecialPrice() {\r\n            const sourcePrice = parseFloat(this.form.sourcePrice);\r\n            const specialPrice = parseFloat(this.form.specialPrice);\r\n            // 判断特价是否大于原价\r\n            if (specialPrice > sourcePrice) {\r\n                this.$u.toast('特价不能大于原价');  // 提示用户特价不能大于原价\r\n                this.form.specialPrice = '';  // 清空特价输入框\r\n            }\r\n        },\r\n        selectChange2() {\r\n            // 此处为点击的事件\r\n        },\r\n        searchEvent(val) {\r\n            console.log('查询事件参数', val)\r\n            // 此处把新的请求值 赋值给options\r\n        },\r\n        changeHandler(e) {\r\n            console.log(e)\r\n        },\r\n        timechange(time) {\r\n            const date = new Date(time);\r\n            const year = date.getFullYear();\r\n            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，+1 后格式化\r\n            const day = date.getDate().toString().padStart(2, '0');\r\n            const formattedDate = `${year}-${month}-${day}`;\r\n            return formattedDate\r\n        },\r\n        endTimecof(e) {\r\n            if (this.form.endTimes < this.form.startTimes) {\r\n                this.$u.toast('结束时间不能早于开始时间'); // 提示用户结束时间不合法\r\n                this.form.endTimes = ''; // 清空结束时间\r\n                return; // 阻止操作\r\n            }\r\n            this.form.endTime = this.form.endTimes;  // 将选择的结束时间赋值给 form.endTime\r\n            this.form.endTime = this.timechange(e.value)\r\n            this.endTime = false\r\n        },\r\n        startTimecof(e) {\r\n            this.form.startTime = this.timechange(e.value)\r\n            this.startTime = false\r\n        },\r\n        amountValidator(rule, value, callback) {\r\n            if (value < 0.01) {\r\n                callback(new Error('金额不能小于 0.01'));\r\n            } else {\r\n                callback(); // 验证通过\r\n            }\r\n        },\r\n        confirmCalendar(e) {\r\n            console.log(e)\r\n            let temp = []\r\n            for (var i = 0; i < e.length; i++) {\r\n                temp.push(e[i] + ' 00:00:00')\r\n            }\r\n            this.form.classTimeList = temp\r\n            this.showCalendar = false\r\n            if (e.length > 0) {\r\n                this.classTimeListName = e.toString()\r\n            }\r\n        },\r\n        getCoach() {\r\n            api.getcoList().then((res) => {\r\n                this.columnsCoach = [res.rows]\r\n            })\r\n        },\r\n        confirmCoach(e) {\r\n            this.form.type = e.value[0].id\r\n            this.form.typename = e.value[0].label\r\n            this.type = false\r\n        },\r\n        confirmptRule(e) {\r\n            // this.form.ptRule = e.\r\n            this.ptRule = false\r\n\r\n        },\r\n        confirmptptHour(e) {\r\n            // this.form.ptRule = e.\r\n            this.form.ptHour = e.value[0].label\r\n            this.ptHour = false\r\n\r\n        },\r\n\r\n        previewBanner() {\r\n            uni.previewImage({\r\n                urls: this.form.infoPic,\r\n                longPressActions: {\r\n                    success: function (data) { },\r\n                    fail: function (err) { },\r\n                },\r\n            })\r\n        },\r\n        chooseBanner() {\r\n            let token = uni.getStorageSync('token')\r\n            uni.chooseImage({\r\n                count: 9, //默认9\r\n                sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n                sourceType: ['album'], //从相册选择\r\n                success: (res) => {\r\n                    uni.showLoading({\r\n                        mask: true,\r\n                        title: '正在上传中……请稍后',\r\n                    })\r\n                    const tempFilePaths = res.tempFilePaths\r\n                    let uploadedCount = 0\r\n                    const uploadNext = () => {\r\n                        if (uploadedCount < tempFilePaths.length) {\r\n                            uni.uploadFile({\r\n                                url: this.$serverUrl + '/common/upload',\r\n                                filePath: tempFilePaths[uploadedCount],\r\n                                name: 'file',\r\n                                header: {\r\n                                    Authorization: token,\r\n                                },\r\n                                success: (succ) => {\r\n                                    this.form.infoPic.push(JSON.parse(succ.data).url)\r\n                                },\r\n                                fail: (err) => {\r\n                                    console.log(err)\r\n                                },\r\n                                complete() {\r\n                                    uploadedCount++\r\n                                    uploadNext()\r\n                                },\r\n                            })\r\n                        } else {\r\n                            uni.hideLoading()\r\n                        }\r\n                    }\r\n                    uploadNext()\r\n                },\r\n            })\r\n        },\r\n        previewBackground() {\r\n            uni.previewImage({\r\n                urls: [this.form.background],\r\n                longPressActions: {\r\n                    success: function (data) { },\r\n                    fail: function (err) { },\r\n                },\r\n            })\r\n        },\r\n        previewposter() {\r\n            uni.previewImage({\r\n                urls: [this.form.poster],\r\n                longPressActions: {\r\n                    success: function (data) { },\r\n                    fail: function (err) { },\r\n                },\r\n            })\r\n        },\r\n        chooseBackground() {\r\n            let token = uni.getStorageSync('token')\r\n            uni.chooseImage({\r\n                count: 1, //默认9\r\n                sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n                sourceType: ['album'], //从相册选择\r\n                success: (res) => {\r\n                    uni.showLoading({\r\n                        mask: true,\r\n                        title: '正在上传中……请稍后',\r\n                    })\r\n                    const tempFilePaths = res.tempFilePaths\r\n                    uni.uploadFile({\r\n                        url: this.$serverUrl + '/common/upload',\r\n                        filePath: tempFilePaths[0],\r\n                        name: 'file',\r\n                        header: {\r\n                            Authorization: token,\r\n                        },\r\n                        success: (succ) => {\r\n                            this.form.background = JSON.parse(succ.data).url\r\n                        },\r\n                        fail: (err) => {\r\n                            console.log(err)\r\n                        },\r\n                        complete() {\r\n                            uni.hideLoading()\r\n                        },\r\n                    })\r\n                },\r\n            })\r\n        },\r\n        chooseposter() {\r\n            let token = uni.getStorageSync('token')\r\n            uni.chooseImage({\r\n                count: 1, //默认9\r\n                sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n                sourceType: ['album'], //从相册选择\r\n                success: (res) => {\r\n                    uni.showLoading({\r\n                        mask: true,\r\n                        title: '正在上传中……请稍后',\r\n                    })\r\n                    const tempFilePaths = res.tempFilePaths\r\n                    uni.uploadFile({\r\n                        url: this.$serverUrl + '/common/upload',\r\n                        filePath: tempFilePaths[0],\r\n                        name: 'file',\r\n                        header: {\r\n                            Authorization: token,\r\n                        },\r\n                        success: (succ) => {\r\n                            this.form.poster = JSON.parse(succ.data).url\r\n                        },\r\n                        fail: (err) => {\r\n                            console.log(err)\r\n                        },\r\n                        complete() {\r\n                            uni.hideLoading()\r\n                        },\r\n                    })\r\n                },\r\n            })\r\n        },\r\n        previewClassInfoPic() {\r\n            uni.previewImage({\r\n                urls: this.form.classInfoPic,\r\n                longPressActions: {\r\n                    success: function (data) { },\r\n                    fail: function (err) { },\r\n                },\r\n            })\r\n        },\r\n        chooseClassInfoPic() {\r\n            let token = uni.getStorageSync('token')\r\n            uni.chooseImage({\r\n                count: 9, //默认9\r\n                sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n                sourceType: ['album'], //从相册选择\r\n                success: (res) => {\r\n                    uni.showLoading({\r\n                        mask: true,\r\n                        title: '正在上传中……请稍后',\r\n                    })\r\n                    const tempFilePaths = res.tempFilePaths\r\n                    let uploadedCount = 0\r\n                    const uploadNext = () => {\r\n                        if (uploadedCount < tempFilePaths.length) {\r\n                            uni.uploadFile({\r\n                                url: this.$serverUrl + '/common/upload',\r\n                                filePath: tempFilePaths[uploadedCount],\r\n                                name: 'file',\r\n                                header: {\r\n                                    Authorization: token,\r\n                                },\r\n                                success: (succ) => {\r\n                                    this.form.classInfoPic.push(JSON.parse(succ.data).url)\r\n                                },\r\n                                fail: (err) => {\r\n                                    console.log(err)\r\n                                },\r\n                                complete() {\r\n                                    uploadedCount++\r\n                                    uploadNext()\r\n                                },\r\n                            })\r\n                        } else {\r\n                            uni.hideLoading()\r\n                        }\r\n                    }\r\n                    uploadNext()\r\n                },\r\n            })\r\n        },\r\n        changeTime(e) {\r\n            this.form.ptHour = e.value\r\n            this.ptHour = false\r\n        },\r\n        changeTimes(e) {\r\n            console.log(e, 1)\r\n            const date = new Date(e.value);\r\n            const year = date.getFullYear();\r\n            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，+1 后格式化\r\n            const day = date.getDate().toString().padStart(2, '0');\r\n            const hours = date.getHours().toString().padStart(2, '0');\r\n            const minutes = date.getMinutes().toString().padStart(2, '0');\r\n            const seconds = date.getSeconds().toString().padStart(2, '0');\r\n            const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n            this.form.firstClassTime = formattedDate\r\n            this.form.lastClassTime = formattedDate\r\n            console.log(this.form, 1, formattedDate)\r\n            this.timePickera = false\r\n        },\r\n        delBanner(index) {\r\n            this.form.infoPic.splice(index, 1)\r\n        },\r\n        delBackground() {\r\n            this.form.background = ''\r\n        },\r\n        delposter() {\r\n            this.form.poster = ''\r\n        },\r\n        delClassInfoPic(index) {\r\n            this.form.classInfoPic.splice(index, 1)\r\n        },\r\n        createGroupCourse() {\r\n            this.disabled = true\r\n            console.log(this.form)\r\n            this.$refs.uForm\r\n                .validate()\r\n                .then(() => {\r\n                    uni.showModal({\r\n                        title: '确认提交',\r\n                        content: '',\r\n                        success: (res) => {\r\n                            if (res.confirm) {\r\n                                uni.showLoading({\r\n                                    mask: true,\r\n                                })\r\n                                this.submit()\r\n                            } else {\r\n                                this.disabled = false\r\n                                uni.hideLoading()\r\n                            }\r\n                        },\r\n                    })\r\n                })\r\n                .catch(() => {\r\n                    this.disabled = false\r\n                })\r\n        },\r\n        submit() {\r\n            const formParams = JSON.parse(JSON.stringify(this.form))\r\n            formParams.infoPic = !formParams.infoPic ? '' : formParams.infoPic.join(',')\r\n            let ptRule = { memberCards: [] }\r\n            console.log(this.checkUserList)\r\n            this.checkUserList.forEach(value => {\r\n                this.actionsMember.filter(item => item.memberCardId === value)\r\n                    .forEach(item => ptRule.memberCards.push({\r\n                        memberCardId: item.memberCardId,\r\n                        cardType: item.cardType\r\n                    }));\r\n            });\r\n            formParams.shareShopIds = this.checkUserList1.join(',')\r\n            formParams.ptRule = ptRule\r\n            let url = this.typetext == 'edit' ? 'EditActivityPt' : 'addActivityPt'\r\n            api[url]({\r\n                data: {\r\n                    ...formParams,\r\n                },\r\n                method: this.typetext == 'edit' ? 'PUT' : 'POST',\r\n            })\r\n                .then((res) => {\r\n                    console.log(res)\r\n                    if (res.code == 200) {\r\n                        uni.showToast({\r\n                            title: `${this.typetext == 'edit' ? '修改' : '新增'}成功`,\r\n                            icon: 'success',\r\n                            duration: 2000,\r\n                            success: () => { },\r\n                        })\r\n                        setTimeout(() => {\r\n                            uni.navigateBack({\r\n                                delta: 1\r\n                            });\r\n                        }, 2000)\r\n                    } else {\r\n                        uni.showToast({\r\n                            title: res.msg,\r\n                            icon: 'success',\r\n                            duration: 2000,\r\n                            success: () => { },\r\n                        })\r\n                    }\r\n                    this.disabled = false\r\n                })\r\n                .catch((err) => {\r\n                    this.disabled = false\r\n                })\r\n        },\r\n    },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.hours {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\nu-input {\r\n    width: 200px;\r\n}\r\n\r\ntext {\r\n    font-size: 14px;\r\n    color: #888;\r\n}\r\n\r\n.textareaTitle {\r\n    border-radius: 8rpx 8rpx 0 0;\r\n    padding: 10rpx 30rpx;\r\n    background-color: #e6e6e6;\r\n}\r\n\r\n.container ::v-deep {\r\n    min-height: 50vh;\r\n\r\n    .u-form-item__body__right__message {\r\n        text-align: end !important;\r\n    }\r\n}\r\n\r\n.upload-img-container {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.upload-img-box {\r\n    display: inline-flex;\r\n    width: 180rpx;\r\n    height: 180rpx;\r\n    border: 1px solid #ddd;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-top: 20rpx;\r\n    margin-right: 20rpx;\r\n    vertical-align: top;\r\n}\r\n\r\n.main-box {\r\n    display: flex;\r\n    margin: 20rpx;\r\n    justify-content: space-between;\r\n    background: #f7f7f7;\r\n    padding: 30rpx;\r\n}\r\n</style>", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages-admin/huodongGuanli/info.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./info.vue?vue&type=template&id=58d62656&scoped=true&\"\nvar renderjs\nimport script from \"./info.vue?vue&type=script&lang=js&\"\nexport * from \"./info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./info.vue?vue&type=style&index=0&id=58d62656&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"58d62656\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages-admin/huodongGuanli/info.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=style&index=0&id=58d62656&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=style&index=0&id=58d62656&scoped=true&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=template&id=58d62656&scoped=true&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_regeneratorRuntime", "components", "zqsSelect", "data", "hourlist", "label", "id", "checkUserList", "checkUserList1", "importUserId", "value", "shareShopIds", "Shoplist", "ShopIds", "selectedItems", "ptRule", "endTime", "startTime", "ptHour", "columns", "timePicker", "timePickera", "disabled", "form", "ctNum", "remark", "poster", "typename", "name", "startTimes", "endTimes", "sourcePrice", "specialPrice", "shopId", "type", "ruleContent", "background", "infoPic", "numMax", "classTimeListName", "rules", "required", "message", "trigger", "rebateOne", "user", "minDate", "Date", "now", "detail", "showCoach", "showCalendar", "typetext", "actionsMember", "watch", "value2", "val", "console", "log", "onReady", "$refs", "uForm", "setRules", "onLoad", "option", "_this", "_asyncToGenerator", "mark", "_callee", "actionsMembers", "longitude", "latitude", "rslt", "wrap", "_callee$", "_context", "prev", "next", "uni", "getStorageSync", "shopname", "list", "JSON", "parse", "key", "undefined", "split", "item", "String", "memberCards", "res", "Number", "memberCardId", "map", "num", "api", "getUserCardList", "coachId", "pageNum", "pageSize", "then", "rows", "x", "y", "getShopListForDistance", "distance", "companyId", "sent", "stop", "methods", "validateInput", "isNaN", "parseFloat", "checkSpecialPrice", "$u", "toast", "selectChange2", "searchEvent", "<PERSON><PERSON><PERSON><PERSON>", "timechange", "time", "date", "year", "getFullYear", "month", "getMonth", "toString", "padStart", "day", "getDate", "formattedDate", "concat", "endTimecof", "startTimecof", "amountValidator", "rule", "callback", "Error", "confirmCalendar", "temp", "i", "classTimeList", "getCoach", "_this2", "getcoList", "columnsCoach", "confirmCoach", "confirmptRule", "confirmptptHour", "previewBanner", "previewImage", "urls", "longPressActions", "success", "fail", "err", "chooseBanner", "_this3", "token", "chooseImage", "count", "sizeType", "sourceType", "showLoading", "mask", "title", "tempFilePaths", "uploadedCount", "uploadNext", "uploadFile", "url", "$serverUrl", "filePath", "header", "Authorization", "succ", "complete", "hideLoading", "previewBackground", "previewposter", "chooseBackground", "_this4", "chooseposter", "_this5", "previewClassInfoPic", "classInfoPic", "chooseClassInfoPic", "_this6", "changeTime", "changeTimes", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "firstClassTime", "lastClassTime", "delBanner", "index", "splice", "delBackground", "del<PERSON>er", "delClassInfoPic", "createGroupCourse", "_this7", "validate", "showModal", "content", "confirm", "submit", "catch", "_this8", "formParams", "stringify", "join", "cardType", "method", "code", "showToast", "icon", "duration", "setTimeout", "navigateBack", "delta", "msg", "_vue", "_info", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}