"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/changGuan/nav-bar"],{9588:function(e,n,o){var u=o(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;n["default"]={props:{buttonBgColor:{type:String,default:"#fff"},buttonTextColor:{type:String,default:"#000"}},methods:{goBack:function(){u.navigateBack()}}}},35605:function(e,n,o){o.r(n),o.d(n,{__esModule:function(){return c.__esModule},default:function(){return d}});var u,t={uNavbar:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(o.bind(o,66372))},uIcon:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(o.bind(o,78278))},uLine:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-line/u-line")]).then(o.bind(o,84916))}},a=function(){var e=this,n=e.$createElement;e._self._c},l=[],c=o(9588),i=c["default"],r=o(18535),s=(0,r["default"])(i,a,l,!1,null,null,null,!1,t,u),d=s.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/changGuan/nav-bar-create-component"],{},function(e){e("81715")["createComponent"](e(35605))}]);