(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-popup/u-popup"],{43673:function(e,t,o){"use strict";var n=o(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var i=u(o(86496));function u(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-popup",mixins:[n.$u.mpMixin,n.$u.mixin,i.default],data:function(){return{overlayDuration:Number(this.duration)+50}},watch:{show:function(e,t){if(!0===e){var o=this.$children;this.retryComputedComponentRect(o)}}},computed:{transitionStyle:function(){var e={zIndex:this.zIndex,position:"fixed",display:"flex"};return e[this.mode]=0,"left"===this.mode||"right"===this.mode?n.$u.deepMerge(e,{bottom:0,top:0}):"top"===this.mode||"bottom"===this.mode?n.$u.deepMerge(e,{left:0,right:0}):"center"===this.mode?n.$u.deepMerge(e,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0}):void 0},contentStyle:function(){var e={},t=n.$u.sys();t.safeAreaInsets;if("center"!==this.mode&&(e.flex=1),this.bgColor&&(e.backgroundColor=this.bgColor),this.round){var o=n.$u.addUnit(this.round);"top"===this.mode?(e.borderBottomLeftRadius=o,e.borderBottomRightRadius=o):"bottom"===this.mode?(e.borderTopLeftRadius=o,e.borderTopRightRadius=o):"center"===this.mode&&(e.borderRadius=o)}return n.$u.deepMerge(e,n.$u.addStyle(this.customStyle))},position:function(){return"center"===this.mode?this.zoom?"fade-zoom":"fade":"left"===this.mode?"slide-left":"right"===this.mode?"slide-right":"bottom"===this.mode?"slide-up":"top"===this.mode?"slide-down":void 0}},methods:{overlayClick:function(){this.closeOnClickOverlay&&this.$emit("close")},close:function(e){this.$emit("close")},afterEnter:function(){this.$emit("open")},clickHandler:function(){"center"===this.mode&&this.overlayClick(),this.$emit("click")},retryComputedComponentRect:function(e){for(var t=this,o=["u-calendar-month","u-album","u-collapse-item","u-dropdown","u-index-item","u-index-list","u-line-progress","u-list-item","u-rate","u-read-more","u-row","u-row-notice","u-scroll-list","u-skeleton","u-slider","u-steps-item","u-sticky","u-subsection","u-swipe-action-item","u-tabbar","u-tabs","u-tooltip"],i=function(){var i=e[u],s=i.$children;o.includes(i.$options.name)&&"function"===typeof(null===i||void 0===i?void 0:i.init)&&n.$u.sleep(50).then((function(){i.init()})),s.length&&t.retryComputedComponentRect(s)},u=0;u<e.length;u++)i()}}}},68600:function(){},85432:function(e,t,o){"use strict";o.r(t),o.d(t,{__esModule:function(){return r.__esModule},default:function(){return p}});var n,i={uOverlay:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-overlay/u-overlay")]).then(o.bind(o,15550))},uTransition:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-transition/u-transition")]).then(o.bind(o,68270))},uStatusBar:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-status-bar/u-status-bar")]).then(o.bind(o,51401))},uIcon:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(o.bind(o,78278))},uSafeBottom:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom")]).then(o.bind(o,26397))}},u=function(){var e=this,t=e.$createElement,o=(e._self._c,e.__get_style([e.contentStyle]));e.$mp.data=Object.assign({},{$root:{s0:o}})},s=[],r=o(43673),d=r["default"],l=o(68600),c=o.n(l),a=(c(),o(18535)),m=(0,a["default"])(d,u,s,!1,null,"bc826622",null,!1,i,n),p=m.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-popup/u-popup-create-component"],{},function(e){e("81715")["createComponent"](e(85432))}]);