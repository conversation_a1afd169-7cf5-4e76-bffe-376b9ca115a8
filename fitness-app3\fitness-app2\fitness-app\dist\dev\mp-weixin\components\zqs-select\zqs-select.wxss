/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/zqs-select/zqs-select.vue?vue&type=style&index=0&lang=css& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/

@font-face {
  font-family: 'selectIcon';
  src: url('//at.alicdn.com/t/font_1833441_ycfzdhg2u3.eot?t=1590375117208');
  /* IE9 */
  src: url('//at.alicdn.com/t/font_1833441_ycfzdhg2u3.eot?t=1590375117208#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */
      url(data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAMEAAsAAAAABvQAAAK4AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgqBRIFCATYCJAMMCwgABCAFhQUHNRsfBsg+QCa3uoO0oAJTMwhxVu965keqWBy1hkbwtfzWb2Z279/shRhJisKF6FApKLI7oyBbpAaHo3w24k+ca9EUJbDmjaeznUdZ/FOUlkWdJ33rizZY/Pw6J5Xw0qKYxHTMesePHVT6EFpaC4zV70sKi2bYgNPc1w0WHnDVC/e/UnNTgyP+4Jq6BBpIHoisgypLaIAFEtU0wgeaIG8Yu4nAIZwnUK1QgFfOT6nUUoBpgXjj2lqplTMpiuXtCW3N2iK+aPTS2/Qdnzny8d+5IEiaDMy99exklra//FrKnX48pChmgrq5QcYRQCEe17ruqgqLAKv8WntwqwhpLms/nB5yW/iHRxJEC0QOgT3NnfgF01NBKvOuIzNoZdh5gJuAeGrsozE8vOJ7u5D832oz55039W5G+S52K0H+zNf1TJz07k26kqoQybRfwVFV4rjDS/K8EXUyuF1cXnT3weKS9Rvdm/xe7h8oA1hLwOR18R+Y4n4zwpr4z5SU089Vc+cpfWL+mn5APmT3Z39jeOs/GbWjK+DnmsuL/u6ehMX4j4yedSVkAUUuPh3TY022MtKZUEOtPqCb8Bkvnr5XT6imU0gGrEJW7aAL/gw0OhegVV2F6pC7uTOppirKIA4MFQhTrpCM+AbZlDu64L/QmAkQWlMhQXU75D07O9Gtl0PUYjTBLyAzOLNQYtypIEEjvsXtBLQTooV2nrQrGEau2gKmZlR4L8gwnGtBJbUn1diCOOQUnEkTkRAOeci9KHOQxvFro+tx3ZcGAaeljstCSBNDJuArgIyBYyy6OdZxAhHIELu1IC9AtgShCVtLltEKrSff1XoHJo3RC33hM63o3j6pSNkmqmIWEAtxFHB2OwoRBAfyeqE3r2ogHeF42dBhs7gvf7CukH5MmlUGOCpHihxFfs6TehDyKCqVAA==)
      format('woff2'),
    url('//at.alicdn.com/t/font_1833441_ycfzdhg2u3.woff?t=1590375117208')
      format('woff'),
    url('//at.alicdn.com/t/font_1833441_ycfzdhg2u3.ttf?t=1590375117208')
      format('truetype'),
    /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
      url('//at.alicdn.com/t/font_1833441_ycfzdhg2u3.svg?t=1590375117208#selectIcon')
      format('svg');
  /* iOS 4.1- */
}
.title-main {
  display: flex;
  justify-content: center;
  width: 100%;
}
.title-detail {
  display: flex;
  width: 88%;
  justify-content: center;
  padding: 30rpx 0;
  /*  border-bottom: 1rpx solid #e6e1e1; */
}
.selectIcon {
  font-family: 'selectIcon' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icongou:before {
  content: '\e61c';
}
.iconcross:before {
  content: '\e61a';
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/zqs-select/zqs-select.vue?vue&type=style&index=1&id=2a4bdcb8&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-2a4bdcb8, .theme-color.u-content-color.data-v-2a4bdcb8 {
  color: var(--base-color);
}
.theme-bg-color.data-v-2a4bdcb8, .bgc.data-v-2a4bdcb8 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-2a4bdcb8, .nbc.data-v-2a4bdcb8 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-2a4bdcb8, .bc.data-v-2a4bdcb8 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-2a4bdcb8, .lbc.data-v-2a4bdcb8 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-2a4bdcb8, .btc.data-v-2a4bdcb8 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-2a4bdcb8, .ltc.data-v-2a4bdcb8 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-2a4bdcb8 {
  background: var(--scroll-item-bg-color);
}
.main.data-v-2a4bdcb8 {
  font-size: 28rpx;
}
.bg-white.data-v-2a4bdcb8 {
  background-color: #ffffff;
}
.input.data-v-2a4bdcb8 {
  display: flex;
  align-items: center;
}
.input input.data-v-2a4bdcb8 {
  flex: 1;
  text-align: right;
  color: #333333;
}
.input .selector-icon.data-v-2a4bdcb8 {
  width: 32rpx;
  height: 36rpx;
  vertical-align: middle;
}
.select-modal.data-v-2a4bdcb8 {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  opacity: 0;
  outline: 0;
  text-align: center;
  -webkit-transform: scale(1.185);
          transform: scale(1.185);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-perspective: 2000rpx;
          perspective: 2000rpx;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
  margin-bottom: -1000rpx;
}
.select-modal.data-v-2a4bdcb8::before {
  content: "​";
  display: inline-block;
  height: 100%;
  vertical-align: bottom;
}
.select-modal .select-dialog.data-v-2a4bdcb8 {
  position: absolute;
  left: 0;
  bottom: 0;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
  background-color: #f8f8f8;
  overflow: hidden;
  width: 100%;
  border-radius: 0;
}
.select-modal .select-dialog .select-content.data-v-2a4bdcb8 {
  height: 60vh;
  overflow: auto;
}
.select-modal .select-dialog .select-content .select-item.data-v-2a4bdcb8 {
  text-align: left;
  padding: 20rpx 80rpx;
  display: flex;
}
.select-modal .select-dialog .select-content .select-item.data-v-2a4bdcb8 ::after {
  content: "";
  width: 100%;
  height: 1px;
  display: block;
  margin: 0 auto;
  border-bottom: 2px solid #f5f2f2;
  padding: 1px;
}
.select-modal .select-dialog .select-content .select-item .title.data-v-2a4bdcb8 {
  flex: 1;
}
.select-modal.show.data-v-2a4bdcb8 {
  opacity: 1;
  transition-duration: 0.3s;
  -webkit-transform: scale(1);
          transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
  margin-bottom: 0;
}
.select-bar.data-v-2a4bdcb8 {
  padding: 0 80rpx;
  display: flex;
  position: relative;
  align-items: center;
  min-height: 80rpx;
  justify-content: space-between;
  margin-bottom: 50rpx;
}
.select-bar .action.data-v-2a4bdcb8 {
  display: flex;
  align-items: center;
  height: 78rpx;
  justify-content: center;
  max-width: 100%;
  padding: 0 100rpx;
}
.search-box.data-v-2a4bdcb8 {
  display: flex;
  margin: 10rpx 0;
  align-items: center;
  padding: 10rpx 40rpx;
}
.search-input.data-v-2a4bdcb8 {
  display: flex;
  flex: 1;
  height: 67rpx;
  line-height: 67rpx;
  border-radius: 40rpx;
  background: #f5f2f2;
}
.search-text.data-v-2a4bdcb8 {
  padding-left: 30rpx;
}
