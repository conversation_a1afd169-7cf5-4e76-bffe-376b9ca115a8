<theme-wrap scoped-slots-compiler="augmented" vue-id="68f8959e-1" class="data-v-1247023a" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-1247023a" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('68f8959e-2')+','+('68f8959e-1')}}" title="企业缴费查询" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-1247023a" bind:__l="__l"></u-navbar><view class="searchView data-v-1247023a" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('68f8959e-3')+','+('68f8959e-1')}}" showAction="{{false}}" placeholder="请输入企业名称" animation="{{true}}" data-event-opts="{{[['^search',[['search']]]]}}" bind:search="__e" class="data-v-1247023a" bind:__l="__l"></u-search></view><u-subsection vue-id="{{('68f8959e-4')+','+('68f8959e-1')}}" list="{{list}}" activeColor="{{$root.m4['buttonLightBgColor']}}" current="{{curNow}}" data-event-opts="{{[['^change',[['sectionChange']]]]}}" bind:change="__e" class="data-v-1247023a" bind:__l="__l"></u-subsection><block wx:if="{{curNow==0}}"><view class="u-p-l-20 u-p-r-20 data-v-1247023a"><view class="u-p-t-20 u-p-b-20 u-border-bottom u-flex data-v-1247023a"><view class="u-flex-1 data-v-1247023a"><view class="title data-v-1247023a">小毛睿有线公司柯桥绍兴分公司企业下面的一个公司小毛睿有线公司柯桥绍兴分公司企业下面的一个公司</view><view class="value u-flex u-m-t-20 data-v-1247023a"><view class="u-flex-1 data-v-1247023a">最后缴费时间：2023-10-12</view><view class="data-v-1247023a">缴费人：小毛猫</view></view></view><view data-event-opts="{{[['tap',[['gotoDetails']]]]}}" class="u-m-l-20 data-v-1247023a" bindtap="__e"><u-icon vue-id="{{('68f8959e-5')+','+('68f8959e-1')}}" name="arrow-right" class="data-v-1247023a" bind:__l="__l"></u-icon></view></view></view></block></view></theme-wrap>