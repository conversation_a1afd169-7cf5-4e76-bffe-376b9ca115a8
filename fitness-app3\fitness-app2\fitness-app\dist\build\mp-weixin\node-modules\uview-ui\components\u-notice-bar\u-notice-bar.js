(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-notice-bar/u-notice-bar"],{19555:function(e,n,o){"use strict";o.r(n),o.d(n,{__esModule:function(){return l.__esModule},default:function(){return f}});var t,u={uColumnNotice:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-column-notice/u-column-notice")]).then(o.bind(o,13235))},uRowNotice:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-row-notice/u-row-notice")]).then(o.bind(o,67852))}},i=function(){var e=this,n=e.$createElement,o=(e._self._c,e.show?e.__get_style([{backgroundColor:e.bgColor},e.$u.addStyle(e.customStyle)]):null);e.$mp.data=Object.assign({},{$root:{s0:o}})},c=[],l=o(37479),s=l["default"],r=o(65411),a=o.n(r),d=(a(),o(18535)),m=(0,d["default"])(s,i,c,!1,null,"72ec5916",null,!1,u,t),f=m.exports},37479:function(e,n,o){"use strict";var t=o(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var u=i(o(78246));function i(e){return e&&e.__esModule?e:{default:e}}n["default"]={name:"u-notice-bar",mixins:[t.$u.mpMixin,t.$u.mixin,u.default],data:function(){return{show:!0}},methods:{click:function(e){this.$emit("click",e),this.url&&this.linkType&&this.openPage()},close:function(){this.show=!1,this.$emit("close")}}}},65411:function(){}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-notice-bar/u-notice-bar-create-component"],{},function(e){e("81715")["createComponent"](e(19555))}]);