(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-swiper/u-swiper"],{411:function(e,t,n){"use strict";var i=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var o=u(n(96706));function u(e){return e&&e.__esModule?e:{default:e}}function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}t["default"]={name:"u-swiper",mixins:[i.$u.mpMixin,i.$u.mixin,o.default],data:function(){return{currentIndex:0}},watch:{current:function(e,t){e!==t&&(this.currentIndex=e)}},computed:{itemStyle:function(){var e=this;return function(t){var n={};return e.nextMargin&&e.previousMargin&&(n.borderRadius=i.$u.addUnit(e.radius),t!==e.currentIndex&&(n.transform="scale(0.92)")),n}}},methods:{getItemType:function(e){return"string"===typeof e?i.$u.test.video(this.getSource(e))?"video":"image":"object"===r(e)&&this.keyName?e.type?"image"===e.type?"image":"video"===e.type?"video":"image":i.$u.test.video(this.getSource(e))?"video":"image":void 0},getSource:function(e){return"string"===typeof e?e:"object"===r(e)&&this.keyName?e[this.keyName]:(i.$u.error("请按格式传递列表参数"),"")},change:function(e){var t=e.detail.current;this.pauseVideo(this.currentIndex),this.currentIndex=t,this.$emit("change",e.detail)},pauseVideo:function(e){var t=this.getSource(this.list[e]);if(i.$u.test.video(t)){var n=i.createVideoContext("video-".concat(e),this);n.pause()}},getPoster:function(e){return"object"===r(e)&&e.poster?e.poster:""},clickHandler:function(e){this.$emit("click",e)}}}},53147:function(){},84578:function(e,t,n){"use strict";n.r(t),n.d(t,{__esModule:function(){return a.__esModule},default:function(){return m}});var i,o={uLoadingIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(n.bind(n,94597))},uSwiperIndicator:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-swiper-indicator/u-swiper-indicator")]).then(n.bind(n,58251))}},u=function(){var e=this,t=e.$createElement,n=(e._self._c,e.$u.addUnit(e.height)),i=e.$u.addUnit(e.radius),o=e.loading?null:e.$u.addUnit(e.height),u=e.loading?null:e.$u.addUnit(e.previousMargin),r=e.loading?null:e.$u.addUnit(e.nextMargin),a=e.loading?null:e.__map(e.list,(function(t,n){var i=e.__get_orig(t),o=e.__get_style([e.itemStyle(n)]),u=e.getItemType(t),r="image"===u?e.$u.addUnit(e.height):null,a="image"===u?e.$u.addUnit(e.radius):null,l="image"===u?e.getSource(t):null,d=e.getItemType(t),s="video"===d?e.$u.addUnit(e.height):null,c="video"===d?e.getSource(t):null,g="video"===d?e.getPoster(t):null,m="video"===d?e.showTitle&&e.$u.test.object(t)&&t.title:null,f=e.showTitle&&e.$u.test.object(t)&&t.title&&e.$u.test.image(e.getSource(t));return{$orig:i,s0:o,m0:u,g5:r,g6:a,m1:l,m2:d,g7:s,m3:c,m4:g,g8:m,g9:f}})),l=e.__get_style([e.$u.addStyle(e.indicatorStyle)]),d=e.loading||!e.indicator||e.showTitle?null:e.list.length;e.$mp.data=Object.assign({},{$root:{g0:n,g1:i,g2:o,g3:u,g4:r,l0:a,s1:l,g10:d}})},r=[],a=n(411),l=a["default"],d=n(53147),s=n.n(d),c=(s(),n(18535)),g=(0,c["default"])(l,u,r,!1,null,"5678f2ce",null,!1,o,i),m=g.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-swiper/u-swiper-create-component"],{},function(e){e("81715")["createComponent"](e(84578))}]);