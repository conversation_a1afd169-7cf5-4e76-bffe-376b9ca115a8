(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["uni_modules/lime-echart/components/l-echart/l-echart"],{40878:function(){},51641:function(t,e,r){"use strict";var n;r.r(e),r.d(e,{__esModule:function(){return c.__esModule},default:function(){return d}});var i,o=function(){var t=this,e=t.$createElement,r=(t._self._c,t.canvasId?t.__get_style([t.customStyle]):null);t.$mp.data=Object.assign({},{$root:{s0:r}})},a=[],c=r(84750),s=c["default"],h=r(40878),u=r.n(h),l=(u(),r(18535)),f=(0,l["default"])(s,o,a,!1,null,null,null,!1,n,i),d=f.exports},84750:function(t,e,r){"use strict";var n=r(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=r(62427),o=r(57174);function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",h=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),c=new D(n||[]);return i(a,"_invoke",{value:O(t,r,c)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",v="suspendedYield",y="executing",g="completed",m={};function w(){}function x(){}function b(){}var _={};l(_,s,(function(){return this}));var L=Object.getPrototypeOf,T=L&&L(L(I([])));T&&T!==r&&n.call(T,s)&&(_=T);var C=b.prototype=w.prototype=Object.create(_);function E(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(i,o,a,s){var h=d(t[i],t,o);if("throw"!==h.type){var u=h.arg,l=u.value;return l&&"object"==c(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,s)}))}s(h.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function O(e,r,n){var i=p;return function(o,a){if(i===y)throw Error("Generator is already running");if(i===g){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var c=n.delegate;if(c){var s=P(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===p)throw i=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=y;var h=d(e,r,n);if("normal"===h.type){if(i=n.done?g:v,h.arg===m)continue;return{value:h.arg,done:n.done}}"throw"===h.type&&(i=g,n.method="throw",n.arg=h.arg)}}}function P(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=d(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(c(e)+" is not iterable")}return x.prototype=b,i(C,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:x,configurable:!0}),x.displayName=l(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,l(t,u,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},E(k.prototype),l(k.prototype,h,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new k(f(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(C),l(C,u,"Generator"),l(C,s,(function(){return this})),l(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=I,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return c.type="throw",c.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),h=n.call(a,"finallyLoc");if(s&&h){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!h)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;j(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:I(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(t,e,r,n,i,o,a){try{var c=t[o](a),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,i)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){s(o,n,i,a,c,"next",t)}function c(t){s(o,n,i,a,c,"throw",t)}a(void 0)}))}}e["default"]={name:"lime-echart",props:{type:{type:String,default:"2d"},customStyle:String,isDisableScroll:Boolean,isClickable:{type:Boolean,default:!0},enableHover:Boolean,beforeDelay:{type:Number,default:30},landscape:Boolean},data:function(){return{use2dCanvas:!0,ariaLabel:"图表",width:null,height:null,nodeWidth:null,nodeHeight:null,config:{},inited:!1,finished:!1,file:"",platform:"",isPC:!1,isDown:!1,isOffscreenCanvas:!1,offscreenWidth:0,offscreenHeight:0}},computed:{rootStyle:function(){if(this.landscape)return"transform: translate(-50%,-50%) rotate(90deg); top:50%; left:50%;"},canvasId:function(){return"lime-echart".concat(this._&&this._.uid||this._uid)},offscreenCanvasId:function(){return"".concat(this.canvasId,"_offscreen")},offscreenStyle:function(){return"width:".concat(this.offscreenWidth,"px;height: ").concat(this.offscreenHeight,"px; position: fixed; left: 99999px; background: red")},canvasStyle:function(){return this.rootStyle+(this.width&&this.height?"width:"+this.width+"px;height:"+this.height+"px":"")}},beforeDestroy:function(){this.clear(),this.dispose()},created:function(){var t=(0,o.getDeviceInfo)(),e=t.platform;this.isPC=/windows/i.test(e),this.use2dCanvas="2d"===this.type&&(0,o.canIUseCanvas2d)()},mounted:function(){var t=this;this.$nextTick((function(){t.$emit("finished")}))},methods:{setChart:function(t){this.chart?"function"===typeof t&&this.chart&&t(this.chart):console.warn("组件还未初始化，请先使用 init")},setOption:function(){var t;this.chart&&this.chart.setOption?(t=this.chart).setOption.apply(t,arguments):console.warn("组件还未初始化，请先使用 init")},showLoading:function(){var t;this.chart&&(t=this.chart).showLoading.apply(t,arguments)},hideLoading:function(){this.chart&&this.chart.hideLoading()},clear:function(){this.chart&&!this.chart.isDisposed()&&this.chart.clear()},dispose:function(){this.chart&&!this.chart.isDisposed()&&this.chart.dispose()},resize:function(t){var e=this;t&&t.width&&t.height?(this.height=t.height,this.width=t.width,this.chart&&this.chart.resize(t)):this.$nextTick((function(){(0,o.getRect)(".lime-echart",e).then((function(t){if(t){var r=t.width,n=t.height;e.width=r=r||300,e.height=n=n||300,e.chart.resize({width:r,height:n})}}))}))},canvasToTempFilePath:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=this.use2dCanvas,i=this.canvasId;return new Promise((function(o,a){var c=Object.assign({canvasId:i,success:o,fail:a},e);r&&(delete c.canvasId,c.canvas=t.canvasNode),n.canvasToTempFilePath(c,t)}))},init:function(t){var e=arguments,r=this;return h(a().mark((function n(){var s,h,u,l,f,d,p,v;return a().wrap((function(n){while(1)switch(n.prev=n.next){case 0:for(s=e.length,h=new Array(s>1?s-1:0),u=1;u<s;u++)h[u-1]=e[u];if(!h||0!=h.length||t){n.next=4;break}return console.error("缺少参数：init(echarts, theme?:string, opts?: object, callback?: function)"),n.abrupt("return");case 4:if(l=null,f={},h.forEach((function(t){"function"===typeof t&&(d=t),["string"].includes(c(t))&&(l=t),"object"===c(t)&&(f=t)})),!r.beforeDelay){n.next=9;break}return n.next=9,(0,o.sleep)(r.beforeDelay);case 9:return n.next=11,r.getContext();case 11:return p=n.sent,(0,i.setCanvasCreator)(t,p),n.prev=13,r.chart=t.init(p.canvas,l,Object.assign({},p,f||{})),null===(v=d)||void 0===v||v(r.chart),n.abrupt("return",r.chart);case 19:return n.prev=19,n.t0=n["catch"](13),console.error("【lime-echarts】:",n.t0),n.abrupt("return",null);case 23:case"end":return n.stop()}}),n,null,[[13,19]])})))()},getContext:function(){var t=this;return(0,o.getRect)("#".concat(this.canvasId),this,this.use2dCanvas).then((function(e){if(e){var r,a=o.devicePixelRatio,c=e.width,s=e.height,h=e.node;if(t.width=c=c||300,t.height=s=s||300,h){var u=h.getContext("2d");r=new i.Canvas(u,t,!0,h),t.canvasNode=h}else{a=t.isPC?o.devicePixelRatio:1,t.rect=e,t.nodeWidth=c*a,t.nodeHeight=s*a;var l=n.createCanvasContext(t.canvasId,t);r=new i.Canvas(l,t,!1)}return{canvas:r,width:c,height:s,devicePixelRatio:a,node:h}}return{}}))},getRelative:function(t,e){var r=t.clientX,n=t.clientY;return r&&n||!e||!e[0]||(r=e[0].clientX,n=e[0].clientY),{x:r-this.rect.left,y:n-this.rect.top,wheelDelta:t.wheelDelta||0}},getTouch:function(t,e){var r=e&&e[0]||{},n=r.x,i=n?e[0]:this.getRelative(t,e);if(this.landscape){var o=[i.y,this.height-i.x];i.x=o[0],i.y=o[1]}return i},touchStart:function(t){var e=this;this.isDown=!0;var r=function(){var r=(0,o.convertTouchesToArray)(t.touches);if(e.chart){var n=e.getTouch(t,r);e.startX=n.x,e.startY=n.y,e.startT=new Date;var a=e.chart.getZr().handler;i.dispatch.call(a,"mousedown",n),i.dispatch.call(a,"mousemove",n),a.processGesture((0,o.wrapTouch)(t),"start"),clearTimeout(e.endTimer)}};this.isPC?(0,o.getRect)("#".concat(this.canvasId),{context:this}).then((function(t){e.rect=t,r()})):r()},touchMove:function(t){this.isPC&&this.enableHover&&!this.isDown&&(this.isDown=!0);var e=(0,o.convertTouchesToArray)(t.touches);if(this.chart&&this.isDown){var r=this.chart.getZr().handler;i.dispatch.call(r,"mousemove",this.getTouch(t,e)),r.processGesture((0,o.wrapTouch)(t),"change")}},touchEnd:function(t){if(this.isDown=!1,this.chart){var e=(0,o.convertTouchesToArray)(t.changedTouches),r=e&&e[0]||{},n=r.x,a=(n?e[0]:this.getRelative(t,e))||{};if(this.landscape){var c=[a.y,this.height-a.x];a.x=c[0],a.y=c[1]}var s=this.chart.getZr().handler,h=Math.abs(a.x-this.startX)<10&&new Date-this.startT<200;i.dispatch.call(s,"mouseup",a),s.processGesture((0,o.wrapTouch)(t),"end"),h?i.dispatch.call(s,"click",a):this.endTimer=setTimeout((function(){i.dispatch.call(s,"mousemove",{x:999999999,y:999999999}),i.dispatch.call(s,"mouseup",{x:999999999,y:999999999})}),50)}}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["uni_modules/lime-echart/components/l-echart/l-echart-create-component"],{},function(t){t("81715")["createComponent"](t(51641))}]);