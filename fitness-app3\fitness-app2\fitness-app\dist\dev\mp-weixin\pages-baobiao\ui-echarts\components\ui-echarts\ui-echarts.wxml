<view class="{{['ui-echarts','data-v-7cf13c33',customClass]}}" style="{{(wrapStyle)}}"><canvas class="ui-echarts_canvas data-v-7cf13c33" style="{{(wrapStyle)}}" canvas-id="{{canvasId}}" id="{{canvasId}}" type="2d" data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></canvas></view>