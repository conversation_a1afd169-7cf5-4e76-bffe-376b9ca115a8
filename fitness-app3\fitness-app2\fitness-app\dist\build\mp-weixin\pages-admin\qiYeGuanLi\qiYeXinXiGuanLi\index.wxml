<theme-wrap scoped-slots-compiler="augmented" vue-id="fcd13606-1" class="data-v-5cb1fb50" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-5cb1fb50" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('fcd13606-2')+','+('fcd13606-1')}}" title="企业信息管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-5cb1fb50" bind:__l="__l"></u-navbar><block wx:if="{{$root.g0>0}}"><view class="data-v-5cb1fb50"><block wx:for="{{qyList}}" wx:for-item="list" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['gotoDetails',['$0'],[[['qyList','',idx]]]]]]]}}" class="uList data-v-5cb1fb50" bindtap="__e"><view class="enterpriseBody data-v-5cb1fb50"><view class="enterpriseTitle bold data-v-5cb1fb50">{{list.companyName}}</view><view class="enterpriseBottom u-flex w-100 data-v-5cb1fb50"><view class="u-m-l-20 u-flex-1 data-v-5cb1fb50">{{"联系人："+list.contractName}}</view><view class="data-v-5cb1fb50">{{"联系电话："+list.contractPhone}}</view></view></view></view></block><view class="whiteView data-v-5cb1fb50"></view><view class="bottonBtn u-flex data-v-5cb1fb50"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-5cb1fb50" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增企业</view></view></view></block><block wx:else><u-empty vue-id="{{('fcd13606-3')+','+('fcd13606-1')}}" marginTop="150" mode="list" class="data-v-5cb1fb50" bind:__l="__l"></u-empty></block></view></theme-wrap>