<theme-wrap scoped-slots-compiler="augmented" vue-id="2f258c46-1" class="data-v-122b72a3" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-122b72a3" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('2f258c46-2')+','+('2f258c46-1')}}" title="教练管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-122b72a3" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-122b72a3"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 data-v-122b72a3" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view><block wx:if="{{$root.g0>0}}"><view class="u-p-t-20 u-p-b-20 u-p-r-40 u-p-l-40 w-100 border-16 u-m-b-20 bg-fff data-v-122b72a3"><u-radio-group vue-id="{{('2f258c46-3')+','+('2f258c46-1')}}" placement="column" class="data-v-122b72a3" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{roleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('2f258c46-4-'+index)+','+('2f258c46-3')}}" customStyle="{{({marginBottom:'8px'})}}" label="{{item.nickName}}" name="{{item.nickName}}" data-event-opts="{{[['^change',[['radioChange',['$0'],[[['roleList','',index]]]]]]]}}" bind:change="__e" class="data-v-122b72a3" bind:__l="__l"></u-radio></block></u-radio-group></view></block><block wx:else><u-empty vue-id="{{('2f258c46-5')+','+('2f258c46-1')}}" marginTop="150" mode="data" class="data-v-122b72a3" bind:__l="__l"></u-empty></block></view><u-picker vue-id="{{('2f258c46-6')+','+('2f258c46-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-122b72a3" bind:__l="__l"></u-picker><view hidden="{{!(choseList!='')}}" class="bottom-blk bg-fff u-flex w-100 u-p-40 data-v-122b72a3"><view class="u-flex-1 u-m-r-10 data-v-122b72a3"><u-button vue-id="{{('2f258c46-7')+','+('2f258c46-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['update']]]]}}" bind:click="__e" class="data-v-122b72a3" bind:__l="__l" vue-slots="{{['default']}}">修改</u-button></view><view class="u-flex-1 u-m-r-10 data-v-122b72a3"><u-button vue-id="{{('2f258c46-8')+','+('2f258c46-1')}}" color="{{$root.m4['buttonLightBgColor']}}" loading="{{disabled}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['setCourse']]]]}}" bind:click="__e" class="data-v-122b72a3" bind:__l="__l" vue-slots="{{['default']}}">授课设置</u-button></view><view class="u-flex-1 data-v-122b72a3"><u-button vue-id="{{('2f258c46-9')+','+('2f258c46-1')}}" color="{{$root.m5['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['updateCard']]]]}}" bind:click="__e" class="data-v-122b72a3" bind:__l="__l" vue-slots="{{['default']}}">会员卡设置</u-button></view></view></view></theme-wrap>