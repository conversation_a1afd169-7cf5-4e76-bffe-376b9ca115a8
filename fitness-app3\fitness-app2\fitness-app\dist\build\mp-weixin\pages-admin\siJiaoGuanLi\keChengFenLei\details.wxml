<theme-wrap scoped-slots-compiler="augmented" vue-id="5882e270-1" class="data-v-1e2fd114" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-1e2fd114" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('5882e270-2')+','+('5882e270-1')}}" title="课程详情" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-1e2fd114" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-1e2fd114"><view class="formView data-v-1e2fd114"><view class="formList data-v-1e2fd114"><view class="data-v-1e2fd114">课程类型名称:</view><u--input bind:input="__e" vue-id="{{('5882e270-3')+','+('5882e270-1')}}" border="{{false}}" value="{{formList.courseTypeName}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseTypeName','$event',[]],['formList']]]]]}}" class="data-v-1e2fd114" bind:__l="__l"></u--input></view></view></view><view class="bottonBtn u-flex data-v-1e2fd114"><view data-event-opts="{{[['tap',[['deleteTrainer']]]]}}" class="moreBtn data-v-1e2fd114" style="{{'color:'+($root.m3['buttonLightBgColor'])+';'+('border-color:'+($root.m4['buttonLightBgColor'])+';')}}" bindtap="__e">删除课程类型</view><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="addHuiYuan data-v-1e2fd114" style="{{'background:'+($root.m5['buttonLightBgColor'])+';'+('color:'+($root.m6['buttonTextColor'])+';')+('border-color:'+($root.m7['buttonLightBgColor'])+';')}}" bindtap="__e">修改课程类型</view></view></view></theme-wrap>