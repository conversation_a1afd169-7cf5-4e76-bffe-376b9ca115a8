(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-notify/u-notify"],{11380:function(t,n,o){"use strict";o.r(n),o.d(n,{__esModule:function(){return s.__esModule},default:function(){return m}});var e,i={uTransition:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-transition/u-transition")]).then(o.bind(o,68270))},uStatusBar:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-status-bar/u-status-bar")]).then(o.bind(o,51401))},uIcon:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(o.bind(o,78278))}},r=function(){var t=this,n=t.$createElement,o=(t._self._c,t.__get_style([t.backgroundColor,t.$u.addStyle(t.customStyle)])),e=["success","warning","error"].includes(t.tmpConfig.type),i=t.$u.addUnit(t.tmpConfig.fontSize);t.$mp.data=Object.assign({},{$root:{s0:o,g0:e,g1:i}})},u=[],s=o(99657),c=s["default"],a=o(81393),f=o.n(a),p=(f(),o(18535)),l=(0,p["default"])(c,r,u,!1,null,"099d0df0",null,!1,i,e),m=l.exports},81393:function(){},99657:function(t,n,o){"use strict";var e=o(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var i=r(o(82687));function r(t){return t&&t.__esModule?t:{default:t}}n["default"]={name:"u-notify",mixins:[e.$u.mpMixin,e.$u.mixin,i.default],data:function(){return{open:!1,timer:null,config:{top:e.$u.props.notify.top,type:e.$u.props.notify.type,color:e.$u.props.notify.color,bgColor:e.$u.props.notify.bgColor,message:e.$u.props.notify.message,duration:e.$u.props.notify.duration,fontSize:e.$u.props.notify.fontSize,safeAreaInsetTop:e.$u.props.notify.safeAreaInsetTop},tmpConfig:{}}},computed:{containerStyle:function(){var t=0;this.tmpConfig.top;var n={top:e.$u.addUnit(0===this.tmpConfig.top?t:this.tmpConfig.top),position:"fixed",left:0,right:0,zIndex:10076};return n},backgroundColor:function(){var t={};return this.tmpConfig.bgColor&&(t.backgroundColor=this.tmpConfig.bgColor),t},icon:function(){var t;return"success"===this.tmpConfig.type?t="checkmark-circle":"error"===this.tmpConfig.type?t="close-circle":"warning"===this.tmpConfig.type&&(t="error-circle"),t}},created:function(){var t=this;["primary","success","error","warning"].map((function(n){t[n]=function(o){return t.show({type:n,message:o})}}))},methods:{show:function(t){var n=this;this.tmpConfig=e.$u.deepMerge(this.config,t),this.clearTimer(),this.open=!0,this.tmpConfig.duration>0&&(this.timer=setTimeout((function(){n.open=!1,n.clearTimer(),"function"===typeof n.tmpConfig.complete&&n.tmpConfig.complete()}),this.tmpConfig.duration))},close:function(){this.clearTimer()},clearTimer:function(){this.open=!1,clearTimeout(this.timer),this.timer=null}},beforeDestroy:function(){this.clearTimer()}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-notify/u-notify-create-component"],{},function(t){t("81715")["createComponent"](t(11380))}]);