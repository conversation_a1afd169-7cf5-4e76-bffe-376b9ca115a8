(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-loadmore/u-loadmore"],{8737:function(){},67727:function(e,o,t){"use strict";t.r(o),t.d(o,{__esModule:function(){return l.__esModule},default:function(){return f}});var n,i={uLine:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-line/u-line")]).then(t.bind(t,84916))},uLoadingIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(t.bind(t,94597))}},u=function(){var e=this,o=e.$createElement,t=(e._self._c,e.__get_style([e.$u.addStyle(e.customStyle),{backgroundColor:e.bgColor,marginBottom:e.$u.addUnit(e.marginBottom),marginTop:e.$u.addUnit(e.marginTop),height:e.$u.addUnit(e.height)}])),n=e.__get_style([e.loadTextStyle]);e.$mp.data=Object.assign({},{$root:{s0:t,s1:n}})},a=[],l=t(98289),d=l["default"],s=t(8737),r=t.n(s),c=(r(),t(18535)),m=(0,c["default"])(d,u,a,!1,null,"722334c2",null,!1,i,n),f=m.exports},98289:function(e,o,t){"use strict";var n=t(81715)["default"];Object.defineProperty(o,"__esModule",{value:!0}),o["default"]=void 0;var i=u(t(89955));function u(e){return e&&e.__esModule?e:{default:e}}o["default"]={name:"u-loadmore",mixins:[n.$u.mpMixin,n.$u.mixin,i.default],data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:n.$u.addUnit(this.fontSize),lineHeight:n.$u.addUnit(this.fontSize),backgroundColor:this.bgColor}},showText:function(){var e="";return e="loadmore"==this.status?this.loadmoreText:"loading"==this.status?this.loadingText:"nomore"==this.status&&this.isDot?this.dotText:this.nomoreText,e}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-loadmore/u-loadmore-create-component"],{},function(e){e("81715")["createComponent"](e(67727))}]);