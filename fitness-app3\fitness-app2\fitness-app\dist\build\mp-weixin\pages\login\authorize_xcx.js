(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/login/authorize_xcx"],{13289:function(t,e,n){"use strict";var r=n(81715)["default"];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(n(68466));n(77020);function a(t){return t&&t.__esModule?t:{default:t}}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),u=new T(r||[]);return i(a,"_invoke",{value:k(t,n,u)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",v="suspendedYield",y="executing",g="completed",m={};function b(){}function w(){}function _(){}var E={};f(E,c,(function(){return this}));var L=Object.getPrototypeOf,P=L&&L(L(j([])));P&&P!==n&&r.call(P,c)&&(E=P);var x=_.prototype=b.prototype=Object.create(E);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(i,a,u,c){var s=d(t[i],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):e.resolve(f).then((function(t){l.value=t,u(l)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var a;i(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return a=a?a.then(o,o):o()}})}function k(e,n,r){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var u=r.delegate;if(u){var c=N(u,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=y;var s=d(e,n,r);if("normal"===s.type){if(o=r.done?g:v,s.arg===m)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=g,r.method="throw",r.arg=s.arg)}}}function N(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,N(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=d(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function M(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(M,this),this.reset(!0)}function j(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return w.prototype=_,i(x,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},S(O.prototype),f(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new O(h(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(x),f(x,l,"Generator"),f(x,c,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=j,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return u.type="throw",u.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),A(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;A(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:j(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function c(t,e,n,r,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(t){c(i,r,o,a,u,"next",t)}function u(t){c(i,r,o,a,u,"throw",t)}a(void 0)}))}}e["default"]={data:function(){return{loading:!1,disabled:!1,btnLoading:!1,onSubmitDisabled:!1,loadNum:60,codeTips:"获取验证码",NEEDMOBILE:+{NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"海牛",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}.VUE_APP_NEED_MOBILE,KEYMOBILE:+{NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"海牛",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}.VUE_APP_KEY_MOBILE,info:{},rules:{mobile:[{required:!0,message:"请输入手机号",trigger:["change","blur"]},{validator:function(t,e,n){var r=/^1[3-9]\d{9}$/;return r.test(e)},message:"手机号码不正确",trigger:["change","blur"]}],code:[{required:!0,message:"请输入验证码",trigger:["change","blur"]}]}}},methods:{getPhoneNumber:function(t){var e=this;return s(u().mark((function n(){var o;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.btnLoading=!0,"getPhoneNumber:fail user deny"==t.detail.errMsg?(e.btnLoading=!1,e.$refs.uToast.show({message:"绑定失败",type:"error",duration:"2300"})):(o=r.getStorageSync("user").xcx_openid,e.bindMobile(t,o));case 2:case"end":return n.stop()}}),n)})))()},login:function(t){i.default.getTokenXcx({data:{code:t},method:"POST"}).then((function(t){var e=t.data.data;r.setStorageSync("token",e.token),r.setStorageSync("user",e.user)}))},bindMobile:function(t,e){var n=this;return s(u().mark((function o(){return u().wrap((function(o){while(1)switch(o.prev=o.next){case 0:n.btnLoading=!0,i.default.loginByMobile({data:{code:t.detail.code,openid:e},method:"POST"}).then((function(t){200==t.data.code&&t.data.data.token?(r.setStorageSync("token",t.data.data.token),r.setStorageSync("user",t.data.data.user),r.navigateBack()):(r.showToast({title:t.data.message,duration:800,icon:"none"}),n.btnLoading=!1)}));case 2:case"end":return o.stop()}}),o)})))()},useStorage:function(t,e){r.removeStorageSync(t),r.setStorageSync(t,e)}}}},42210:function(t,e,n){"use strict";var r=n(51372)["default"],o=n(81715)["createPage"];n(96910);a(n(923));var i=a(n(73589));function a(t){return t&&t.__esModule?t:{default:t}}r.__webpack_require_UNI_MP_PLUGIN__=n,o(i.default)},69208:function(){},73589:function(t,e,n){"use strict";n.r(e),n.d(e,{__esModule:function(){return u.__esModule},default:function(){return d}});var r,o={uNoNetwork:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-no-network/u-no-network")]).then(n.bind(n,43763))},uToast:function(){return n.e("node-modules/uview-ui/components/u-toast/u-toast").then(n.bind(n,67559))},uNotify:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-notify/u-notify")]).then(n.bind(n,11380))},uLoadingPage:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loading-page/u-loading-page")]).then(n.bind(n,51873))},"u-Image":function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u--image/u--image")]).then(n.bind(n,84027))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(n,65610))}},i=function(){var t=this,e=t.$createElement;t._self._c},a=[],u=n(13289),c=u["default"],s=n(69208),l=n.n(s),f=(l(),n(18535)),h=(0,f["default"])(c,i,a,!1,null,null,null,!1,o,r),d=h.exports}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(42210)}));t.O()}]);