/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/App.vue?vue&type=style&index=0&lang=scss& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color, .theme-color.u-content-color {
  color: var(--base-color);
}
.theme-bg-color, .bgc {
  background: var(--base-bg-color);
}
.theme-nav-bg-color, .nbc {
  background: var(--navbar-color);
}
.theme-button-color, .bc {
  background: var(--button-bg-color);
}
.theme-light-button-color, .lbc {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color, .btc {
  color: var(--button-text-color);
}
.theme-light-text-color, .ltc {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap {
  background: var(--scroll-item-bg-color);
}
.u-line-1 {







  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical !important;
}
.u-line-2 {







  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {







  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {







  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {







  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-border {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.u-border-top {
  border-top-width: 0.5px !important;
  border-color: #dadbde !important;
  border-top-style: solid;
}
.u-border-left {
  border-left-width: 0.5px !important;
  border-color: #dadbde !important;
  border-left-style: solid;
}
.u-border-right {
  border-right-width: 0.5px !important;
  border-color: #dadbde !important;
  border-right-style: solid;
}
.u-border-bottom {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
.u-border-top-bottom {
  border-top-width: 0.5px !important;
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-top-style: solid;
  border-bottom-style: solid;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;

  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
page {
  background-color: #f3f3f3;
  --animate-duration: 1s;
  --animate-delay: 1s;
  --animate-repeat: 1;
}
page.pages-u-avatar-cropper-u-avatar-cropper {
  background-color: #000;
}
.loading {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.container {
  width: 100%;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.u-form-item__body__left__content__label {
  font-size: 30rpx !important;
  font-weight: bold !important;
}
.u-form-item__body__right__message {
  margin-left: 0 !important;
}
.u-input__content__field-wrapper__field {
  font-size: 30rpx !important;
  text-align: right !important;
}
.u-radio-group, .u-checkbox-group, .u-textarea {
  margin-top: 16rpx;
}
.u-cell__body {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.u-cell--clickable {
  background-color: #FFF !important;
}
.u-cell__title-text {
  color: #000;
  font-weight: bold;
}
.u-icon__icon--info {
  color: #000 !important;
  width: 24rpx !important;
}
.fc-999 {
  color: #999;
}
.fc-fff {
  color: #fff;
}
.fw-bold, .font-bold {
  font-weight: bold;
}
.u-relative,
.u-rela {
  position: relative;
}
.bottom-blk {
  bottom: 0;
  left: 0;
  position: fixed;
  z-index: 999;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
}
.bottom-placeholder {
  padding-bottom: 200rpx;
}
.u-absolute,
.u-abso {
  position: absolute;
}
image {
  display: inline-block;
}
view,
text {
  box-sizing: border-box;
}
.u-font-xs {
  font-size: 22rpx;
}
.u-font-sm {
  font-size: 26rpx;
}
.u-font-md {
  font-size: 28rpx;
}
.u-font-lg {
  font-size: 30rpx;
}
.u-font-xl {
  font-size: 34rpx;
}
.flex-0 {
  flex-shrink: 0;
  flex-grow: 0;
}
.u-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.text-no-wrap {
  white-space: nowrap;
}
.u-flex-wrap {
  flex-wrap: wrap;
}
.u-flex-nowrap {
  flex-wrap: nowrap;
}
.u-col-center {
  align-items: center;
}
.u-col-top,
.u-col-start {
  align-items: flex-start;
}
.u-col-bottom,
.u-col-end {
  align-items: flex-end;
}
.u-row-center {
  justify-content: center;
}
.u-row-left,
.u-row-start {
  justify-content: flex-start;
}
.u-row-right,
.u-row-end {
  justify-content: flex-end;
}
.u-row-between {
  justify-content: space-between;
}
.u-row-around {
  justify-content: space-around;
}
.u-text-left {
  text-align: left;
}
.u-text-center {
  text-align: center;
}
.u-text-right {
  text-align: right;
}
.u-flex-col {
  display: flex;
  flex-direction: column;
}
.u-flex-0 {
  flex: 0;
}
.u-flex-1 {
  flex: 1;
}
.u-flex-2 {
  flex: 2;
}
.u-flex-3 {
  flex: 3;
}
.u-flex-4 {
  flex: 4;
}
.u-flex-5 {
  flex: 5;
}
.u-flex-6 {
  flex: 6;
}
.u-flex-7 {
  flex: 7;
}
.u-flex-8 {
  flex: 8;
}
.u-flex-9 {
  flex: 9;
}
.u-flex-10 {
  flex: 10;
}
.u-flex-11 {
  flex: 11;
}
.u-flex-12 {
  flex: 12;
}
.u-font-9 {
  font-size: 9px;
}
.u-font-10 {
  font-size: 10px;
}
.u-font-11 {
  font-size: 11px;
}
.u-font-12 {
  font-size: 12px;
}
.u-font-13 {
  font-size: 13px;
}
.u-font-14 {
  font-size: 14px;
}
.u-font-15 {
  font-size: 15px;
}
.u-font-16 {
  font-size: 16px;
}
.u-font-17 {
  font-size: 17px;
}
.u-font-18 {
  font-size: 18px;
}
.u-font-19 {
  font-size: 19px;
}
.u-font-20 {
  font-size: 20rpx;
}
.u-font-21 {
  font-size: 21rpx;
}
.u-font-22 {
  font-size: 22rpx;
}
.u-font-23 {
  font-size: 23rpx;
}
.u-font-24 {
  font-size: 24rpx;
}
.u-font-25 {
  font-size: 25rpx;
}
.u-font-26 {
  font-size: 26rpx;
}
.u-font-27 {
  font-size: 27rpx;
}
.u-font-28 {
  font-size: 28rpx;
}
.u-font-29 {
  font-size: 29rpx;
}
.u-font-30 {
  font-size: 30rpx;
}
.u-font-31 {
  font-size: 31rpx;
}
.u-font-32 {
  font-size: 32rpx;
}
.u-font-33 {
  font-size: 33rpx;
}
.u-font-34 {
  font-size: 34rpx;
}
.u-font-35 {
  font-size: 35rpx;
}
.u-font-36 {
  font-size: 36rpx;
}
.u-font-37 {
  font-size: 37rpx;
}
.u-font-38 {
  font-size: 38rpx;
}
.u-font-39 {
  font-size: 39rpx;
}
.u-font-40 {
  font-size: 40rpx;
}
.u-margin-0,
.u-m-0 {
  margin: 0rpx !important;
}
.u-padding-0,
.u-p-0 {
  padding: 0rpx !important;
}
.u-m-l-0 {
  margin-left: 0rpx !important;
}
.u-p-l-0 {
  padding-left: 0rpx !important;
}
.u-margin-left-0 {
  margin-left: 0rpx !important;
}
.u-padding-left-0 {
  padding-left: 0rpx !important;
}
.u-m-t-0 {
  margin-top: 0rpx !important;
}
.u-p-t-0 {
  padding-top: 0rpx !important;
}
.u-margin-top-0 {
  margin-top: 0rpx !important;
}
.u-padding-top-0 {
  padding-top: 0rpx !important;
}
.u-m-r-0 {
  margin-right: 0rpx !important;
}
.u-p-r-0 {
  padding-right: 0rpx !important;
}
.u-margin-right-0 {
  margin-right: 0rpx !important;
}
.u-padding-right-0 {
  padding-right: 0rpx !important;
}
.u-m-b-0 {
  margin-bottom: 0rpx !important;
}
.u-p-b-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-bottom-0 {
  margin-bottom: 0rpx !important;
}
.u-padding-bottom-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-2,
.u-m-2 {
  margin: 2rpx !important;
}
.u-padding-2,
.u-p-2 {
  padding: 2rpx !important;
}
.u-m-l-2 {
  margin-left: 2rpx !important;
}
.u-p-l-2 {
  padding-left: 2rpx !important;
}
.u-margin-left-2 {
  margin-left: 2rpx !important;
}
.u-padding-left-2 {
  padding-left: 2rpx !important;
}
.u-m-t-2 {
  margin-top: 2rpx !important;
}
.u-p-t-2 {
  padding-top: 2rpx !important;
}
.u-margin-top-2 {
  margin-top: 2rpx !important;
}
.u-padding-top-2 {
  padding-top: 2rpx !important;
}
.u-m-r-2 {
  margin-right: 2rpx !important;
}
.u-p-r-2 {
  padding-right: 2rpx !important;
}
.u-margin-right-2 {
  margin-right: 2rpx !important;
}
.u-padding-right-2 {
  padding-right: 2rpx !important;
}
.u-m-b-2 {
  margin-bottom: 2rpx !important;
}
.u-p-b-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-bottom-2 {
  margin-bottom: 2rpx !important;
}
.u-padding-bottom-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-4,
.u-m-4 {
  margin: 4rpx !important;
}
.u-padding-4,
.u-p-4 {
  padding: 4rpx !important;
}
.u-m-l-4 {
  margin-left: 4rpx !important;
}
.u-p-l-4 {
  padding-left: 4rpx !important;
}
.u-margin-left-4 {
  margin-left: 4rpx !important;
}
.u-padding-left-4 {
  padding-left: 4rpx !important;
}
.u-m-t-4 {
  margin-top: 4rpx !important;
}
.u-p-t-4 {
  padding-top: 4rpx !important;
}
.u-margin-top-4 {
  margin-top: 4rpx !important;
}
.u-padding-top-4 {
  padding-top: 4rpx !important;
}
.u-m-r-4 {
  margin-right: 4rpx !important;
}
.u-p-r-4 {
  padding-right: 4rpx !important;
}
.u-margin-right-4 {
  margin-right: 4rpx !important;
}
.u-padding-right-4 {
  padding-right: 4rpx !important;
}
.u-m-b-4 {
  margin-bottom: 4rpx !important;
}
.u-p-b-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-bottom-4 {
  margin-bottom: 4rpx !important;
}
.u-padding-bottom-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-5,
.u-m-5 {
  margin: 5rpx !important;
}
.u-padding-5,
.u-p-5 {
  padding: 5rpx !important;
}
.u-m-l-5 {
  margin-left: 5rpx !important;
}
.u-p-l-5 {
  padding-left: 5rpx !important;
}
.u-margin-left-5 {
  margin-left: 5rpx !important;
}
.u-padding-left-5 {
  padding-left: 5rpx !important;
}
.u-m-t-5 {
  margin-top: 5rpx !important;
}
.u-p-t-5 {
  padding-top: 5rpx !important;
}
.u-margin-top-5 {
  margin-top: 5rpx !important;
}
.u-padding-top-5 {
  padding-top: 5rpx !important;
}
.u-m-r-5 {
  margin-right: 5rpx !important;
}
.u-p-r-5 {
  padding-right: 5rpx !important;
}
.u-margin-right-5 {
  margin-right: 5rpx !important;
}
.u-padding-right-5 {
  padding-right: 5rpx !important;
}
.u-m-b-5 {
  margin-bottom: 5rpx !important;
}
.u-p-b-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-bottom-5 {
  margin-bottom: 5rpx !important;
}
.u-padding-bottom-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-6,
.u-m-6 {
  margin: 6rpx !important;
}
.u-padding-6,
.u-p-6 {
  padding: 6rpx !important;
}
.u-m-l-6 {
  margin-left: 6rpx !important;
}
.u-p-l-6 {
  padding-left: 6rpx !important;
}
.u-margin-left-6 {
  margin-left: 6rpx !important;
}
.u-padding-left-6 {
  padding-left: 6rpx !important;
}
.u-m-t-6 {
  margin-top: 6rpx !important;
}
.u-p-t-6 {
  padding-top: 6rpx !important;
}
.u-margin-top-6 {
  margin-top: 6rpx !important;
}
.u-padding-top-6 {
  padding-top: 6rpx !important;
}
.u-m-r-6 {
  margin-right: 6rpx !important;
}
.u-p-r-6 {
  padding-right: 6rpx !important;
}
.u-margin-right-6 {
  margin-right: 6rpx !important;
}
.u-padding-right-6 {
  padding-right: 6rpx !important;
}
.u-m-b-6 {
  margin-bottom: 6rpx !important;
}
.u-p-b-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-bottom-6 {
  margin-bottom: 6rpx !important;
}
.u-padding-bottom-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-8,
.u-m-8 {
  margin: 8rpx !important;
}
.u-padding-8,
.u-p-8 {
  padding: 8rpx !important;
}
.u-m-l-8 {
  margin-left: 8rpx !important;
}
.u-p-l-8 {
  padding-left: 8rpx !important;
}
.u-margin-left-8 {
  margin-left: 8rpx !important;
}
.u-padding-left-8 {
  padding-left: 8rpx !important;
}
.u-m-t-8 {
  margin-top: 8rpx !important;
}
.u-p-t-8 {
  padding-top: 8rpx !important;
}
.u-margin-top-8 {
  margin-top: 8rpx !important;
}
.u-padding-top-8 {
  padding-top: 8rpx !important;
}
.u-m-r-8 {
  margin-right: 8rpx !important;
}
.u-p-r-8 {
  padding-right: 8rpx !important;
}
.u-margin-right-8 {
  margin-right: 8rpx !important;
}
.u-padding-right-8 {
  padding-right: 8rpx !important;
}
.u-m-b-8 {
  margin-bottom: 8rpx !important;
}
.u-p-b-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-bottom-8 {
  margin-bottom: 8rpx !important;
}
.u-padding-bottom-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-10,
.u-m-10 {
  margin: 10rpx !important;
}
.u-padding-10,
.u-p-10 {
  padding: 10rpx !important;
}
.u-m-l-10 {
  margin-left: 10rpx !important;
}
.u-p-l-10 {
  padding-left: 10rpx !important;
}
.u-margin-left-10 {
  margin-left: 10rpx !important;
}
.u-padding-left-10 {
  padding-left: 10rpx !important;
}
.u-m-t-10 {
  margin-top: 10rpx !important;
}
.u-p-t-10 {
  padding-top: 10rpx !important;
}
.u-margin-top-10 {
  margin-top: 10rpx !important;
}
.u-padding-top-10 {
  padding-top: 10rpx !important;
}
.u-m-r-10 {
  margin-right: 10rpx !important;
}
.u-p-r-10 {
  padding-right: 10rpx !important;
}
.u-margin-right-10 {
  margin-right: 10rpx !important;
}
.u-padding-right-10 {
  padding-right: 10rpx !important;
}
.u-m-b-10 {
  margin-bottom: 10rpx !important;
}
.u-p-b-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-bottom-10 {
  margin-bottom: 10rpx !important;
}
.u-padding-bottom-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-12,
.u-m-12 {
  margin: 12rpx !important;
}
.u-padding-12,
.u-p-12 {
  padding: 12rpx !important;
}
.u-m-l-12 {
  margin-left: 12rpx !important;
}
.u-p-l-12 {
  padding-left: 12rpx !important;
}
.u-margin-left-12 {
  margin-left: 12rpx !important;
}
.u-padding-left-12 {
  padding-left: 12rpx !important;
}
.u-m-t-12 {
  margin-top: 12rpx !important;
}
.u-p-t-12 {
  padding-top: 12rpx !important;
}
.u-margin-top-12 {
  margin-top: 12rpx !important;
}
.u-padding-top-12 {
  padding-top: 12rpx !important;
}
.u-m-r-12 {
  margin-right: 12rpx !important;
}
.u-p-r-12 {
  padding-right: 12rpx !important;
}
.u-margin-right-12 {
  margin-right: 12rpx !important;
}
.u-padding-right-12 {
  padding-right: 12rpx !important;
}
.u-m-b-12 {
  margin-bottom: 12rpx !important;
}
.u-p-b-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-bottom-12 {
  margin-bottom: 12rpx !important;
}
.u-padding-bottom-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-14,
.u-m-14 {
  margin: 14rpx !important;
}
.u-padding-14,
.u-p-14 {
  padding: 14rpx !important;
}
.u-m-l-14 {
  margin-left: 14rpx !important;
}
.u-p-l-14 {
  padding-left: 14rpx !important;
}
.u-margin-left-14 {
  margin-left: 14rpx !important;
}
.u-padding-left-14 {
  padding-left: 14rpx !important;
}
.u-m-t-14 {
  margin-top: 14rpx !important;
}
.u-p-t-14 {
  padding-top: 14rpx !important;
}
.u-margin-top-14 {
  margin-top: 14rpx !important;
}
.u-padding-top-14 {
  padding-top: 14rpx !important;
}
.u-m-r-14 {
  margin-right: 14rpx !important;
}
.u-p-r-14 {
  padding-right: 14rpx !important;
}
.u-margin-right-14 {
  margin-right: 14rpx !important;
}
.u-padding-right-14 {
  padding-right: 14rpx !important;
}
.u-m-b-14 {
  margin-bottom: 14rpx !important;
}
.u-p-b-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-bottom-14 {
  margin-bottom: 14rpx !important;
}
.u-padding-bottom-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-15,
.u-m-15 {
  margin: 15rpx !important;
}
.u-padding-15,
.u-p-15 {
  padding: 15rpx !important;
}
.u-m-l-15 {
  margin-left: 15rpx !important;
}
.u-p-l-15 {
  padding-left: 15rpx !important;
}
.u-margin-left-15 {
  margin-left: 15rpx !important;
}
.u-padding-left-15 {
  padding-left: 15rpx !important;
}
.u-m-t-15 {
  margin-top: 15rpx !important;
}
.u-p-t-15 {
  padding-top: 15rpx !important;
}
.u-margin-top-15 {
  margin-top: 15rpx !important;
}
.u-padding-top-15 {
  padding-top: 15rpx !important;
}
.u-m-r-15 {
  margin-right: 15rpx !important;
}
.u-p-r-15 {
  padding-right: 15rpx !important;
}
.u-margin-right-15 {
  margin-right: 15rpx !important;
}
.u-padding-right-15 {
  padding-right: 15rpx !important;
}
.u-m-b-15 {
  margin-bottom: 15rpx !important;
}
.u-p-b-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-bottom-15 {
  margin-bottom: 15rpx !important;
}
.u-padding-bottom-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-16,
.u-m-16 {
  margin: 16rpx !important;
}
.u-padding-16,
.u-p-16 {
  padding: 16rpx !important;
}
.u-m-l-16 {
  margin-left: 16rpx !important;
}
.u-p-l-16 {
  padding-left: 16rpx !important;
}
.u-margin-left-16 {
  margin-left: 16rpx !important;
}
.u-padding-left-16 {
  padding-left: 16rpx !important;
}
.u-m-t-16 {
  margin-top: 16rpx !important;
}
.u-p-t-16 {
  padding-top: 16rpx !important;
}
.u-margin-top-16 {
  margin-top: 16rpx !important;
}
.u-padding-top-16 {
  padding-top: 16rpx !important;
}
.u-m-r-16 {
  margin-right: 16rpx !important;
}
.u-p-r-16 {
  padding-right: 16rpx !important;
}
.u-margin-right-16 {
  margin-right: 16rpx !important;
}
.u-padding-right-16 {
  padding-right: 16rpx !important;
}
.u-m-b-16 {
  margin-bottom: 16rpx !important;
}
.u-p-b-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-bottom-16 {
  margin-bottom: 16rpx !important;
}
.u-padding-bottom-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-18,
.u-m-18 {
  margin: 18rpx !important;
}
.u-padding-18,
.u-p-18 {
  padding: 18rpx !important;
}
.u-m-l-18 {
  margin-left: 18rpx !important;
}
.u-p-l-18 {
  padding-left: 18rpx !important;
}
.u-margin-left-18 {
  margin-left: 18rpx !important;
}
.u-padding-left-18 {
  padding-left: 18rpx !important;
}
.u-m-t-18 {
  margin-top: 18rpx !important;
}
.u-p-t-18 {
  padding-top: 18rpx !important;
}
.u-margin-top-18 {
  margin-top: 18rpx !important;
}
.u-padding-top-18 {
  padding-top: 18rpx !important;
}
.u-m-r-18 {
  margin-right: 18rpx !important;
}
.u-p-r-18 {
  padding-right: 18rpx !important;
}
.u-margin-right-18 {
  margin-right: 18rpx !important;
}
.u-padding-right-18 {
  padding-right: 18rpx !important;
}
.u-m-b-18 {
  margin-bottom: 18rpx !important;
}
.u-p-b-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-bottom-18 {
  margin-bottom: 18rpx !important;
}
.u-padding-bottom-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-20,
.u-m-20 {
  margin: 20rpx !important;
}
.u-padding-20,
.u-p-20 {
  padding: 20rpx !important;
}
.u-m-l-20 {
  margin-left: 20rpx !important;
}
.u-p-l-20 {
  padding-left: 20rpx !important;
}
.u-margin-left-20 {
  margin-left: 20rpx !important;
}
.u-padding-left-20 {
  padding-left: 20rpx !important;
}
.u-m-t-20 {
  margin-top: 20rpx !important;
}
.u-p-t-20 {
  padding-top: 20rpx !important;
}
.u-margin-top-20 {
  margin-top: 20rpx !important;
}
.u-padding-top-20 {
  padding-top: 20rpx !important;
}
.u-m-r-20 {
  margin-right: 20rpx !important;
}
.u-p-r-20 {
  padding-right: 20rpx !important;
}
.u-margin-right-20 {
  margin-right: 20rpx !important;
}
.u-padding-right-20 {
  padding-right: 20rpx !important;
}
.u-m-b-20 {
  margin-bottom: 20rpx !important;
}
.u-p-b-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-bottom-20 {
  margin-bottom: 20rpx !important;
}
.u-padding-bottom-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-22,
.u-m-22 {
  margin: 22rpx !important;
}
.u-padding-22,
.u-p-22 {
  padding: 22rpx !important;
}
.u-m-l-22 {
  margin-left: 22rpx !important;
}
.u-p-l-22 {
  padding-left: 22rpx !important;
}
.u-margin-left-22 {
  margin-left: 22rpx !important;
}
.u-padding-left-22 {
  padding-left: 22rpx !important;
}
.u-m-t-22 {
  margin-top: 22rpx !important;
}
.u-p-t-22 {
  padding-top: 22rpx !important;
}
.u-margin-top-22 {
  margin-top: 22rpx !important;
}
.u-padding-top-22 {
  padding-top: 22rpx !important;
}
.u-m-r-22 {
  margin-right: 22rpx !important;
}
.u-p-r-22 {
  padding-right: 22rpx !important;
}
.u-margin-right-22 {
  margin-right: 22rpx !important;
}
.u-padding-right-22 {
  padding-right: 22rpx !important;
}
.u-m-b-22 {
  margin-bottom: 22rpx !important;
}
.u-p-b-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-bottom-22 {
  margin-bottom: 22rpx !important;
}
.u-padding-bottom-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-24,
.u-m-24 {
  margin: 24rpx !important;
}
.u-padding-24,
.u-p-24 {
  padding: 24rpx !important;
}
.u-m-l-24 {
  margin-left: 24rpx !important;
}
.u-p-l-24 {
  padding-left: 24rpx !important;
}
.u-margin-left-24 {
  margin-left: 24rpx !important;
}
.u-padding-left-24 {
  padding-left: 24rpx !important;
}
.u-m-t-24 {
  margin-top: 24rpx !important;
}
.u-p-t-24 {
  padding-top: 24rpx !important;
}
.u-margin-top-24 {
  margin-top: 24rpx !important;
}
.u-padding-top-24 {
  padding-top: 24rpx !important;
}
.u-m-r-24 {
  margin-right: 24rpx !important;
}
.u-p-r-24 {
  padding-right: 24rpx !important;
}
.u-margin-right-24 {
  margin-right: 24rpx !important;
}
.u-padding-right-24 {
  padding-right: 24rpx !important;
}
.u-m-b-24 {
  margin-bottom: 24rpx !important;
}
.u-p-b-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-bottom-24 {
  margin-bottom: 24rpx !important;
}
.u-padding-bottom-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-25,
.u-m-25 {
  margin: 25rpx !important;
}
.u-padding-25,
.u-p-25 {
  padding: 25rpx !important;
}
.u-m-l-25 {
  margin-left: 25rpx !important;
}
.u-p-l-25 {
  padding-left: 25rpx !important;
}
.u-margin-left-25 {
  margin-left: 25rpx !important;
}
.u-padding-left-25 {
  padding-left: 25rpx !important;
}
.u-m-t-25 {
  margin-top: 25rpx !important;
}
.u-p-t-25 {
  padding-top: 25rpx !important;
}
.u-margin-top-25 {
  margin-top: 25rpx !important;
}
.u-padding-top-25 {
  padding-top: 25rpx !important;
}
.u-m-r-25 {
  margin-right: 25rpx !important;
}
.u-p-r-25 {
  padding-right: 25rpx !important;
}
.u-margin-right-25 {
  margin-right: 25rpx !important;
}
.u-padding-right-25 {
  padding-right: 25rpx !important;
}
.u-m-b-25 {
  margin-bottom: 25rpx !important;
}
.u-p-b-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-bottom-25 {
  margin-bottom: 25rpx !important;
}
.u-padding-bottom-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-26,
.u-m-26 {
  margin: 26rpx !important;
}
.u-padding-26,
.u-p-26 {
  padding: 26rpx !important;
}
.u-m-l-26 {
  margin-left: 26rpx !important;
}
.u-p-l-26 {
  padding-left: 26rpx !important;
}
.u-margin-left-26 {
  margin-left: 26rpx !important;
}
.u-padding-left-26 {
  padding-left: 26rpx !important;
}
.u-m-t-26 {
  margin-top: 26rpx !important;
}
.u-p-t-26 {
  padding-top: 26rpx !important;
}
.u-margin-top-26 {
  margin-top: 26rpx !important;
}
.u-padding-top-26 {
  padding-top: 26rpx !important;
}
.u-m-r-26 {
  margin-right: 26rpx !important;
}
.u-p-r-26 {
  padding-right: 26rpx !important;
}
.u-margin-right-26 {
  margin-right: 26rpx !important;
}
.u-padding-right-26 {
  padding-right: 26rpx !important;
}
.u-m-b-26 {
  margin-bottom: 26rpx !important;
}
.u-p-b-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-bottom-26 {
  margin-bottom: 26rpx !important;
}
.u-padding-bottom-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-28,
.u-m-28 {
  margin: 28rpx !important;
}
.u-padding-28,
.u-p-28 {
  padding: 28rpx !important;
}
.u-m-l-28 {
  margin-left: 28rpx !important;
}
.u-p-l-28 {
  padding-left: 28rpx !important;
}
.u-margin-left-28 {
  margin-left: 28rpx !important;
}
.u-padding-left-28 {
  padding-left: 28rpx !important;
}
.u-m-t-28 {
  margin-top: 28rpx !important;
}
.u-p-t-28 {
  padding-top: 28rpx !important;
}
.u-margin-top-28 {
  margin-top: 28rpx !important;
}
.u-padding-top-28 {
  padding-top: 28rpx !important;
}
.u-m-r-28 {
  margin-right: 28rpx !important;
}
.u-p-r-28 {
  padding-right: 28rpx !important;
}
.u-margin-right-28 {
  margin-right: 28rpx !important;
}
.u-padding-right-28 {
  padding-right: 28rpx !important;
}
.u-m-b-28 {
  margin-bottom: 28rpx !important;
}
.u-p-b-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-bottom-28 {
  margin-bottom: 28rpx !important;
}
.u-padding-bottom-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-30,
.u-m-30 {
  margin: 30rpx !important;
}
.u-padding-30,
.u-p-30 {
  padding: 30rpx !important;
}
.u-m-l-30 {
  margin-left: 30rpx !important;
}
.u-p-l-30 {
  padding-left: 30rpx !important;
}
.u-margin-left-30 {
  margin-left: 30rpx !important;
}
.u-padding-left-30 {
  padding-left: 30rpx !important;
}
.u-m-t-30 {
  margin-top: 30rpx !important;
}
.u-p-t-30 {
  padding-top: 30rpx !important;
}
.u-margin-top-30 {
  margin-top: 30rpx !important;
}
.u-padding-top-30 {
  padding-top: 30rpx !important;
}
.u-m-r-30 {
  margin-right: 30rpx !important;
}
.u-p-r-30 {
  padding-right: 30rpx !important;
}
.u-margin-right-30 {
  margin-right: 30rpx !important;
}
.u-padding-right-30 {
  padding-right: 30rpx !important;
}
.u-m-b-30 {
  margin-bottom: 30rpx !important;
}
.u-p-b-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-bottom-30 {
  margin-bottom: 30rpx !important;
}
.u-padding-bottom-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-32,
.u-m-32 {
  margin: 32rpx !important;
}
.u-padding-32,
.u-p-32 {
  padding: 32rpx !important;
}
.u-m-l-32 {
  margin-left: 32rpx !important;
}
.u-p-l-32 {
  padding-left: 32rpx !important;
}
.u-margin-left-32 {
  margin-left: 32rpx !important;
}
.u-padding-left-32 {
  padding-left: 32rpx !important;
}
.u-m-t-32 {
  margin-top: 32rpx !important;
}
.u-p-t-32 {
  padding-top: 32rpx !important;
}
.u-margin-top-32 {
  margin-top: 32rpx !important;
}
.u-padding-top-32 {
  padding-top: 32rpx !important;
}
.u-m-r-32 {
  margin-right: 32rpx !important;
}
.u-p-r-32 {
  padding-right: 32rpx !important;
}
.u-margin-right-32 {
  margin-right: 32rpx !important;
}
.u-padding-right-32 {
  padding-right: 32rpx !important;
}
.u-m-b-32 {
  margin-bottom: 32rpx !important;
}
.u-p-b-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-bottom-32 {
  margin-bottom: 32rpx !important;
}
.u-padding-bottom-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-34,
.u-m-34 {
  margin: 34rpx !important;
}
.u-padding-34,
.u-p-34 {
  padding: 34rpx !important;
}
.u-m-l-34 {
  margin-left: 34rpx !important;
}
.u-p-l-34 {
  padding-left: 34rpx !important;
}
.u-margin-left-34 {
  margin-left: 34rpx !important;
}
.u-padding-left-34 {
  padding-left: 34rpx !important;
}
.u-m-t-34 {
  margin-top: 34rpx !important;
}
.u-p-t-34 {
  padding-top: 34rpx !important;
}
.u-margin-top-34 {
  margin-top: 34rpx !important;
}
.u-padding-top-34 {
  padding-top: 34rpx !important;
}
.u-m-r-34 {
  margin-right: 34rpx !important;
}
.u-p-r-34 {
  padding-right: 34rpx !important;
}
.u-margin-right-34 {
  margin-right: 34rpx !important;
}
.u-padding-right-34 {
  padding-right: 34rpx !important;
}
.u-m-b-34 {
  margin-bottom: 34rpx !important;
}
.u-p-b-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-bottom-34 {
  margin-bottom: 34rpx !important;
}
.u-padding-bottom-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-35,
.u-m-35 {
  margin: 35rpx !important;
}
.u-padding-35,
.u-p-35 {
  padding: 35rpx !important;
}
.u-m-l-35 {
  margin-left: 35rpx !important;
}
.u-p-l-35 {
  padding-left: 35rpx !important;
}
.u-margin-left-35 {
  margin-left: 35rpx !important;
}
.u-padding-left-35 {
  padding-left: 35rpx !important;
}
.u-m-t-35 {
  margin-top: 35rpx !important;
}
.u-p-t-35 {
  padding-top: 35rpx !important;
}
.u-margin-top-35 {
  margin-top: 35rpx !important;
}
.u-padding-top-35 {
  padding-top: 35rpx !important;
}
.u-m-r-35 {
  margin-right: 35rpx !important;
}
.u-p-r-35 {
  padding-right: 35rpx !important;
}
.u-margin-right-35 {
  margin-right: 35rpx !important;
}
.u-padding-right-35 {
  padding-right: 35rpx !important;
}
.u-m-b-35 {
  margin-bottom: 35rpx !important;
}
.u-p-b-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-bottom-35 {
  margin-bottom: 35rpx !important;
}
.u-padding-bottom-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-36,
.u-m-36 {
  margin: 36rpx !important;
}
.u-padding-36,
.u-p-36 {
  padding: 36rpx !important;
}
.u-m-l-36 {
  margin-left: 36rpx !important;
}
.u-p-l-36 {
  padding-left: 36rpx !important;
}
.u-margin-left-36 {
  margin-left: 36rpx !important;
}
.u-padding-left-36 {
  padding-left: 36rpx !important;
}
.u-m-t-36 {
  margin-top: 36rpx !important;
}
.u-p-t-36 {
  padding-top: 36rpx !important;
}
.u-margin-top-36 {
  margin-top: 36rpx !important;
}
.u-padding-top-36 {
  padding-top: 36rpx !important;
}
.u-m-r-36 {
  margin-right: 36rpx !important;
}
.u-p-r-36 {
  padding-right: 36rpx !important;
}
.u-margin-right-36 {
  margin-right: 36rpx !important;
}
.u-padding-right-36 {
  padding-right: 36rpx !important;
}
.u-m-b-36 {
  margin-bottom: 36rpx !important;
}
.u-p-b-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-bottom-36 {
  margin-bottom: 36rpx !important;
}
.u-padding-bottom-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-38,
.u-m-38 {
  margin: 38rpx !important;
}
.u-padding-38,
.u-p-38 {
  padding: 38rpx !important;
}
.u-m-l-38 {
  margin-left: 38rpx !important;
}
.u-p-l-38 {
  padding-left: 38rpx !important;
}
.u-margin-left-38 {
  margin-left: 38rpx !important;
}
.u-padding-left-38 {
  padding-left: 38rpx !important;
}
.u-m-t-38 {
  margin-top: 38rpx !important;
}
.u-p-t-38 {
  padding-top: 38rpx !important;
}
.u-margin-top-38 {
  margin-top: 38rpx !important;
}
.u-padding-top-38 {
  padding-top: 38rpx !important;
}
.u-m-r-38 {
  margin-right: 38rpx !important;
}
.u-p-r-38 {
  padding-right: 38rpx !important;
}
.u-margin-right-38 {
  margin-right: 38rpx !important;
}
.u-padding-right-38 {
  padding-right: 38rpx !important;
}
.u-m-b-38 {
  margin-bottom: 38rpx !important;
}
.u-p-b-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-bottom-38 {
  margin-bottom: 38rpx !important;
}
.u-padding-bottom-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-40,
.u-m-40 {
  margin: 40rpx !important;
}
.u-padding-40,
.u-p-40 {
  padding: 40rpx !important;
}
.u-m-l-40 {
  margin-left: 40rpx !important;
}
.u-p-l-40 {
  padding-left: 40rpx !important;
}
.u-margin-left-40 {
  margin-left: 40rpx !important;
}
.u-padding-left-40 {
  padding-left: 40rpx !important;
}
.u-m-t-40 {
  margin-top: 40rpx !important;
}
.u-p-t-40 {
  padding-top: 40rpx !important;
}
.u-margin-top-40 {
  margin-top: 40rpx !important;
}
.u-padding-top-40 {
  padding-top: 40rpx !important;
}
.u-m-r-40 {
  margin-right: 40rpx !important;
}
.u-p-r-40 {
  padding-right: 40rpx !important;
}
.u-margin-right-40 {
  margin-right: 40rpx !important;
}
.u-padding-right-40 {
  padding-right: 40rpx !important;
}
.u-m-b-40 {
  margin-bottom: 40rpx !important;
}
.u-p-b-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-bottom-40 {
  margin-bottom: 40rpx !important;
}
.u-padding-bottom-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-42,
.u-m-42 {
  margin: 42rpx !important;
}
.u-padding-42,
.u-p-42 {
  padding: 42rpx !important;
}
.u-m-l-42 {
  margin-left: 42rpx !important;
}
.u-p-l-42 {
  padding-left: 42rpx !important;
}
.u-margin-left-42 {
  margin-left: 42rpx !important;
}
.u-padding-left-42 {
  padding-left: 42rpx !important;
}
.u-m-t-42 {
  margin-top: 42rpx !important;
}
.u-p-t-42 {
  padding-top: 42rpx !important;
}
.u-margin-top-42 {
  margin-top: 42rpx !important;
}
.u-padding-top-42 {
  padding-top: 42rpx !important;
}
.u-m-r-42 {
  margin-right: 42rpx !important;
}
.u-p-r-42 {
  padding-right: 42rpx !important;
}
.u-margin-right-42 {
  margin-right: 42rpx !important;
}
.u-padding-right-42 {
  padding-right: 42rpx !important;
}
.u-m-b-42 {
  margin-bottom: 42rpx !important;
}
.u-p-b-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-bottom-42 {
  margin-bottom: 42rpx !important;
}
.u-padding-bottom-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-44,
.u-m-44 {
  margin: 44rpx !important;
}
.u-padding-44,
.u-p-44 {
  padding: 44rpx !important;
}
.u-m-l-44 {
  margin-left: 44rpx !important;
}
.u-p-l-44 {
  padding-left: 44rpx !important;
}
.u-margin-left-44 {
  margin-left: 44rpx !important;
}
.u-padding-left-44 {
  padding-left: 44rpx !important;
}
.u-m-t-44 {
  margin-top: 44rpx !important;
}
.u-p-t-44 {
  padding-top: 44rpx !important;
}
.u-margin-top-44 {
  margin-top: 44rpx !important;
}
.u-padding-top-44 {
  padding-top: 44rpx !important;
}
.u-m-r-44 {
  margin-right: 44rpx !important;
}
.u-p-r-44 {
  padding-right: 44rpx !important;
}
.u-margin-right-44 {
  margin-right: 44rpx !important;
}
.u-padding-right-44 {
  padding-right: 44rpx !important;
}
.u-m-b-44 {
  margin-bottom: 44rpx !important;
}
.u-p-b-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-bottom-44 {
  margin-bottom: 44rpx !important;
}
.u-padding-bottom-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-45,
.u-m-45 {
  margin: 45rpx !important;
}
.u-padding-45,
.u-p-45 {
  padding: 45rpx !important;
}
.u-m-l-45 {
  margin-left: 45rpx !important;
}
.u-p-l-45 {
  padding-left: 45rpx !important;
}
.u-margin-left-45 {
  margin-left: 45rpx !important;
}
.u-padding-left-45 {
  padding-left: 45rpx !important;
}
.u-m-t-45 {
  margin-top: 45rpx !important;
}
.u-p-t-45 {
  padding-top: 45rpx !important;
}
.u-margin-top-45 {
  margin-top: 45rpx !important;
}
.u-padding-top-45 {
  padding-top: 45rpx !important;
}
.u-m-r-45 {
  margin-right: 45rpx !important;
}
.u-p-r-45 {
  padding-right: 45rpx !important;
}
.u-margin-right-45 {
  margin-right: 45rpx !important;
}
.u-padding-right-45 {
  padding-right: 45rpx !important;
}
.u-m-b-45 {
  margin-bottom: 45rpx !important;
}
.u-p-b-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-bottom-45 {
  margin-bottom: 45rpx !important;
}
.u-padding-bottom-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-46,
.u-m-46 {
  margin: 46rpx !important;
}
.u-padding-46,
.u-p-46 {
  padding: 46rpx !important;
}
.u-m-l-46 {
  margin-left: 46rpx !important;
}
.u-p-l-46 {
  padding-left: 46rpx !important;
}
.u-margin-left-46 {
  margin-left: 46rpx !important;
}
.u-padding-left-46 {
  padding-left: 46rpx !important;
}
.u-m-t-46 {
  margin-top: 46rpx !important;
}
.u-p-t-46 {
  padding-top: 46rpx !important;
}
.u-margin-top-46 {
  margin-top: 46rpx !important;
}
.u-padding-top-46 {
  padding-top: 46rpx !important;
}
.u-m-r-46 {
  margin-right: 46rpx !important;
}
.u-p-r-46 {
  padding-right: 46rpx !important;
}
.u-margin-right-46 {
  margin-right: 46rpx !important;
}
.u-padding-right-46 {
  padding-right: 46rpx !important;
}
.u-m-b-46 {
  margin-bottom: 46rpx !important;
}
.u-p-b-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-bottom-46 {
  margin-bottom: 46rpx !important;
}
.u-padding-bottom-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-48,
.u-m-48 {
  margin: 48rpx !important;
}
.u-padding-48,
.u-p-48 {
  padding: 48rpx !important;
}
.u-m-l-48 {
  margin-left: 48rpx !important;
}
.u-p-l-48 {
  padding-left: 48rpx !important;
}
.u-margin-left-48 {
  margin-left: 48rpx !important;
}
.u-padding-left-48 {
  padding-left: 48rpx !important;
}
.u-m-t-48 {
  margin-top: 48rpx !important;
}
.u-p-t-48 {
  padding-top: 48rpx !important;
}
.u-margin-top-48 {
  margin-top: 48rpx !important;
}
.u-padding-top-48 {
  padding-top: 48rpx !important;
}
.u-m-r-48 {
  margin-right: 48rpx !important;
}
.u-p-r-48 {
  padding-right: 48rpx !important;
}
.u-margin-right-48 {
  margin-right: 48rpx !important;
}
.u-padding-right-48 {
  padding-right: 48rpx !important;
}
.u-m-b-48 {
  margin-bottom: 48rpx !important;
}
.u-p-b-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-bottom-48 {
  margin-bottom: 48rpx !important;
}
.u-padding-bottom-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-50,
.u-m-50 {
  margin: 50rpx !important;
}
.u-padding-50,
.u-p-50 {
  padding: 50rpx !important;
}
.u-m-l-50 {
  margin-left: 50rpx !important;
}
.u-p-l-50 {
  padding-left: 50rpx !important;
}
.u-margin-left-50 {
  margin-left: 50rpx !important;
}
.u-padding-left-50 {
  padding-left: 50rpx !important;
}
.u-m-t-50 {
  margin-top: 50rpx !important;
}
.u-p-t-50 {
  padding-top: 50rpx !important;
}
.u-margin-top-50 {
  margin-top: 50rpx !important;
}
.u-padding-top-50 {
  padding-top: 50rpx !important;
}
.u-m-r-50 {
  margin-right: 50rpx !important;
}
.u-p-r-50 {
  padding-right: 50rpx !important;
}
.u-margin-right-50 {
  margin-right: 50rpx !important;
}
.u-padding-right-50 {
  padding-right: 50rpx !important;
}
.u-m-b-50 {
  margin-bottom: 50rpx !important;
}
.u-p-b-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-bottom-50 {
  margin-bottom: 50rpx !important;
}
.u-padding-bottom-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-52,
.u-m-52 {
  margin: 52rpx !important;
}
.u-padding-52,
.u-p-52 {
  padding: 52rpx !important;
}
.u-m-l-52 {
  margin-left: 52rpx !important;
}
.u-p-l-52 {
  padding-left: 52rpx !important;
}
.u-margin-left-52 {
  margin-left: 52rpx !important;
}
.u-padding-left-52 {
  padding-left: 52rpx !important;
}
.u-m-t-52 {
  margin-top: 52rpx !important;
}
.u-p-t-52 {
  padding-top: 52rpx !important;
}
.u-margin-top-52 {
  margin-top: 52rpx !important;
}
.u-padding-top-52 {
  padding-top: 52rpx !important;
}
.u-m-r-52 {
  margin-right: 52rpx !important;
}
.u-p-r-52 {
  padding-right: 52rpx !important;
}
.u-margin-right-52 {
  margin-right: 52rpx !important;
}
.u-padding-right-52 {
  padding-right: 52rpx !important;
}
.u-m-b-52 {
  margin-bottom: 52rpx !important;
}
.u-p-b-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-bottom-52 {
  margin-bottom: 52rpx !important;
}
.u-padding-bottom-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-54,
.u-m-54 {
  margin: 54rpx !important;
}
.u-padding-54,
.u-p-54 {
  padding: 54rpx !important;
}
.u-m-l-54 {
  margin-left: 54rpx !important;
}
.u-p-l-54 {
  padding-left: 54rpx !important;
}
.u-margin-left-54 {
  margin-left: 54rpx !important;
}
.u-padding-left-54 {
  padding-left: 54rpx !important;
}
.u-m-t-54 {
  margin-top: 54rpx !important;
}
.u-p-t-54 {
  padding-top: 54rpx !important;
}
.u-margin-top-54 {
  margin-top: 54rpx !important;
}
.u-padding-top-54 {
  padding-top: 54rpx !important;
}
.u-m-r-54 {
  margin-right: 54rpx !important;
}
.u-p-r-54 {
  padding-right: 54rpx !important;
}
.u-margin-right-54 {
  margin-right: 54rpx !important;
}
.u-padding-right-54 {
  padding-right: 54rpx !important;
}
.u-m-b-54 {
  margin-bottom: 54rpx !important;
}
.u-p-b-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-bottom-54 {
  margin-bottom: 54rpx !important;
}
.u-padding-bottom-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-55,
.u-m-55 {
  margin: 55rpx !important;
}
.u-padding-55,
.u-p-55 {
  padding: 55rpx !important;
}
.u-m-l-55 {
  margin-left: 55rpx !important;
}
.u-p-l-55 {
  padding-left: 55rpx !important;
}
.u-margin-left-55 {
  margin-left: 55rpx !important;
}
.u-padding-left-55 {
  padding-left: 55rpx !important;
}
.u-m-t-55 {
  margin-top: 55rpx !important;
}
.u-p-t-55 {
  padding-top: 55rpx !important;
}
.u-margin-top-55 {
  margin-top: 55rpx !important;
}
.u-padding-top-55 {
  padding-top: 55rpx !important;
}
.u-m-r-55 {
  margin-right: 55rpx !important;
}
.u-p-r-55 {
  padding-right: 55rpx !important;
}
.u-margin-right-55 {
  margin-right: 55rpx !important;
}
.u-padding-right-55 {
  padding-right: 55rpx !important;
}
.u-m-b-55 {
  margin-bottom: 55rpx !important;
}
.u-p-b-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-bottom-55 {
  margin-bottom: 55rpx !important;
}
.u-padding-bottom-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-56,
.u-m-56 {
  margin: 56rpx !important;
}
.u-padding-56,
.u-p-56 {
  padding: 56rpx !important;
}
.u-m-l-56 {
  margin-left: 56rpx !important;
}
.u-p-l-56 {
  padding-left: 56rpx !important;
}
.u-margin-left-56 {
  margin-left: 56rpx !important;
}
.u-padding-left-56 {
  padding-left: 56rpx !important;
}
.u-m-t-56 {
  margin-top: 56rpx !important;
}
.u-p-t-56 {
  padding-top: 56rpx !important;
}
.u-margin-top-56 {
  margin-top: 56rpx !important;
}
.u-padding-top-56 {
  padding-top: 56rpx !important;
}
.u-m-r-56 {
  margin-right: 56rpx !important;
}
.u-p-r-56 {
  padding-right: 56rpx !important;
}
.u-margin-right-56 {
  margin-right: 56rpx !important;
}
.u-padding-right-56 {
  padding-right: 56rpx !important;
}
.u-m-b-56 {
  margin-bottom: 56rpx !important;
}
.u-p-b-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-bottom-56 {
  margin-bottom: 56rpx !important;
}
.u-padding-bottom-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-58,
.u-m-58 {
  margin: 58rpx !important;
}
.u-padding-58,
.u-p-58 {
  padding: 58rpx !important;
}
.u-m-l-58 {
  margin-left: 58rpx !important;
}
.u-p-l-58 {
  padding-left: 58rpx !important;
}
.u-margin-left-58 {
  margin-left: 58rpx !important;
}
.u-padding-left-58 {
  padding-left: 58rpx !important;
}
.u-m-t-58 {
  margin-top: 58rpx !important;
}
.u-p-t-58 {
  padding-top: 58rpx !important;
}
.u-margin-top-58 {
  margin-top: 58rpx !important;
}
.u-padding-top-58 {
  padding-top: 58rpx !important;
}
.u-m-r-58 {
  margin-right: 58rpx !important;
}
.u-p-r-58 {
  padding-right: 58rpx !important;
}
.u-margin-right-58 {
  margin-right: 58rpx !important;
}
.u-padding-right-58 {
  padding-right: 58rpx !important;
}
.u-m-b-58 {
  margin-bottom: 58rpx !important;
}
.u-p-b-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-bottom-58 {
  margin-bottom: 58rpx !important;
}
.u-padding-bottom-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-60,
.u-m-60 {
  margin: 60rpx !important;
}
.u-padding-60,
.u-p-60 {
  padding: 60rpx !important;
}
.u-m-l-60 {
  margin-left: 60rpx !important;
}
.u-p-l-60 {
  padding-left: 60rpx !important;
}
.u-margin-left-60 {
  margin-left: 60rpx !important;
}
.u-padding-left-60 {
  padding-left: 60rpx !important;
}
.u-m-t-60 {
  margin-top: 60rpx !important;
}
.u-p-t-60 {
  padding-top: 60rpx !important;
}
.u-margin-top-60 {
  margin-top: 60rpx !important;
}
.u-padding-top-60 {
  padding-top: 60rpx !important;
}
.u-m-r-60 {
  margin-right: 60rpx !important;
}
.u-p-r-60 {
  padding-right: 60rpx !important;
}
.u-margin-right-60 {
  margin-right: 60rpx !important;
}
.u-padding-right-60 {
  padding-right: 60rpx !important;
}
.u-m-b-60 {
  margin-bottom: 60rpx !important;
}
.u-p-b-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-bottom-60 {
  margin-bottom: 60rpx !important;
}
.u-padding-bottom-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-62,
.u-m-62 {
  margin: 62rpx !important;
}
.u-padding-62,
.u-p-62 {
  padding: 62rpx !important;
}
.u-m-l-62 {
  margin-left: 62rpx !important;
}
.u-p-l-62 {
  padding-left: 62rpx !important;
}
.u-margin-left-62 {
  margin-left: 62rpx !important;
}
.u-padding-left-62 {
  padding-left: 62rpx !important;
}
.u-m-t-62 {
  margin-top: 62rpx !important;
}
.u-p-t-62 {
  padding-top: 62rpx !important;
}
.u-margin-top-62 {
  margin-top: 62rpx !important;
}
.u-padding-top-62 {
  padding-top: 62rpx !important;
}
.u-m-r-62 {
  margin-right: 62rpx !important;
}
.u-p-r-62 {
  padding-right: 62rpx !important;
}
.u-margin-right-62 {
  margin-right: 62rpx !important;
}
.u-padding-right-62 {
  padding-right: 62rpx !important;
}
.u-m-b-62 {
  margin-bottom: 62rpx !important;
}
.u-p-b-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-bottom-62 {
  margin-bottom: 62rpx !important;
}
.u-padding-bottom-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-64,
.u-m-64 {
  margin: 64rpx !important;
}
.u-padding-64,
.u-p-64 {
  padding: 64rpx !important;
}
.u-m-l-64 {
  margin-left: 64rpx !important;
}
.u-p-l-64 {
  padding-left: 64rpx !important;
}
.u-margin-left-64 {
  margin-left: 64rpx !important;
}
.u-padding-left-64 {
  padding-left: 64rpx !important;
}
.u-m-t-64 {
  margin-top: 64rpx !important;
}
.u-p-t-64 {
  padding-top: 64rpx !important;
}
.u-margin-top-64 {
  margin-top: 64rpx !important;
}
.u-padding-top-64 {
  padding-top: 64rpx !important;
}
.u-m-r-64 {
  margin-right: 64rpx !important;
}
.u-p-r-64 {
  padding-right: 64rpx !important;
}
.u-margin-right-64 {
  margin-right: 64rpx !important;
}
.u-padding-right-64 {
  padding-right: 64rpx !important;
}
.u-m-b-64 {
  margin-bottom: 64rpx !important;
}
.u-p-b-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-bottom-64 {
  margin-bottom: 64rpx !important;
}
.u-padding-bottom-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-65,
.u-m-65 {
  margin: 65rpx !important;
}
.u-padding-65,
.u-p-65 {
  padding: 65rpx !important;
}
.u-m-l-65 {
  margin-left: 65rpx !important;
}
.u-p-l-65 {
  padding-left: 65rpx !important;
}
.u-margin-left-65 {
  margin-left: 65rpx !important;
}
.u-padding-left-65 {
  padding-left: 65rpx !important;
}
.u-m-t-65 {
  margin-top: 65rpx !important;
}
.u-p-t-65 {
  padding-top: 65rpx !important;
}
.u-margin-top-65 {
  margin-top: 65rpx !important;
}
.u-padding-top-65 {
  padding-top: 65rpx !important;
}
.u-m-r-65 {
  margin-right: 65rpx !important;
}
.u-p-r-65 {
  padding-right: 65rpx !important;
}
.u-margin-right-65 {
  margin-right: 65rpx !important;
}
.u-padding-right-65 {
  padding-right: 65rpx !important;
}
.u-m-b-65 {
  margin-bottom: 65rpx !important;
}
.u-p-b-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-bottom-65 {
  margin-bottom: 65rpx !important;
}
.u-padding-bottom-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-66,
.u-m-66 {
  margin: 66rpx !important;
}
.u-padding-66,
.u-p-66 {
  padding: 66rpx !important;
}
.u-m-l-66 {
  margin-left: 66rpx !important;
}
.u-p-l-66 {
  padding-left: 66rpx !important;
}
.u-margin-left-66 {
  margin-left: 66rpx !important;
}
.u-padding-left-66 {
  padding-left: 66rpx !important;
}
.u-m-t-66 {
  margin-top: 66rpx !important;
}
.u-p-t-66 {
  padding-top: 66rpx !important;
}
.u-margin-top-66 {
  margin-top: 66rpx !important;
}
.u-padding-top-66 {
  padding-top: 66rpx !important;
}
.u-m-r-66 {
  margin-right: 66rpx !important;
}
.u-p-r-66 {
  padding-right: 66rpx !important;
}
.u-margin-right-66 {
  margin-right: 66rpx !important;
}
.u-padding-right-66 {
  padding-right: 66rpx !important;
}
.u-m-b-66 {
  margin-bottom: 66rpx !important;
}
.u-p-b-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-bottom-66 {
  margin-bottom: 66rpx !important;
}
.u-padding-bottom-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-68,
.u-m-68 {
  margin: 68rpx !important;
}
.u-padding-68,
.u-p-68 {
  padding: 68rpx !important;
}
.u-m-l-68 {
  margin-left: 68rpx !important;
}
.u-p-l-68 {
  padding-left: 68rpx !important;
}
.u-margin-left-68 {
  margin-left: 68rpx !important;
}
.u-padding-left-68 {
  padding-left: 68rpx !important;
}
.u-m-t-68 {
  margin-top: 68rpx !important;
}
.u-p-t-68 {
  padding-top: 68rpx !important;
}
.u-margin-top-68 {
  margin-top: 68rpx !important;
}
.u-padding-top-68 {
  padding-top: 68rpx !important;
}
.u-m-r-68 {
  margin-right: 68rpx !important;
}
.u-p-r-68 {
  padding-right: 68rpx !important;
}
.u-margin-right-68 {
  margin-right: 68rpx !important;
}
.u-padding-right-68 {
  padding-right: 68rpx !important;
}
.u-m-b-68 {
  margin-bottom: 68rpx !important;
}
.u-p-b-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-bottom-68 {
  margin-bottom: 68rpx !important;
}
.u-padding-bottom-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-70,
.u-m-70 {
  margin: 70rpx !important;
}
.u-padding-70,
.u-p-70 {
  padding: 70rpx !important;
}
.u-m-l-70 {
  margin-left: 70rpx !important;
}
.u-p-l-70 {
  padding-left: 70rpx !important;
}
.u-margin-left-70 {
  margin-left: 70rpx !important;
}
.u-padding-left-70 {
  padding-left: 70rpx !important;
}
.u-m-t-70 {
  margin-top: 70rpx !important;
}
.u-p-t-70 {
  padding-top: 70rpx !important;
}
.u-margin-top-70 {
  margin-top: 70rpx !important;
}
.u-padding-top-70 {
  padding-top: 70rpx !important;
}
.u-m-r-70 {
  margin-right: 70rpx !important;
}
.u-p-r-70 {
  padding-right: 70rpx !important;
}
.u-margin-right-70 {
  margin-right: 70rpx !important;
}
.u-padding-right-70 {
  padding-right: 70rpx !important;
}
.u-m-b-70 {
  margin-bottom: 70rpx !important;
}
.u-p-b-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-bottom-70 {
  margin-bottom: 70rpx !important;
}
.u-padding-bottom-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-72,
.u-m-72 {
  margin: 72rpx !important;
}
.u-padding-72,
.u-p-72 {
  padding: 72rpx !important;
}
.u-m-l-72 {
  margin-left: 72rpx !important;
}
.u-p-l-72 {
  padding-left: 72rpx !important;
}
.u-margin-left-72 {
  margin-left: 72rpx !important;
}
.u-padding-left-72 {
  padding-left: 72rpx !important;
}
.u-m-t-72 {
  margin-top: 72rpx !important;
}
.u-p-t-72 {
  padding-top: 72rpx !important;
}
.u-margin-top-72 {
  margin-top: 72rpx !important;
}
.u-padding-top-72 {
  padding-top: 72rpx !important;
}
.u-m-r-72 {
  margin-right: 72rpx !important;
}
.u-p-r-72 {
  padding-right: 72rpx !important;
}
.u-margin-right-72 {
  margin-right: 72rpx !important;
}
.u-padding-right-72 {
  padding-right: 72rpx !important;
}
.u-m-b-72 {
  margin-bottom: 72rpx !important;
}
.u-p-b-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-bottom-72 {
  margin-bottom: 72rpx !important;
}
.u-padding-bottom-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-74,
.u-m-74 {
  margin: 74rpx !important;
}
.u-padding-74,
.u-p-74 {
  padding: 74rpx !important;
}
.u-m-l-74 {
  margin-left: 74rpx !important;
}
.u-p-l-74 {
  padding-left: 74rpx !important;
}
.u-margin-left-74 {
  margin-left: 74rpx !important;
}
.u-padding-left-74 {
  padding-left: 74rpx !important;
}
.u-m-t-74 {
  margin-top: 74rpx !important;
}
.u-p-t-74 {
  padding-top: 74rpx !important;
}
.u-margin-top-74 {
  margin-top: 74rpx !important;
}
.u-padding-top-74 {
  padding-top: 74rpx !important;
}
.u-m-r-74 {
  margin-right: 74rpx !important;
}
.u-p-r-74 {
  padding-right: 74rpx !important;
}
.u-margin-right-74 {
  margin-right: 74rpx !important;
}
.u-padding-right-74 {
  padding-right: 74rpx !important;
}
.u-m-b-74 {
  margin-bottom: 74rpx !important;
}
.u-p-b-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-bottom-74 {
  margin-bottom: 74rpx !important;
}
.u-padding-bottom-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-75,
.u-m-75 {
  margin: 75rpx !important;
}
.u-padding-75,
.u-p-75 {
  padding: 75rpx !important;
}
.u-m-l-75 {
  margin-left: 75rpx !important;
}
.u-p-l-75 {
  padding-left: 75rpx !important;
}
.u-margin-left-75 {
  margin-left: 75rpx !important;
}
.u-padding-left-75 {
  padding-left: 75rpx !important;
}
.u-m-t-75 {
  margin-top: 75rpx !important;
}
.u-p-t-75 {
  padding-top: 75rpx !important;
}
.u-margin-top-75 {
  margin-top: 75rpx !important;
}
.u-padding-top-75 {
  padding-top: 75rpx !important;
}
.u-m-r-75 {
  margin-right: 75rpx !important;
}
.u-p-r-75 {
  padding-right: 75rpx !important;
}
.u-margin-right-75 {
  margin-right: 75rpx !important;
}
.u-padding-right-75 {
  padding-right: 75rpx !important;
}
.u-m-b-75 {
  margin-bottom: 75rpx !important;
}
.u-p-b-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-bottom-75 {
  margin-bottom: 75rpx !important;
}
.u-padding-bottom-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-76,
.u-m-76 {
  margin: 76rpx !important;
}
.u-padding-76,
.u-p-76 {
  padding: 76rpx !important;
}
.u-m-l-76 {
  margin-left: 76rpx !important;
}
.u-p-l-76 {
  padding-left: 76rpx !important;
}
.u-margin-left-76 {
  margin-left: 76rpx !important;
}
.u-padding-left-76 {
  padding-left: 76rpx !important;
}
.u-m-t-76 {
  margin-top: 76rpx !important;
}
.u-p-t-76 {
  padding-top: 76rpx !important;
}
.u-margin-top-76 {
  margin-top: 76rpx !important;
}
.u-padding-top-76 {
  padding-top: 76rpx !important;
}
.u-m-r-76 {
  margin-right: 76rpx !important;
}
.u-p-r-76 {
  padding-right: 76rpx !important;
}
.u-margin-right-76 {
  margin-right: 76rpx !important;
}
.u-padding-right-76 {
  padding-right: 76rpx !important;
}
.u-m-b-76 {
  margin-bottom: 76rpx !important;
}
.u-p-b-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-bottom-76 {
  margin-bottom: 76rpx !important;
}
.u-padding-bottom-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-78,
.u-m-78 {
  margin: 78rpx !important;
}
.u-padding-78,
.u-p-78 {
  padding: 78rpx !important;
}
.u-m-l-78 {
  margin-left: 78rpx !important;
}
.u-p-l-78 {
  padding-left: 78rpx !important;
}
.u-margin-left-78 {
  margin-left: 78rpx !important;
}
.u-padding-left-78 {
  padding-left: 78rpx !important;
}
.u-m-t-78 {
  margin-top: 78rpx !important;
}
.u-p-t-78 {
  padding-top: 78rpx !important;
}
.u-margin-top-78 {
  margin-top: 78rpx !important;
}
.u-padding-top-78 {
  padding-top: 78rpx !important;
}
.u-m-r-78 {
  margin-right: 78rpx !important;
}
.u-p-r-78 {
  padding-right: 78rpx !important;
}
.u-margin-right-78 {
  margin-right: 78rpx !important;
}
.u-padding-right-78 {
  padding-right: 78rpx !important;
}
.u-m-b-78 {
  margin-bottom: 78rpx !important;
}
.u-p-b-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-bottom-78 {
  margin-bottom: 78rpx !important;
}
.u-padding-bottom-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-80,
.u-m-80 {
  margin: 80rpx !important;
}
.u-padding-80,
.u-p-80 {
  padding: 80rpx !important;
}
.u-m-l-80 {
  margin-left: 80rpx !important;
}
.u-p-l-80 {
  padding-left: 80rpx !important;
}
.u-margin-left-80 {
  margin-left: 80rpx !important;
}
.u-padding-left-80 {
  padding-left: 80rpx !important;
}
.u-m-t-80 {
  margin-top: 80rpx !important;
}
.u-p-t-80 {
  padding-top: 80rpx !important;
}
.u-margin-top-80 {
  margin-top: 80rpx !important;
}
.u-padding-top-80 {
  padding-top: 80rpx !important;
}
.u-m-r-80 {
  margin-right: 80rpx !important;
}
.u-p-r-80 {
  padding-right: 80rpx !important;
}
.u-margin-right-80 {
  margin-right: 80rpx !important;
}
.u-padding-right-80 {
  padding-right: 80rpx !important;
}
.u-m-b-80 {
  margin-bottom: 80rpx !important;
}
.u-p-b-80 {
  padding-bottom: 80rpx !important;
}
.u-margin-bottom-80 {
  margin-bottom: 80rpx !important;
}
.u-padding-bottom-80 {
  padding-bottom: 80rpx !important;
}
.h-100 {
  height: 100%;
}
.w-100 {
  width: 100%;
}
.bg-fff {
  background-color: #fff;
}
.placeholder, .placeholder-w-100, .placeholder-h-100, .placeholder-w-5, .placeholder-h-5, .placeholder-w-4, .placeholder-h-4, .placeholder-w-3, .placeholder-h-3, .placeholder-w-2, .placeholder-h-2, .placeholder-w-1, .placeholder-h-1 {
  min-height: 30rpx;
  background: linear-gradient(90deg, #f1f2f4 25%, #e6e6e6 37%, #f1f2f4 50%);
  background-size: 400% 100%;
  border-radius: 3px;
  -webkit-animation: skeleton 1.8s ease infinite;
          animation: skeleton 1.8s ease infinite;
}
@-webkit-keyframes skeleton {
0% {
    background-position: 100% 50%;
}
100% {
    background-position: 0 50%;
}
}
@keyframes skeleton {
0% {
    background-position: 100% 50%;
}
100% {
    background-position: 0 50%;
}
}
.overflow-hidden {
  overflow: hidden;
}
.placeholder-h-1 {
  height: 20%;
}
.placeholder-w-1 {
  width: 20%;
}
.border-8 {
  border-radius: 8rpx;
}
.placeholder-h-2 {
  height: 40%;
}
.placeholder-w-2 {
  width: 40%;
}
.border-16 {
  border-radius: 16rpx;
}
.placeholder-h-3 {
  height: 60%;
}
.placeholder-w-3 {
  width: 60%;
}
.border-24 {
  border-radius: 24rpx;
}
.placeholder-h-4 {
  height: 80%;
}
.placeholder-w-4 {
  width: 80%;
}
.border-32 {
  border-radius: 32rpx;
}
.placeholder-h-5 {
  height: 100%;
}
.placeholder-w-5 {
  width: 100%;
}
.border-40 {
  border-radius: 40rpx;
}
.placeholder-h-100 {
  min-height: 100%;
}
.placeholder-w-100 {
  min-width: 100%;
}
/* 文字超出截断隐藏 */
.clamp {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.clamp-2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.clamp-3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
.shake {
  -webkit-animation: shake 0.5s;
          animation: shake 0.5s;
}
@-webkit-keyframes shake {
0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
10% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
20% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
30% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
40% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
50% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
60% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
70% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
80% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
90% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
}
@keyframes shake {
0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
10% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
20% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
30% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
40% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
50% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
60% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
70% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
80% {
    -webkit-transform: translate(5px, 0);
            transform: translate(5px, 0);
}
90% {
    -webkit-transform: translate(-5px, 0);
            transform: translate(-5px, 0);
}
100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
}
