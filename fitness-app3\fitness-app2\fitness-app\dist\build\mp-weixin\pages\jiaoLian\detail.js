(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/jiaoLian/detail"],{58862:function(t,e,s){"use strict";s.r(e),s.d(e,{__esModule:function(){return u.__esModule},default:function(){return m}});var n,o={uLoadingPage:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-loading-page/u-loading-page")]).then(s.bind(s,51873))},uSticky:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-sticky/u-sticky")]).then(s.bind(s,38037))},uIcon:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(s.bind(s,78278))},uEmpty:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-empty/u-empty")]).then(s.bind(s,72683))},uLoadingIcon:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(s.bind(s,94597))},uButton:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-button/u-button")]).then(s.bind(s,65610))},uPopup:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(s.bind(s,85432))},uSearch:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-search/u-search")]).then(s.bind(s,9450))},uList:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-list/u-list")]).then(s.bind(s,60312))},uListItem:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-list-item/u-list-item")]).then(s.bind(s,75539))},uCell:function(){return Promise.all([s.e("common/vendor"),s.e("node-modules/uview-ui/components/u-cell/u-cell")]).then(s.bind(s,68675))}},i=function(){var t=this,e=t.$createElement,s=(t._self._c,t.$hasSSP("71dead74-1")),n=s?t._f("Img")(t.detail.background):null,o=s?t._f("Img")(t.detail.coachAvatar):null,i=s?t.__map(t.returnSpecialty(t.detail.qualification),(function(e,s){var n=t.__get_orig(e),o=t._f("Img")(e);return{$orig:n,f2:o}})):null,a=s?t.__map(t.siJiaoKeList,(function(e,s){var n=t.__get_orig(e),o=t.siJiaoKeList.length,i=o>0&&e.cover?t._f("Img")(e.cover):null;return{$orig:n,g0:o,f3:i}})):null,u=s?t.courses.length:null,c=s&&null!==t.currentIndex&&!t.isCoach?t.$getSSP("71dead74-1","content"):null,d=s&&null!==t.currentIndex&&!t.isCoach&&null!==t.currentIndex?t.$getSSP("71dead74-1","content"):null,r=s&&null!==t.currentIndex&&t.isCoach&&null!==t.currentIndex?t.$getSSP("71dead74-1","content"):null;t._isMounted||(t.e0=function(e,s){var n=arguments[arguments.length-1].currentTarget.dataset,o=n.eventParams||n["event-params"];s=o.i;return t.previewImage(s)},t.e1=function(e){t.showUserSelect=!1}),t.$mp.data=Object.assign({},{$root:{m0:s,f0:n,f1:o,l0:i,l1:a,g1:u,m1:c,m2:d,m3:r}})},a=[],u=s(94883),c=u["default"],d=s(63130),r=s.n(d),l=(r(),s(18535)),h=(0,l["default"])(c,i,a,!1,null,null,null,!1,o,n),m=h.exports},63130:function(){},75300:function(t,e,s){"use strict";var n=s(51372)["default"],o=s(81715)["createPage"];s(96910);a(s(923));var i=a(s(58862));function a(t){return t&&t.__esModule?t:{default:t}}n.__webpack_require_UNI_MP_PLUGIN__=s,o(i.default)},94883:function(t,e,s){"use strict";var n=s(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;s(77020);var o=i(s(68466));function i(t){return t&&t.__esModule?t:{default:t}}var a=function(){s.e("components/calendar").then(function(){return resolve(s(21877))}.bind(null,s))["catch"](s.oe)},u=function(){s.e("components/official-qrcode").then(function(){return resolve(s(65602))}.bind(null,s))["catch"](s.oe)};e["default"]={components:{calendar:a,officialQrcode:u},data:function(){return{disabled:!1,allindex:-1,courseId:"",courseName:"",memberId:"",memberName:"",shopId:"",shopName:"",loading:!1,activeDate:"",currentIndex:null,choseKeCheng:-1,list:[],courses:[{text:"00:00",id:1,status:2},{text:"00:30",id:2,status:3},{text:"01:00",id:3,status:1},{text:"01:30",id:4,status:1},{text:"02:00",id:5,status:1},{text:"02:30",id:6,status:1},{text:"03:00",id:7,status:1},{text:"03:30",id:8,status:1},{text:"04:00",id:9,status:1},{text:"04:30",id:10,status:1},{text:"05:00",id:11,status:1},{text:"05:30",id:12,status:1},{text:"06:00",id:13,status:1},{text:"06:30",id:14,status:1},{text:"07:00",id:15,status:1},{text:"07:30",id:16,status:1},{text:"08:00",id:17,status:1},{text:"08:30",id:18,status:1},{text:"09:00",id:19,status:1},{text:"09:30",id:20,status:1},{text:"10:00",id:21,status:1},{text:"10:30",id:22,status:1},{text:"11:00",id:23,status:1},{text:"11:30",id:24,status:1},{text:"12:00",id:25,status:1},{text:"12:30",id:26,status:1},{text:"13:00",id:27,status:1},{text:"13:30",id:28,status:1},{text:"14:00",id:29,status:1},{text:"14:30",id:30,status:1},{text:"15:00",id:31,status:1},{text:"15:30",id:32,status:1},{text:"16:00",id:33,status:1},{text:"16:30",id:34,status:1},{text:"17:00",id:35,status:1},{text:"17:30",id:36,status:1},{text:"18:00",id:37,status:1},{text:"18:30",id:38,status:1},{text:"19:00",id:39,status:1},{text:"19:30",id:40,status:1},{text:"20:00",id:41,status:1},{text:"20:30",id:42,status:1},{text:"21:00",id:43,status:1},{text:"21:30",id:44,status:1},{text:"22:00",id:45,status:1},{text:"22:30",id:46,status:1},{text:"23:00",id:47,status:1},{text:"23:30",id:48,status:1}],currentPage:1,totalPages:1,Img:s(16989),siJiaoKeList:[],specialty:[],detail:{},showQrcode:!1,isCoach:!1,userList:[],phoneKey:"",showUserSelect:!1,userId:""}},onLoad:function(t){console.log("1",t),this.memberId=t.id,this.memberName=t.name,this.shopId=n.getStorageSync("nowShopId"),this.shopName=n.getStorageSync("nowShopName");var e=new Date,s=e.getFullYear(),o=e.getMonth()+1,i=e.getDate(),a=e.getHours(),u=e.getMinutes(),c=e.getSeconds();console.log(s,o,i,a,u,c),this.activeDate="".concat(s,"-").concat(o,"-").concat(i);var d=n.getStorageSync("userRoles");1==d.includes("shop_1_coach")?this.isCoach=!0:this.isCoach=!1,this.loadData()},methods:{returnSpecialty:function(t){return t?t.split(","):""},GivenList:function(){var t=this;o.default.getCourseGivenList({data:{memberId:this.memberId,shopId:this.shopId}}).then((function(e){if(200==e.code){var s=[];e.rows.map((function(t){s.push({name:t.courseName,id:t.courseId,checked:!1,cover:t.cover})})),t.siJiaoKeList=s}}))},changeBox:function(t,e){console.log("选择课程：",e),this.choseKeCheng=t,this.siJiaoKeList.map((function(t){t.checked=!1})),this.courseId=e.id,this.courseName=e.name,this.siJiaoKeList[t].checked=!0},getUserList:function(){var t=this;o.default.getUserList({phone:this.phoneKey}).then((function(e){console.log(e),e.data?t.userList=[e.data]:t.$u.toast("未查询到用户")})).catch((function(t){console.log(t)}))},handleUserItem:function(t){var e=this;n.showModal({title:"确认",content:"确认给".concat(this.userList[t].nickName,"预约"),success:function(s){s.confirm&&e.bookingHelp(e.userList[t].memberId)}})},appointment:function(){var t=this;n.getStorageSync("wxUserInfo");if(this.courseId)if(this.activeDate){this.disabled=!0;var e=this.activeDate+" "+this.courses[this.currentIndex].text;o.default.postTrainerBooking({data:{bookingTime:e,courseId:this.courseId,courseName:this.courseName,coachId:this.memberId,coachName:this.memberName,shopId:this.shopId,shopName:this.shopName},method:"POST"}).then((function(e){t.disabled=!1,200==e.code&&(t.$u.toast("预约成功！"),setTimeout((function(){n.navigateBack()}),2e3))})).catch((function(e){t.disabled=!1}))}else this.$u.toast("请先选择预约日期！");else this.$u.toast("请先选择预约课程！")},appointmentHelp:function(){this.courseId&&this.courseId?this.activeDate?this.showUserSelect=!0:this.$u.toast("请先选择预约日期！"):this.$u.toast("请先选择预约课程！")},bookingHelp:function(t){var e=this;this.disabled=!0;var s=this.activeDate+" "+this.courses[this.currentIndex].text;o.default.bookingHelp({data:{bookingTime:s,courseId:this.courseId,courseName:this.courseName,coachId:this.memberId,coachName:this.memberName,shopId:this.shopId,shopName:this.shopName,memberId:t},method:"POST"}).then((function(t){e.disabled=!1,200==t.code&&(e.$u.toast("预约成功！"),setTimeout((function(){n.navigateBack()}),2e3))})).catch((function(t){e.disabled=!1}))},previewImage:function(t){this.mixinShowImage(t)},changeCurrentIndex:function(t,e){console.log(t),1===e&&(this.currentIndex=t)},changeActive:function(t){this.activeDate=t,this.currentPage=1,this.totalPages=1,console.log(this.activeDate)},toBuyHuiYuanKa:function(){var t=n.getStorageSync("nowShopId");n.navigateTo({url:"/pages/huiYuanKa/index?id="+t+"&coachId="+this.memberId})},loadData:function(){var t=this;o.default.getCoachDetails({data:{memberId:this.memberId,shopId:this.shopId}}).then((function(e){if(200==e.code){if(t.detail=e.data,e.data.honor){var s=e.data.honor.split("，");t.detail.honor=s}if(e.data.specialty){var n=e.data.specialty.split(",");t.specialty=n}t.dataProcessing(),t.GivenList()}}))},dataProcessing:function(){var t=this.detail.workStartTime,e=this.detail.workEndTime,s=new Date;s.getHours(),s.getMinutes();console.log(t),console.log(e);for(var n=0;n<this.courses.length;n++)"Y"==this.detail.isRest?this.courses[n].status=4:t>this.courses[n].text?(console.log(777),this.courses[n].status=4):e<this.courses[n].text&&(this.courses[n].status=4)},back:function(){n.$u.debounce(n.navigateBack(),1e3)},handleOfficial:function(){this.showQrcode=!0},onOfficialClose:function(){this.showQrcode=!1,o.default.getInfo({data:{companyId:1},method:"GET"}).then((function(t){200==t.code&&n.setStorageSync("wxUserInfo",t.wxUser)}))}}}}},function(t){var e=function(e){return t(t.s=e)};t.O(0,["common/vendor"],(function(){return e(75300)}));t.O()}]);