<theme-wrap scoped-slots-compiler="augmented" vue-id="8e737fe4-1" class="data-v-33c55e22" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-33c55e22" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('8e737fe4-2')+','+('8e737fe4-1')}}" title="场馆管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-33c55e22" bind:__l="__l"></u-navbar><view class="formView data-v-33c55e22"><view class="formList data-v-33c55e22"><view class="data-v-33c55e22">场馆名称:</view><u--input bind:input="__e" vue-id="{{('8e737fe4-3')+','+('8e737fe4-1')}}" border="{{false}}" value="{{formList.shopName}}" data-event-opts="{{[['^input',[['__set_model',['$0','shopName','$event',[]],['formList']]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--input></view><view class="formList data-v-33c55e22"><view class="data-v-33c55e22">所在省:</view><u-picker vue-id="{{('8e737fe4-4')+','+('8e737fe4-1')}}" show="{{showProvince}}" columns="{{provinceList}}" keyName="label" data-event-opts="{{[['^confirm',[['changePicker']]]]}}" bind:confirm="__e" class="data-v-33c55e22" bind:__l="__l"></u-picker><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="u-flex-1 data-v-33c55e22" bindtap="__e"><u--input bind:input="__e" vue-id="{{('8e737fe4-5')+','+('8e737fe4-1')}}" border="{{false}}" disabled="{{true}}" value="{{provinceName}}" data-event-opts="{{[['^input',[['__set_model',['','provinceName','$event',[]]]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--input></view></view><view class="formList data-v-33c55e22"><view class="data-v-33c55e22">所在市:</view><u-picker vue-id="{{('8e737fe4-6')+','+('8e737fe4-1')}}" show="{{showCity}}" columns="{{cityList}}" keyName="label" data-event-opts="{{[['^confirm',[['changeCity']]]]}}" bind:confirm="__e" class="data-v-33c55e22" bind:__l="__l"></u-picker><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="u-flex-1 data-v-33c55e22" bindtap="__e"><u--input bind:input="__e" vue-id="{{('8e737fe4-7')+','+('8e737fe4-1')}}" border="{{false}}" disabled="{{true}}" value="{{cityName}}" data-event-opts="{{[['^input',[['__set_model',['','cityName','$event',[]]]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--input></view></view><view data-event-opts="{{[['tap',[['choseAdvice',['$event']]]]]}}" class="formList data-v-33c55e22" bindtap="__e"><view class="data-v-33c55e22">选择地址:</view><u--input bind:input="__e" vue-id="{{('8e737fe4-8')+','+('8e737fe4-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['formList']]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--input></view><view class="formList data-v-33c55e22"><view class="data-v-33c55e22">客服电话:</view><u--input bind:input="__e" vue-id="{{('8e737fe4-9')+','+('8e737fe4-1')}}" border="{{false}}" value="{{formList.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['formList']]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--input></view><view class="formList data-v-33c55e22"><view class="data-v-33c55e22">微信支付商户号:</view><u--input bind:input="__e" vue-id="{{('8e737fe4-10')+','+('8e737fe4-1')}}" border="{{false}}" value="{{formList.wxpayMchid}}" data-event-opts="{{[['^input',[['__set_model',['$0','wxpayMchid','$event',[]],['formList']]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--input></view><view class="formTextarea border-8 data-v-33c55e22"><view class="textareaTitle u-flex data-v-33c55e22"><view class="u-flex-1 data-v-33c55e22">场馆Logo:</view><u-icon vue-id="{{('8e737fe4-11')+','+('8e737fe4-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseLogo']]]]}}" bind:click="__e" class="data-v-33c55e22" bind:__l="__l"></u-icon></view><view class="formLogo data-v-33c55e22"><u--image vue-id="{{('8e737fe4-12')+','+('8e737fe4-1')}}" showLoading="{{true}}" src="{{$root.f0}}" width="240rpx" height="160rpx" radius="4" data-event-opts="{{[['^click',[['clickLogo']]]]}}" bind:click="__e" class="data-v-33c55e22" bind:__l="__l"></u--image></view></view><view class="formTextarea border-8 data-v-33c55e22"><view class="textareaTitle u-flex data-v-33c55e22"><view class="u-flex-1 data-v-33c55e22">场馆介绍:</view></view><view class="formLogo data-v-33c55e22"><u--textarea bind:input="__e" vue-id="{{('8e737fe4-13')+','+('8e737fe4-1')}}" placeholder="请输入内容" count="{{true}}" maxlength="{{250}}" value="{{formList.shopIntroduce}}" data-event-opts="{{[['^input',[['__set_model',['$0','shopIntroduce','$event',[]],['formList']]]]]}}" class="data-v-33c55e22" bind:__l="__l"></u--textarea></view></view><view class="formTextarea border-8 data-v-33c55e22"><view class="textareaTitle u-flex data-v-33c55e22"><view class="u-flex-1 data-v-33c55e22">场馆环境:</view><u-icon vue-id="{{('8e737fe4-14')+','+('8e737fe4-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseImage']]]]}}" bind:click="__e" class="data-v-33c55e22" bind:__l="__l"></u-icon></view><view class="formLogo u-flex data-v-33c55e22"><block wx:for="{{$root.l0}}" wx:for-item="list" wx:for-index="idx" wx:key="*this"><view class="imgView data-v-33c55e22"><u--image vue-id="{{('8e737fe4-15-'+idx)+','+('8e737fe4-1')}}" showLoading="{{true}}" src="{{list.f1}}" width="200rpx" height="120rpx" radius="4" class="data-v-33c55e22" bind:__l="__l"></u--image></view></block></view></view></view><view class="whiteView data-v-33c55e22"></view><view class="bottonBtn u-flex data-v-33c55e22"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-33c55e22" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">确认修改</view></view></view></theme-wrap>