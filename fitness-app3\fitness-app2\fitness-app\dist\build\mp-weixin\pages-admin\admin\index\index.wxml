<theme-wrap scoped-slots-compiler="augmented" vue-id="20a8ca98-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('20a8ca98-2')+','+('20a8ca98-1')}}" title="我的管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" bind:__l="__l"></u-navbar><view class="user-avatar u-m-b-40"><image src="{{user.avatar}}" alt class="_img"></image><view class="u-text-center u-m-t-30">{{''+user.nickName+''}}</view></view><u-gap class="u-m-t-30 u-m-b-30" vue-id="{{('20a8ca98-3')+','+('20a8ca98-1')}}" height="1" bgColor="#bbb" bind:__l="__l"></u-gap><view class="admin_collapse u-m-t-30"><view class="u-block__title u-m-b-20 u-font-36"></view><block wx:for="{{collItem}}" wx:for-item="list" wx:for-index="idx" wx:key="idx"><u-collapse vue-id="{{('20a8ca98-4-'+idx)+','+('20a8ca98-1')}}" border="{{false}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{!list.hidden}}"><u-collapse-item vue-id="{{('20a8ca98-5-'+idx)+','+('20a8ca98-4-'+idx)}}" title="{{list.meta.title}}" name="Docs guide" bind:__l="__l" vue-slots="{{['default']}}"><view><u-cell-group vue-id="{{('20a8ca98-6-'+idx)+','+('20a8ca98-5-'+idx)}}" border="{{false}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list.children}}" wx:for-item="item" wx:for-index="n" wx:key="n"><block wx:if="{{!item.hidden}}"><u-cell vue-id="{{('20a8ca98-7-'+idx+'-'+n)+','+('20a8ca98-6-'+idx)}}" title="{{'· '+item.meta.title}}" border="{{false}}" isLink="{{true}}" url="{{item.appPath}}" bind:__l="__l"></u-cell></block></block></u-cell-group></view></u-collapse-item></block></u-collapse></block></view></view></theme-wrap>