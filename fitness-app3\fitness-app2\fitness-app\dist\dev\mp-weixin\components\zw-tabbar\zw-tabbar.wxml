<view class="tabbar-box"><view class="menu-list"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['click',[index]]]]]}}" class="item" style="{{'color:'+(selIdx==index?hoverColor:color)+';'}}" bindtap="__e"><view class="{{[(index==bigIdx)?'big':'']}}"><view class="icon-box"><image mode="aspectFit" src="{{selIdx==index?item.selectedIconPath:item.iconPath}}"></image></view></view><block wx:if="{{item.text}}"><text>{{item.text}}</text></block></view></block></view></view>