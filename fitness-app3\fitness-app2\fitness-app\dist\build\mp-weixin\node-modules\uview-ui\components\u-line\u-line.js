(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-line/u-line"],{48511:function(e,t,n){"use strict";var i=n(81715)["default"];Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var o=u(n(89896));function u(e){return e&&e.__esModule?e:{default:e}}t["default"]={name:"u-line",mixins:[i.$u.mpMixin,i.$u.mixin,o.default],computed:{lineStyle:function(){var e={};return e.margin=this.margin,"row"===this.direction?(e.borderBottomWidth="1px",e.borderBottomStyle=this.dashed?"dashed":"solid",e.width=i.$u.addUnit(this.length),this.hairline&&(e.transform="scaleY(0.5)")):(e.borderLeftWidth="1px",e.borderLeftStyle=this.dashed?"dashed":"solid",e.height=i.$u.addUnit(this.length),this.hairline&&(e.transform="scaleX(0.5)")),e.borderColor=this.color,i.$u.deepMerge(e,i.$u.addStyle(this.customStyle))}}}},84916:function(e,t,n){"use strict";var i;n.r(t),n.d(t,{__esModule:function(){return s.__esModule},default:function(){return f}});var o,u=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__get_style([e.lineStyle]));e.$mp.data=Object.assign({},{$root:{s0:n}})},l=[],s=n(48511),d=s["default"],r=n(95258),a=n.n(r),c=(a(),n(18535)),h=(0,c["default"])(d,u,l,!1,null,"24d3e896",null,!1,i,o),f=h.exports},95258:function(){}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-line/u-line-create-component"],{},function(e){e("81715")["createComponent"](e(84916))}]);