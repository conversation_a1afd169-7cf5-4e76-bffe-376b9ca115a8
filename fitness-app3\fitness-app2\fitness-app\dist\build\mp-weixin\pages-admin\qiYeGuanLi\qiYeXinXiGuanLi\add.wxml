<theme-wrap scoped-slots-compiler="augmented" vue-id="ded91a64-1" class="data-v-4f3f2834" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-4f3f2834" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('ded91a64-2')+','+('ded91a64-1')}}" title="企业管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-4f3f2834" bind:__l="__l"></u-navbar><view class="formView data-v-4f3f2834"><view class="formList data-v-4f3f2834"><view class="data-v-4f3f2834">单位名称:</view><u--input bind:input="__e" vue-id="{{('ded91a64-3')+','+('ded91a64-1')}}" border="{{false}}" value="{{formList.companyName}}" data-event-opts="{{[['^input',[['__set_model',['$0','companyName','$event',[]],['formList']]]]]}}" class="data-v-4f3f2834" bind:__l="__l"></u--input></view><view class="formList data-v-4f3f2834"><view class="data-v-4f3f2834">联系人:</view><u--input bind:input="__e" vue-id="{{('ded91a64-4')+','+('ded91a64-1')}}" border="{{false}}" value="{{formList.contractName}}" data-event-opts="{{[['^input',[['__set_model',['$0','contractName','$event',[]],['formList']]]]]}}" class="data-v-4f3f2834" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['choseAdvice',['$event']]]]]}}" class="formList data-v-4f3f2834" bindtap="__e"><view class="data-v-4f3f2834">选择地址:</view><u--input bind:input="__e" vue-id="{{('ded91a64-5')+','+('ded91a64-1')}}" border="{{false}}" disabled="{{true}}" value="{{formList.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['formList']]]]]}}" class="data-v-4f3f2834" bind:__l="__l"></u--input></view><view class="formList data-v-4f3f2834"><view class="data-v-4f3f2834">联系电话:</view><u--input bind:input="__e" vue-id="{{('ded91a64-6')+','+('ded91a64-1')}}" border="{{false}}" maxlength="{{11}}" value="{{formList.contractPhone}}" data-event-opts="{{[['^input',[['__set_model',['$0','contractPhone','$event',[]],['formList']]]]]}}" class="data-v-4f3f2834" bind:__l="__l"></u--input></view></view><view class="whiteView data-v-4f3f2834"></view><view class="bottonBtn u-flex data-v-4f3f2834"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-4f3f2834" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增单位</view></view></view></theme-wrap>