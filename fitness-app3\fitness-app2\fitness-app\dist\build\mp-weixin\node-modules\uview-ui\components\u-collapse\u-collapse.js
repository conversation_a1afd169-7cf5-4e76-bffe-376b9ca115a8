(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-collapse/u-collapse"],{31195:function(){},36713:function(e,n,t){"use strict";t.r(n),t.d(n,{__esModule:function(){return r.__esModule},default:function(){return p}});var o,i={uLine:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-line/u-line")]).then(t.bind(t,84916))}},u=function(){var e=this,n=e.$createElement;e._self._c},a=[],r=t(83465),c=r["default"],l=t(31195),s=t.n(l),f=(s(),t(18535)),d=(0,f["default"])(c,u,a,!1,null,"64ac287c",null,!1,i,o),p=d.exports},83465:function(e,n,t){"use strict";var o=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var i=u(t(22657));function u(e){return e&&e.__esModule?e:{default:e}}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function r(e,n,t){return(n=c(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function c(e){var n=l(e,"string");return"symbol"==a(n)?n:n+""}function l(e,n){if("object"!=a(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,n||"default");if("object"!=a(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}n["default"]=r(r({name:"u-collapse",mixins:[o.$u.mpMixin,o.$u.mixin,i.default],watch:{needInit:function(){this.init()}},created:function(){this.children=[]},computed:{needInit:function(){return[this.accordion,this.value]}}},"watch",{parentData:function(){this.children.length&&this.children.map((function(e){"function"===typeof e.updateParentData&&e.updateParentData()}))}}),"methods",{init:function(){this.children.map((function(e){e.init()}))},onChange:function(e){var n=this,t=[];this.children.map((function(o,i){n.accordion?(o.expanded=o===e&&!e.expanded,o.setContentAnimate()):o===e&&(o.expanded=!o.expanded,o.setContentAnimate()),t.push({name:o.name||i,status:o.expanded?"open":"close"})})),this.$emit("change",t),this.$emit(e.expanded?"open":"close",e.name)}})}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-collapse/u-collapse-create-component"],{},function(e){e("81715")["createComponent"](e(36713))}]);