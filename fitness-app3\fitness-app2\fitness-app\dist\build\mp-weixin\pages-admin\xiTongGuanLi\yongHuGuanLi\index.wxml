<theme-wrap scoped-slots-compiler="augmented" vue-id="7abc2cb8-1" class="data-v-4b70310e" bind:__l="__l" vue-slots="{{['content']}}"><view class="con data-v-4b70310e" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('7abc2cb8-2')+','+('7abc2cb8-1')}}" title="用户管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-4b70310e" bind:__l="__l"></u-navbar><view class="searchView data-v-4b70310e" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('7abc2cb8-3')+','+('7abc2cb8-1')}}" showAction="{{false}}" placeholder="请输入会员昵称" animation="{{true}}" data-event-opts="{{[['^change',[['searchView']]]]}}" bind:change="__e" class="data-v-4b70310e" bind:__l="__l"></u-search></view><view data-event-opts="{{[['tap',[['clickSuspension',['$event']]]]]}}" class="suspension data-v-4b70310e" style="{{'background:'+($root.m4['buttonLightBgColor'])+';'}}" bindtap="__e"><u-icon vue-id="{{('7abc2cb8-4')+','+('7abc2cb8-1')}}" name="arrow-right-double" size="60rpx" color="#fff" data-event-opts="{{[['^click',[['clickSuspension']]]]}}" bind:click="__e" class="data-v-4b70310e" bind:__l="__l"></u-icon></view><u-popup vue-id="{{('7abc2cb8-5')+','+('7abc2cb8-1')}}" show="{{show}}" mode="left" data-event-opts="{{[['^close',[['close']]],['^open',[['open']]]]}}" bind:close="__e" bind:open="__e" class="data-v-4b70310e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$root.g0>0}}"><view class="u-m-t-80 u-p-l-20 u-p-r-20 shopView data-v-4b70310e"><view class="title data-v-4b70310e">选择场馆</view><block wx:for="{{shopList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view class="{{['u-p-t-20','u-p-b-20','u-border-bottom','u-flex','data-v-4b70310e',index==nowChoseShop?'active':'']}}"><view data-event-opts="{{[['tap',[['choseShop',['$0',index],[[['shopList','',index]]]]]]]}}" class="u-flex-1 data-v-4b70310e" bindtap="__e"><view class="u-text-center data-v-4b70310e">{{list.shopName}}</view></view></view></block></view></block></u-popup><u-gap vue-id="{{('7abc2cb8-6')+','+('7abc2cb8-1')}}" height="1" bgColor="#bbb" class="data-v-4b70310e" bind:__l="__l"></u-gap><scroll-view class="scrollView data-v-4b70310e" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="lit" wx:for-index="index" wx:key="*this"><view class="userList data-v-4b70310e"><view class="user_avatar data-v-4b70310e"><u-avatar vue-id="{{('7abc2cb8-7-'+index)+','+('7abc2cb8-1')}}" size="80rpx" src="{{lit.f0}}" shape="circle" class="data-v-4b70310e" bind:__l="__l"></u-avatar></view><view data-event-opts="{{[['tap',[['addHuiYuan',['$0'],['showList.'+index+'']]]]]}}" class="user_textView data-v-4b70310e" bindtap="__e"><text class="data-v-4b70310e">{{lit.$orig.nickName}}</text><view class="user_tag data-v-4b70310e">{{"所属场馆："+lit.$orig.companyName}}</view></view><view class="user_rightView data-v-4b70310e"><u-icon vue-id="{{('7abc2cb8-8-'+index)+','+('7abc2cb8-1')}}" name="arrow-right" class="data-v-4b70310e" bind:__l="__l"></u-icon></view></view></block></scroll-view></view></theme-wrap>