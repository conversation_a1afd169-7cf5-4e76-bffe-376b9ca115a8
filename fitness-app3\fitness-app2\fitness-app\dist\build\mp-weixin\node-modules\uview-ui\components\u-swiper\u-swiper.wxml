<view class="u-swiper data-v-5678f2ce" style="{{'background-color:'+(bgColor)+';'+('height:'+($root.g0)+';')+('border-radius:'+($root.g1)+';')}}"><block wx:if="{{loading}}"><view class="u-swiper__loading data-v-5678f2ce"><u-loading-icon vue-id="977b1866-1" mode="circle" class="data-v-5678f2ce" bind:__l="__l"></u-loading-icon></view></block><block wx:else><swiper class="u-swiper__wrapper data-v-5678f2ce" style="{{'height:'+($root.g2)+';'}}" circular="{{circular}}" interval="{{interval}}" duration="{{duration}}" autoplay="{{autoplay}}" current="{{current}}" currentItemId="{{currentItemId}}" previousMargin="{{$root.g3}}" nextMargin="{{$root.g4}}" acceleration="{{acceleration}}" displayMultipleItems="{{displayMultipleItems}}" easingFunction="{{easingFunction}}" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="u-swiper__wrapper__item data-v-5678f2ce"><view class="u-swiper__wrapper__item__wrapper data-v-5678f2ce" style="{{item.s0}}"><block wx:if="{{item.m0==='image'}}"><image class="u-swiper__wrapper__item__wrapper__image data-v-5678f2ce" style="{{'height:'+(item.g5)+';'+('border-radius:'+(item.g6)+';')}}" src="{{item.m1}}" mode="{{imgMode}}" data-event-opts="{{[['tap',[['clickHandler',[index]]]]]}}" bindtap="__e"></image></block><block wx:if="{{item.m2==='video'}}"><video class="u-swiper__wrapper__item__wrapper__video data-v-5678f2ce" style="{{'height:'+(item.g7)+';'}}" id="{{'video-'+index}}" enable-progress-gesture="{{false}}" src="{{item.m3}}" poster="{{item.m4}}" title="{{item.g8?item.$orig.title:''}}" controls="{{true}}" data-event-opts="{{[['tap',[['clickHandler',[index]]]]]}}" bindtap="__e"></video></block><block wx:if="{{item.g9}}"><text class="u-swiper__wrapper__item__wrapper__title u-line-1 data-v-5678f2ce">{{item.$orig.title}}</text></block></view></swiper-item></block></swiper></block><view class="u-swiper__indicator data-v-5678f2ce" style="{{$root.s1}}"><block wx:if="{{$slots.indicator}}"><slot name="indicator"></slot></block><block wx:else><block wx:if="{{!loading&&indicator&&!showTitle}}"><u-swiper-indicator vue-id="977b1866-2" indicatorActiveColor="{{indicatorActiveColor}}" indicatorInactiveColor="{{indicatorInactiveColor}}" length="{{$root.g10}}" current="{{currentIndex}}" indicatorMode="{{indicatorMode}}" class="data-v-5678f2ce" bind:__l="__l"></u-swiper-indicator></block></block></view></view>