(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages-admin/tuanKeGuanLi/detail"],{22475:function(){},66579:function(e,n,t){"use strict";var r=t(51372)["default"],u=t(81715)["createPage"];t(96910);i(t(923));var o=i(t(90713));function i(e){return e&&e.__esModule?e:{default:e}}r.__webpack_require_UNI_MP_PLUGIN__=t,u(o.default)},87810:function(e,n,t){"use strict";var r=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var u=o(t(68466));function o(e){return e&&e.__esModule?e:{default:e}}n["default"]={data:function(){return{id:"",timePicker:!1,form:{attendance:"",banner:"",classTime:"",phone:"",price:100,remark:"",title:""},rules:{title:[{required:!0,message:"请输入团课标题",trigger:"blur"}],attendance:[{required:!0,message:"请输入上课人数上限",trigger:"blur"}],classTime:[{required:!0,message:"请选择课程时间",trigger:"blur"}],phone:[{required:!0,message:"请输入联系人号码",trigger:"blur"},{validator:function(e,n,t){return/^1[3456789]\d{9}$/.test(n)?t():t(new Error("请输入正确的手机号"))},message:"请输入正确的手机号",trigger:"change"}],price:[{required:!0,message:"请输入团课价格",trigger:"blur"}],banner:[{required:!0,message:"请上传团课图片",trigger:"blur"}],remark:[{required:!0,message:"请输入课程介绍",trigger:"blur"}]},user:{},minDate:Date.now(),detail:{}}},onReady:function(){this.$refs.uForm.setRules(this.rules)},onLoad:function(e){this.form=JSON.parse(e.list),console.log(this.form)},methods:{loadData:function(){var e=this;u.default.getGroupCourseDetail(this.id).then((function(n){e.form=n.data}))},previewBanner:function(){r.previewImage({urls:[this.form.banner],longPressActions:{success:function(e){},fail:function(e){}}})}}}},90713:function(e,n,t){"use strict";t.r(n),t.d(n,{__esModule:function(){return a.__esModule},default:function(){return f}});var r,u={uToast:function(){return t.e("node-modules/uview-ui/components/u-toast/u-toast").then(t.bind(t,67559))},uNavbar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(t.bind(t,66372))},uForm:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-form/u-form")]).then(t.bind(t,9506))},uFormItem:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-form-item/u-form-item")]).then(t.bind(t,60088))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-input/u-input")]).then(t.bind(t,18857))}},o=function(){var e=this,n=e.$createElement,t=(e._self._c,e.$hasSSP("dcf11be6-1")),r=t?{color:e.$getSSP("dcf11be6-1","content")["navBarTextColor"]}:null,u=t?e.$getSSP("dcf11be6-1","content"):null,o=t?e.$getSSP("dcf11be6-1","content"):null;e._isMounted||(e.e0=function(n){return e.uni.navigateBack()}),e.$mp.data=Object.assign({},{$root:{m0:t,a0:r,m1:u,m2:o}})},i=[],a=t(87810),s=a["default"],l=t(22475),c=t.n(l),d=(c(),t(18535)),m=(0,d["default"])(s,o,i,!1,null,"e1399738",null,!1,u,r),f=m.exports}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],(function(){return n(66579)}));e.O()}]);