{"version": 3, "file": "pages/login/authorize.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uYAEN;AACP,KAAK;AACL;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uZAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACdA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAIA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IAEA,IAAAC,GAAA,CAAAC,cAAA,aAAAD,GAAA,CAAAC,cAAA;MACAD,GAAA,CAAAE,SAAA;QACAC,GAAA;MACA;IACA;EA4BA;EACAC,MAAA,WAAAA,OAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IAEAC,MAAA,WAAAA,OAAA;MACAP,GAAA,CAAAE,SAAA;QACAC,GAAA;MACA;IACA;IACAK,SAAA,WAAAA,UAAAhB,CAAA;MACA,KAAAM,UAAA;MACA,IAAAW,IAAA;MACAT,GAAA,CAAAU,cAAA;QACAC,IAAA;QACAC,OAAA,WAAAA,QAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,MAAA;YACAL,IAAA,CAAAM,QAAA,CAAAF,GAAA,CAAAG,QAAA;YACAhB,GAAA,CAAAiB,cAAA,aAAAJ,GAAA,CAAAG,QAAA;UACA;YACAP,IAAA,CAAAX,UAAA;YACAW,IAAA,CAAAS,KAAA,CAAAC,MAAA,CAAAC,IAAA;cACAC,OAAA;cACAC,IAAA;cACAC,QAAA;YACA;UACA;QACA;QACAC,IAAA,WAAAA,KAAA;UACAf,IAAA,CAAAX,UAAA;UACAW,IAAA,CAAAS,KAAA,CAAAC,MAAA,CAAAC,IAAA;YACAC,OAAA;YACAC,IAAA;YACAC,QAAA;UACA;QACA;MACA;IACA;IACAR,QAAA,WAAAA,SAAAvB,CAAA;MAAA,IAAAiC,KAAA;MAAA,OAAAC,iBAAA,cAAA/B,mBAAA,GAAAgC,IAAA,UAAAC,QAAA;QAAA,IAAAC,KAAA;QAAA,OAAAlC,mBAAA,GAAAmC,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAlC,GAAA,CAAA6B,KAAA;YAAA;cAAAA,KAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,KAAA,IAAAO,IAAA;gBACAC,YAAA,CACAC,WAAA;kBACA1C,IAAA;oBACAwC,IAAA,EAAAP,KAAA,IAAAO,IAAA;oBACApB,QAAA,EAAAxB;kBACA;kBACA+C,MAAA;gBACA,GACAC,IAAA,WAAA3B,GAAA;kBACAY,KAAA,CAAA3B,UAAA;kBACA,IAAAe,GAAA,CAAAjB,IAAA,CAAA6C,MAAA;oBACA,KAAAhB,KAAA,CAAAiB,EAAA,CAAAC,IAAA,CAAAC,OAAA,CAAA/B,GAAA,CAAAjB,IAAA,CAAAA,IAAA;sBACAI,GAAA,CAAA6C,iBAAA;sBACA,IAAAC,OAAA,GAAAjC,GAAA,CAAAjB,IAAA,CAAAA,IAAA;sBACA;sBACA6B,KAAA,CAAAiB,EAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAE,OAAA,CAAAC,IAAA,IACA,KACAtB,KAAA,CAAAuB,UAAA,SAAAF,OAAA,CAAAC,IAAA;sBACA;sBACAtB,KAAA,CAAAiB,EAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAE,OAAA,CAAAG,YAAA,IACA,KACAxB,KAAA,CAAAuB,UAAA,YAAAF,OAAA,CAAAG,YAAA;sBACA;sBACAxB,KAAA,CAAAiB,EAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAE,OAAA,CAAAI,KAAA,IACA,KACAzB,KAAA,CAAAuB,UAAA,UAAAF,OAAA,CAAAI,KAAA;sBACA;sBACA,CAAAzB,KAAA,CAAAiB,EAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAE,OAAA,CAAAC,IAAA,KACA,CAAAtB,KAAA,CAAAiB,EAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAE,OAAA,CAAAC,IAAA,CAAAI,MAAA,IACAnD,GAAA,CAAAoD,YAAA,KACApD,GAAA,CAAAqD,UAAA;wBAAAlD,GAAA;sBAAA;oBACA;sBACAsB,KAAA,CAAAP,KAAA,CAAAC,MAAA,CAAAC,IAAA;wBACAC,OAAA;wBACAC,IAAA;wBACAC,QAAA;sBACA;oBACA;kBACA;oBACAE,KAAA,CAAAP,KAAA,CAAAC,MAAA,CAAAC,IAAA;sBACAC,OAAA;sBACAC,IAAA;sBACAC,QAAA;oBACA;kBACA;gBACA;cACA;gBACAE,KAAA,CAAA3B,UAAA;gBACA2B,KAAA,CAAAP,KAAA,CAAAC,MAAA,CAAAC,IAAA;kBACAC,OAAA;kBACAC,IAAA;kBACAC,QAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAS,QAAA,CAAAsB,IAAA;UAAA;QAAA,GAAA1B,OAAA;MAAA;IACA;IA2CAoB,UAAA,WAAAA,WAAAO,GAAA,EAAAC,KAAA;MACAxD,GAAA,CAAA6C,iBAAA,CAAAU,GAAA;MACAvD,GAAA,CAAAiB,cAAA,CAAAsC,GAAA,EAAAC,KAAA;IACA;EACA;AACA;;;;;;;;;;ACxOA;;;;;;;;;;;;;;;ACAAjE,mBAAA;AAGA,IAAAkE,IAAA,GAAAnE,sBAAA,CAAAC,mBAAA;AACA,IAAAmE,UAAA,GAAApE,sBAAA,CAAAC,mBAAA;AAA8C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH9C;AACAmE,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLsG;AACtH;AACA,CAA6D;AACL;AACxD,CAAsE;;;AAGtE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBsf,CAAC,+DAAe,6dAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,q4BAAG,EAAC", "sources": ["webpack:///./src/pages/login/authorize.vue?6391", "uni-app:///src/pages/login/authorize.vue", "webpack:///./src/pages/login/authorize.vue?e32f", "uni-app:///src/main.js", "webpack:///./src/pages/login/authorize.vue?5016", "webpack:///./src/pages/login/authorize.vue?b3ff", "webpack:///./src/pages/login/authorize.vue?81b0", "webpack:///./src/pages/login/authorize.vue?4b29"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNoNetwork: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-no-network/u-no-network\" */ \"uview-ui/components/u-no-network/u-no-network.vue\"\n      )\n    },\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uNotify: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notify/u-notify\" */ \"uview-ui/components/u-notify/u-notify.vue\"\n      )\n    },\n    uLoadingPage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-page/u-loading-page\" */ \"uview-ui/components/u-loading-page/u-loading-page.vue\"\n      )\n    },\n    \"u-Image\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--image/u--image\" */ \"uview-ui/components/u--image/u--image.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <view class=\"page\">\n    <u-no-network></u-no-network>\n    <u-toast ref=\"uToast\" />\n    <u-notify ref=\"uNotify\" />\n\n    <view class=\"loading\" v-if=\"loading\">\n      <u-loading-page :loading=\"loading\"></u-loading-page>\n    </view>\n    <view class=\"content\" v-else>\n      <!-- #ifdef MP-WEIXIN -->\n      <view class=\"authorize-page\">\n        <view class=\"logo\">\n          <u--image\n            src=\"@/static/images/logo.png\"\n            mode=\"widthFix\"\n            width=\"150rpx\"\n            :fade=\"true\"\n            duration=\"1000\"\n          ></u--image>\n        </view>\n        <view class=\"auth-title\">授权登录</view>\n        <view class=\"auth-info\">获取您的头像、昵称、地区及性别信息</view>\n        <view class=\"auth-btn\">\n          <u-button\n            type=\"warning\"\n            ripple\n            @click=\"authorize\"\n            :loading=\"btnLoading\"\n            >授权登录</u-button\n          >\n        </view>\n        <view class=\"auth-btn\">\n          <u-button type=\"default\" ripple plain @click=\"goBack\"\n            >暂不登录</u-button\n          >\n        </view>\n      </view>\n      <!-- #endif -->\n    </view>\n  </view>\n</template>\n\n<script>\nimport api from \"@/common/api\";\n//#ifdef H5\nimport { wxApi } from \"@/common/wxApi.js\";\n// #endif\nexport default {\n  data() {\n    return {\n      loading: false,\n      btnLoading: false\n    };\n  },\n  onShow() {\n    // #ifdef MP-WEIXIN\n    if (uni.getStorageSync(\"token\") && uni.getStorageSync(\"session\")) {\n      uni.switchTab({\n        url: \"/pages/index/index\"\n      });\n    }\n    // #endif\n\n    // #ifdef H5\n    if (this.$wechat && this.$wechat.isWechat()) {\n      // 用户已登录，跳转首页\n      let userInfo = uni.getStorageSync(\"userInfo\")\n        ? uni.getStorageSync(\"userInfo\")\n        : {};\n      if (uni.getStorageSync(\"token\") && userInfo && userInfo.mobile) {\n        uni.switchTab({\n          url: \"/pages/index/index\"\n        });\n        return;\n      }\n\n      // 捕捉微信回传的code\n      if (wxApi.getUrlParams().code != undefined) {\n        this.getToken(wxApi.getUrlParams().code);\n      } else {\n        this.$login.auth();\n      }\n    } else {\n      uni.redirectTo({\n        url: \"/pages/login/index\"\n      });\n    }\n    // #endif\n  },\n  onLoad() {},\n  onReady() {},\n  methods: {\n    // #ifdef MP-WEIXIN\n    goBack() {\n      uni.switchTab({\n        url: \"/pages/index/index\"\n      });\n    },\n    authorize(e) {\n      this.btnLoading = true;\n      let that = this;\n      uni.getUserProfile({\n        desc: \"用于完善资料\",\n        success: res => {\n          if (res.errMsg === \"getUserProfile:ok\") {\n            that.logining(res.userInfo);\n            uni.setStorageSync(\"userInfo\", res.userInfo);\n          } else {\n            that.btnLoading = false;\n            that.$refs.uToast.show({\n              message: \"授权失败\",\n              type: \"error\",\n              duration: \"2300\"\n            });\n          }\n        },\n        fail: () => {\n          that.btnLoading = false;\n          that.$refs.uToast.show({\n            message: \"授权失败\",\n            type: \"error\",\n            duration: \"2300\"\n          });\n        }\n      });\n    },\n    async logining(e) {\n      let login = await uni.login();\n      if (login[1].code) {\n        api\n          .getTokenXcx({\n            data: {\n              code: login[1].code,\n              userInfo: e\n            },\n            method: \"POST\"\n          })\n          .then(res => {\n            this.btnLoading = false;\n            if (res.data.status === \"success\") {\n              if (!this.$u.test.isEmpty(res.data.data)) {\n                uni.removeStorageSync(\"loadUser\");\n                let resData = res.data.data;\n                // user信息\n                this.$u.test.isEmpty(resData.user)\n                  ? \"\"\n                  : this.useStorage(\"user\", resData.user);\n                // session信息\n                this.$u.test.isEmpty(resData.session_info)\n                  ? \"\"\n                  : this.useStorage(\"session\", resData.session_info);\n                // token信息\n                this.$u.test.isEmpty(resData.token)\n                  ? \"\"\n                  : this.useStorage(\"token\", resData.token);\n                // 判断跳转\n                !this.$u.test.isEmpty(resData.user) &&\n                !this.$u.test.isEmpty(resData.user.mobile)\n                  ? uni.navigateBack()\n                  : uni.redirectTo({ url: \"/pages/login/index\" });\n              } else {\n                this.$refs.uToast.show({\n                  message: \"授权失败\",\n                  type: \"error\",\n                  duration: \"2300\"\n                });\n              }\n            } else {\n              this.$refs.uToast.show({\n                message: \"授权失败\",\n                type: \"error\",\n                duration: \"2300\"\n              });\n            }\n          });\n      } else {\n        this.btnLoading = false;\n        this.$refs.uToast.show({\n          message: \"获取用户登录凭证失败\",\n          type: \"error\",\n          duration: \"2300\"\n        });\n      }\n    },\n    // #endif\n\n    // #ifdef H5\n    getToken(code) {\n      api\n        .getToken({\n          params: { code: code }\n        })\n        .then(res => {\n          if (!this.$u.test.isEmpty(res.data.data)) {\n            let resData = res.data.data;\n            // openid信息\n            this.$u.test.isEmpty(resData.wx_openid)\n              ? \"\"\n              : this.useStorage(\"openid\", resData.wx_openid);\n            // token信息\n            this.$u.test.isEmpty(resData.token)\n              ? \"\"\n              : this.useStorage(\"token\", resData.token);\n            // user信息\n            this.$u.test.isEmpty(resData.user)\n              ? \"\"\n              : this.useStorage(\"userInfo\", resData.user);\n            // 延时创建保留的user信息提供给绑定手机号创建使用\n            this.$u.test.isEmpty(resData.wx_user)\n              ? \"\"\n              : this.useStorage(\"wxUserInfo\", resData.wx_user);\n            // 判断跳转\n            !this.$u.test.isEmpty(resData.user) &&\n            !this.$u.test.isEmpty(resData.user.mobile)\n              ? uni.switchTab({ url: \"/pages/index/index\" })\n              : uni.reLaunch({ url: \"/pages/login/index\" });\n          } else {\n            this.$refs.uToast.show({\n              message: \"授权失败\",\n              type: \"error\",\n              duration: \"2300\"\n            });\n          }\n        });\n    },\n    // #endif\n    useStorage(key, value) {\n      uni.removeStorageSync(key);\n      uni.setStorageSync(key, value);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.page {\n  height: 100vh;\n  position: relative;\n}\n/* #ifdef MP-WEIXIN */\n.authorize-page {\n  width: 100vw;\n  height: 100vh;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  .logo {\n    padding-top: 320upx;\n    line-height: 0;\n    margin-bottom: 40upx;\n  }\n  .auth-title {\n    font-size: 40upx;\n    line-height: 60upx;\n    color: #222;\n    font-weight: bold;\n    margin-bottom: 10upx;\n  }\n  .auth-info {\n    font-size: 26upx;\n    line-height: 36upx;\n    color: #999;\n  }\n}\n.auth-btn {\n  margin-top: 40upx;\n  width: 500rpx;\n  padding: 0 80upx;\n  .cu-btn {\n    width: 100%;\n  }\n}\n/* #endif */\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/authorize.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./authorize.vue?vue&type=template&id=79bf5bda&\"\nvar renderjs\nimport script from \"./authorize.vue?vue&type=script&lang=js&\"\nexport * from \"./authorize.vue?vue&type=script&lang=js&\"\nimport style0 from \"./authorize.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/authorize.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./authorize.vue?vue&type=template&id=79bf5bda&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "_regeneratorRuntime", "data", "loading", "btnLoading", "onShow", "uni", "getStorageSync", "switchTab", "url", "onLoad", "onReady", "methods", "goBack", "authorize", "that", "getUserProfile", "desc", "success", "res", "errMsg", "logining", "userInfo", "setStorageSync", "$refs", "uToast", "show", "message", "type", "duration", "fail", "_this", "_asyncToGenerator", "mark", "_callee", "login", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "api", "getTokenXcx", "method", "then", "status", "$u", "test", "isEmpty", "removeStorageSync", "resData", "user", "useStorage", "session_info", "token", "mobile", "navigateBack", "redirectTo", "stop", "key", "value", "_vue", "_authorize", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}