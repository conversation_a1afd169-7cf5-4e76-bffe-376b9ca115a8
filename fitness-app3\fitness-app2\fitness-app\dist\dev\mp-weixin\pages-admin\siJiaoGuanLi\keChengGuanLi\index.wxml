<theme-wrap scoped-slots-compiler="augmented" vue-id="47ff7209-1" class="data-v-6dad629d" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-6dad629d" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('47ff7209-2')+','+('47ff7209-1')}}" title="课程管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-6dad629d" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-6dad629d"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 data-v-6dad629d" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view><block wx:if="{{$root.g0>0}}"><view class="u-p-t-20 u-p-b-20 u-p-r-40 u-p-l-40 w-100 border-16 u-m-b-20 bg-fff data-v-6dad629d"><u-cell-group vue-id="{{('47ff7209-3')+','+('47ff7209-1')}}" border="{{false}}" class="data-v-6dad629d" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="list" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('47ff7209-4-'+index)+','+('47ff7209-3')}}" title="{{list.$orig.courseName}}" value="" label=" " border="{{index+1==list.g1?false:true}}" center="{{true}}" isLink="{{true}}" url="{{'/pages-admin/siJiaoGuanLi/keChengGuanLi/details?list='+list.g2}}" data-event-opts="{{[['^click',[['setValue']]]]}}" bind:click="__e" class="data-v-6dad629d" bind:__l="__l"></u-cell></block></u-cell-group></view></block><block wx:else><u-empty vue-id="{{('47ff7209-5')+','+('47ff7209-1')}}" marginTop="150" mode="data" class="data-v-6dad629d" bind:__l="__l"></u-empty></block></view><u-picker vue-id="{{('47ff7209-6')+','+('47ff7209-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-6dad629d" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-6dad629d"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-6dad629d" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增课程</view></view></view></theme-wrap>