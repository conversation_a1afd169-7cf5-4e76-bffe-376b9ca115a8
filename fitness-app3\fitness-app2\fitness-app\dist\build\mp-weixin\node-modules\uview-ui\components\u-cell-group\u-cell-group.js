(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-cell-group/u-cell-group"],{63889:function(){},74979:function(e,u,n){"use strict";n.r(u),n.d(u,{__esModule:function(){return i.__esModule},default:function(){return p}});var t,o={uLine:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-line/u-line")]).then(n.bind(n,84916))}},l=function(){var e=this,u=e.$createElement,n=(e._self._c,e.__get_style([e.$u.addStyle(e.customStyle)]));e.$mp.data=Object.assign({},{$root:{s0:n}})},c=[],i=n(90111),s=i["default"],a=n(63889),r=n.n(a),d=(r(),n(18535)),f=(0,d["default"])(s,l,c,!1,null,"4590adfa",null,!1,o,t),p=f.exports},90111:function(e,u,n){"use strict";var t=n(81715)["default"];Object.defineProperty(u,"__esModule",{value:!0}),u["default"]=void 0;var o=l(n(53467));function l(e){return e&&e.__esModule?e:{default:e}}u["default"]={name:"u-cell-group",mixins:[t.$u.mpMixin,t.$u.mixin,o.default]}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-cell-group/u-cell-group-create-component"],{},function(e){e("81715")["createComponent"](e(74979))}]);