<theme-wrap scoped-slots-compiler="augmented" vue-id="2f31f554-1" class="data-v-b23f1034" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-b23f1034" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('2f31f554-2')+','+('2f31f554-1')}}" title="在籍会员管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-b23f1034" bind:__l="__l"></u-navbar><view class="searchView data-v-b23f1034" style="{{'background:'+($root.m3['navBarColor'])+';'}}"><u-search vue-id="{{('2f31f554-3')+','+('2f31f554-1')}}" showAction="{{false}}" placeholder="请输入用户昵称或手机号" animation="{{true}}" data-event-opts="{{[['^click',[['goSearch']]]]}}" bind:click="__e" class="data-v-b23f1034" bind:__l="__l"></u-search></view><view class="tabsView data-v-b23f1034"><u-tabs vue-id="{{('2f31f554-4')+','+('2f31f554-1')}}" activeStyle="{{$root.a1}}" lineColor="{{$root.m4['buttonLightBgColor']}}" list="{{list1}}" data-event-opts="{{[['^click',[['click']]]]}}" bind:click="__e" class="data-v-b23f1034" bind:__l="__l" vue-slots="{{['right']}}"><view style="padding-left:4px;" slot="right" data-event-opts="{{[['tap',[['tabsRightClick',['$event']]]]]}}" bindtap="__e" class="data-v-b23f1034"><u-icon vue-id="{{('2f31f554-5')+','+('2f31f554-4')}}" name="list" size="21" bold="{{true}}" class="data-v-b23f1034" bind:__l="__l"></u-icon></view></u-tabs></view><scroll-view class="scrollView data-v-b23f1034" style="{{'height:'+(scrollHeight)+';'}}" scroll-y="true"><block wx:for="{{showList}}" wx:for-item="list" wx:for-index="index" wx:key="*this"><view class="userList data-v-b23f1034"><view class="user_avatar data-v-b23f1034"><u-avatar vue-id="{{('2f31f554-6-'+index)+','+('2f31f554-1')}}" size="80rpx" src="{{src}}" shape="circle" class="data-v-b23f1034" bind:__l="__l"></u-avatar></view><view class="user_textView data-v-b23f1034"><text class="data-v-b23f1034">晴天雨绵</text><view class="user_tag data-v-b23f1034"><u-tag vue-id="{{('2f31f554-7-'+index)+','+('2f31f554-1')}}" text="会员卡" plain="{{true}}" size="mini" type="warning" class="data-v-b23f1034" bind:__l="__l"></u-tag><u-tag vue-id="{{('2f31f554-8-'+index)+','+('2f31f554-1')}}" text="健身(译创店)" plain="{{true}}" size="mini" type="warning" class="data-v-b23f1034" bind:__l="__l"></u-tag></view></view></view></block></scroll-view><view data-event-opts="{{[['tap',[['clickSuspension',['$event']]]]]}}" class="suspension data-v-b23f1034" style="{{'background:'+($root.m5['buttonLightBgColor'])+';'}}" bindtap="__e"><u-icon vue-id="{{('2f31f554-9')+','+('2f31f554-1')}}" name="file-text" size="60rpx" color="#fff" class="data-v-b23f1034" bind:__l="__l"></u-icon></view></view></theme-wrap>