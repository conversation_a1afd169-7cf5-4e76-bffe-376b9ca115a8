{"version": 3, "file": "pages/user/signin.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,ySAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACnCA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QACAF,IAAA;MACA;QACAA,IAAA;MACA;MACAG,QAAA;MACAC,SAAA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;MACAC,QAAA;MACAC,CAAA,MAAAC,IAAA,GAAAC,WAAA;MAAA;MACAC,CAAA,MAAAF,IAAA,GAAAG,QAAA;MAAA;MACAC,KAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAArB,mBAAA,GAAAsB,IAAA,UAAAC,QAAA;MAAA,OAAAvB,mBAAA,GAAAwB,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAR,KAAA,CAAAL,KAAA,GAAAK,KAAA,CAAAS,QAAA,CAAAT,KAAA,CAAAV,CAAA,EAAAU,KAAA,CAAAP,CAAA;YACAiB,OAAA,CAAAC,GAAA,CAAAX,KAAA,CAAAS,QAAA,CAAAT,KAAA,CAAAV,CAAA,EAAAU,KAAA,CAAAP,CAAA;YACAO,KAAA,CAAAY,OAAA;YACA,CAAAZ,KAAA,CAAAZ,IAAA,IAAAY,KAAA,CAAAa,MAAA;UAAA;UAAA;YAAA,OAAAP,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA,KAAAjB,MAAA,QAAAkB,QAAA,GAAAC,IAAA;EAEA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAA9B,QAAA,CAAA+B,KAAA,MAAAnC,SAAA,EAAAoC,MAAA,MAAAhC,QAAA,CAAA+B,KAAA,SAAAnC,SAAA;IACA;IACAqC,MAAA,WAAAA,OAAA;MACA,YAAA3B,KAAA,CAAA4B,MAAA;IACA;EACA;EACAC,OAAA;IACAZ,OAAA,WAAAA,QAAAa,IAAA;MAAA,IAAAC,MAAA;MAAA,OAAAzB,iBAAA,cAAArB,mBAAA,GAAAsB,IAAA,UAAAyB,SAAA;QAAA,IAAAC,QAAA;QAAA,OAAAhD,mBAAA,GAAAwB,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACAuB,YAAA,CAAAH,QAAA;gBACA9C,IAAA;kBACAkD,IAAA,EAAAN,MAAA,CAAApC,CAAA;kBACA2C,KAAA,EAAAP,MAAA,CAAAjC,CAAA;kBACAgC,IAAA,EAAAA,IAAA;kBACAS,MAAA,EAAAC,GAAA,CAAAC,cAAA;gBACA;cACA;YAAA;cAPAR,QAAA,GAAAE,SAAA,CAAAO,IAAA;cAQA3B,OAAA,CAAAC,GAAA,CAAAiB,QAAA;cACAF,MAAA,CAAA1C,QAAA,GAAA4C,QAAA,CAAA9C,IAAA;YAAA;YAAA;cAAA,OAAAgD,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAa,QAAA;MAAA;IAEA;IACAW,QAAA,WAAAA,SAAA7D,CAAA;MACAiC,OAAA,CAAAC,GAAA,CAAAlC,CAAA;MACA,IAAAA,CAAA,CAAAI,IAAA;QACA,KAAA+B,OAAA;MACA;QACA,KAAAA,OAAA;MACA;IACA;IACA2B,SAAA,WAAAA,UAAAC,GAAA;MACA,IAAAC,GAAA,GAAAC,MAAA,CAAAF,GAAA;MACA,OAAAC,GAAA,cAAAA,GAAA,GAAAA,GAAA;IACA;IACAzB,QAAA,WAAAA,SAAA;MACA,IAAAC,IAAA,OAAA1B,IAAA;MACA,IAAAD,CAAA,GAAA2B,IAAA,CAAAzB,WAAA;MACA,IAAAC,CAAA,GAAAwB,IAAA,CAAAvB,QAAA;MACA,IAAAiD,CAAA,GAAA1B,IAAA,CAAA2B,OAAA;MACA,IAAAC,IAAA,OAAAtD,IAAA,GAAAuD,MAAA;MACA,IAAAC,QAAA;MACA,IAAAC,UAAA,UAAAD,QAAA,CAAAF,IAAA;MACA,IAAAI,KAAA;QACAhC,IAAA,EAAA3B,CAAA,cAAAiD,SAAA,CAAA9C,CAAA,mBAAA8C,SAAA,CAAAI,CAAA;QACAE,IAAA,EAAAG;MACA;MACA,OAAAC,KAAA;IACA;IACA;IACAxC,QAAA,WAAAA,SAAAnB,CAAA,EAAA2C,KAAA;MACA,IAAAtC,KAAA;MACA,IAAAF,CAAA,GAAAiD,MAAA,CAAAT,KAAA;MACA,IAAAiB,eAAA,OAAA3D,IAAA,CAAAD,CAAA,EAAAG,CAAA,SAAAqD,MAAA;MACA,IAAAK,eAAA,OAAA5D,IAAA,CAAAD,CAAA,EAAAG,CAAA,KAAAmD,OAAA;MACA,IAAAQ,kBAAA,OAAA7D,IAAA,CAAAD,CAAA,EAAAG,CAAA,SAAAmD,OAAA;MACA,IAAA3D,SAAA,QAAAA,SAAA,iBAAAA,SAAA;MACA,IAAAoE,QAAA;QACA;QACA,IAAAH,eAAA,IAAAjE,SAAA;UACA;QACA,WAAAiE,eAAA,GAAAjE,SAAA;UACA,OAAAiE,eAAA,GAAAjE,SAAA;QACA;UACA,WAAAA,SAAA,GAAAiE,eAAA;QACA;MACA;MACA,IAAAI,MAAA,QAAAD,QAAA,GAAAF,eAAA;MACA,SAAAI,CAAA,MAAAA,CAAA,IAAAF,QAAA,EAAAE,CAAA;QACA5D,KAAA,CAAA6D,IAAA;UACAvC,IAAA,OAAAsB,SAAA,CAAAa,kBAAA,GAAAC,QAAA,GAAAE,CAAA;UACAE,GAAA,EAAAxE,SAAA,GAAAsE,CAAA;UACAtB,KAAA,EAAAxC,CAAA,iBAAA8C,SAAA,CAAA9C,CAAA;UACAuC,IAAA,EAAAvC,CAAA,YAAAH,CAAA,GAAAA,CAAA;QACA;MACA;MACA,SAAAoE,CAAA,MAAAA,CAAA,IAAAP,eAAA,EAAAO,CAAA;QACA/D,KAAA,CAAA6D,IAAA;UACAvC,IAAA,OAAAsB,SAAA,CAAAmB,CAAA;UACAD,GAAA,EAAAC,CAAA,OAAAR,eAAA;UACAjB,KAAA,OAAAM,SAAA,CAAA9C,CAAA;UACAuC,IAAA,EAAA1C,CAAA;UACAqE,MAAA;QACA;MACA;MACA,SAAAC,CAAA,MAAAA,CAAA,IAAAN,MAAA,EAAAM,CAAA;QACAjE,KAAA,CAAA6D,IAAA;UACAvC,IAAA,OAAAsB,SAAA,CAAAqB,CAAA;UACAH,GAAA,GAAAN,eAAA,GAAAE,QAAA,GAAApE,SAAA,GAAA2E,CAAA;UACA3B,KAAA,EAAAxC,CAAA,kBAAA8C,SAAA,CAAA9C,CAAA;UACAuC,IAAA,EAAAvC,CAAA,aAAAH,CAAA,GAAAA,CAAA;QACA;MACA;MACA,OAAAK,KAAA;IACA;IACAkE,SAAA,WAAAA,UAAAvE,CAAA,EAAAG,CAAA,EAAAkD,CAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAmB,WAAA,WAAAA,YAAAxE,CAAA,EAAAG,CAAA,EAAAkD,CAAA;MACA;MACA,IAAAoB,GAAA,MAAA1C,MAAA,CAAA/B,CAAA,OAAA+B,MAAA,CAAA5B,CAAA,OAAA4B,MAAA,CAAAsB,CAAA;MACA,IAAAqB,QAAA,OAAAzE,IAAA,CAAAwE,GAAA,CAAAE,OAAA;MACA,IAAAC,QAAA,GAAAF,QAAA,CAAAG,OAAA;MACA,IAAAC,OAAA,OAAA7E,IAAA,GAAA4E,OAAA;MACA,IAAAD,QAAA,GAAAE,OAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA/E,CAAA,EAAAG,CAAA,EAAAkD,CAAA;MACA,IAAA2B,IAAA;MACA,SAAAf,CAAA,MAAAA,CAAA,QAAAvE,QAAA,CAAAuC,MAAA,EAAAgC,CAAA;QACA,IAAAgB,EAAA,MAAAlD,MAAA,CAAA/B,CAAA,OAAA+B,MAAA,CAAA5B,CAAA,OAAA4B,MAAA,CAAAsB,CAAA;QACA,SAAA3D,QAAA,CAAAuE,CAAA,EAAAiB,UAAA,IAAAD,EAAA;UACAD,IAAA;UACA;QACA;MACA;MACA,OAAAA,IAAA;IACA;IACAG,OAAA,WAAAA,QAAAnF,CAAA,EAAAG,CAAA,EAAAkD,CAAA;MACA,IAAA+B,MAAA,GAAApF,CAAA,SAAAG,CAAA,SAAAkD,CAAA;MACA,IAAAM,KAAA,QAAAjC,QAAA,GAAAC,IAAA;MACA,IAAAyD,MAAA,IAAAzB,KAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACApC,MAAA,WAAAA,OAAA;MAAA,IAAA8D,MAAA;MACA,KAAA9E,SAAA,SAAAA,SAAA;MACA,SAAAA,SAAA;QACA,KAAAD,WAAA;MACA;QACA,IAAAgF,KAAA;QACA,KAAAjF,KAAA,CAAAkF,OAAA,WAAAtB,CAAA,EAAAuB,CAAA;UACAH,MAAA,CAAAF,OAAA,CAAAlB,CAAA,CAAAvB,IAAA,EAAAuB,CAAA,CAAAtB,KAAA,EAAAsB,CAAA,CAAAtC,IAAA,MAAA2D,KAAA,GAAAE,CAAA;QACA;QACA,KAAAlF,WAAA,MAAAmF,IAAA,CAAAC,IAAA,EAAAJ,KAAA;MACA;IACA;IACA;IACAK,SAAA,WAAAA,UAAA1B,CAAA,EAAA2B,KAAA;MACA;MACA,IAAAjE,IAAA,MAAAI,MAAA,CAAAkC,CAAA,CAAAvB,IAAA,OAAAX,MAAA,CAAAkC,CAAA,CAAAtB,KAAA,OAAAZ,MAAA,CAAAkC,CAAA,CAAAtC,IAAA;MACA,IAAAkE,OAAA,OAAA5F,IAAA,CAAA0B,IAAA,EAAAkD,OAAA;MACA,IAAAC,OAAA,OAAA7E,IAAA,GAAA4E,OAAA;MACA,IAAAtB,IAAA,OAAAtD,IAAA,CAAA0B,IAAA,EAAA6B,MAAA;MACA,IAAAC,QAAA;MACA,IAAAC,UAAA,UAAAD,QAAA,CAAAF,IAAA;MACA,IAAAuC,QAAA;QACAnE,IAAA,EAAAA,IAAA;QACA4B,IAAA,EAAAG;MACA;MACA,KAAAO,CAAA,CAAAI,MAAA;QACA;QACA;MACA;MACA,IAAAwB,OAAA,GAAAf,OAAA;QACA,SAAAlF,aAAA;UACA;QACA;UACA,KAAAY,MAAA,GAAAmB,IAAA;UACA,KAAAoE,KAAA,eAAAD,QAAA;QACA;MACA;QACA,KAAAtF,MAAA,GAAAmB,IAAA;QACA,KAAAoE,KAAA,eAAAD,QAAA;MACA;MACA1E,OAAA,CAAAC,GAAA,CAAAyE,QAAA;IACA;IACA;IACAE,cAAA,WAAAA,eAAAhG,CAAA,EAAAG,CAAA;MACA,KAAAE,KAAA,QAAAc,QAAA,CAAAnB,CAAA,EAAAG,CAAA;MACA,KAAAH,CAAA,GAAAA,CAAA;MACA,KAAAG,CAAA,GAAAA,CAAA;IACA;IACA8F,WAAA,WAAAA,YAAA9D,IAAA;MACA,IAAAA,IAAA;QACA,SAAAhC,CAAA;UACA,KAAAA,CAAA;UACA,KAAAH,CAAA,QAAAA,CAAA;QACA;UACA,KAAAG,CAAA,QAAAA,CAAA;QACA;MACA;QACA,SAAAA,CAAA;UACA,KAAAA,CAAA;UACA,KAAAH,CAAA,QAAAA,CAAA;QACA;UACA,KAAAG,CAAA,QAAAA,CAAA;QACA;MACA;MACA,KAAAE,KAAA,QAAAc,QAAA,MAAAnB,CAAA,OAAAG,CAAA;IACA;EACA;AACA;;;;;;;;;;ACtSA;;;;;;;;;;;;;;;ACAAjB,mBAAA;AAGA,IAAAgH,IAAA,GAAAjH,sBAAA,CAAAC,mBAAA;AACA,IAAAiH,OAAA,GAAAlH,sBAAA,CAAAC,mBAAA;AAA0C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH1C;AACAiH,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC;;;;;;;;;;;;;;;;;;;ACL+G;AAC/H;AACA,CAA0D;AACL;AACrD,CAA2F;;;AAG3F;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBmf,CAAC,+DAAe,0dAAG,EAAC;;;;;;;;;;;;;;;;;ACAie,CAAC,+DAAe,05BAAG,EAAC", "sources": ["webpack:///./src/pages/user/signin.vue?40d9", "uni-app:///src/pages/user/signin.vue", "webpack:///./src/pages/user/signin.vue?1000", "uni-app:///src/main.js", "webpack:///./src/pages/user/signin.vue?4387", "webpack:///./src/pages/user/signin.vue?9fb2", "webpack:///./src/pages/user/signin.vue?07d5", "webpack:///./src/pages/user/signin.vue?4845"], "sourcesContent": ["var components\ntry {\n  components = {\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-toast/u-toast\" */ \"uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabs/u-tabs\" */ \"uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"2a457929-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"2a457929-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"2a457929-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"2a457929-1\", \"content\") : null\n  var m3 = m0 ? _vm.formatNum(_vm.m) : null\n  var l0 = m0\n    ? _vm.__map(_vm.dates, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m4 = _vm.isMarkDay(item.year, item.month, item.date) && item.isCurM\n        var m5 = _vm.isToday(item.year, item.month, item.date)\n        var m6 = _vm.isWorkDay(item.year, item.month, item.date)\n        var m7 = Number(item.date)\n        return {\n          $orig: $orig,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\r\n    <themeWrap>\r\n        <template #content=\"{ navBarColor, navBarTextColor, buttonLightBgColor }\">\r\n            <u-toast ref=\"toast\"></u-toast>\r\n            <view>\r\n                <!-- 顶部菜单栏 -->\r\n                <u-navbar title=\"签到页面\" @rightClick=\"uni.navigateBack()\" :titleStyle=\"{ color: navBarTextColor }\"\r\n                    :bgColor=\"navBarColor\" :leftIconColor=\"navBarTextColor\" :autoBack=\"true\" :placeholder=\"true\"\r\n                    :safeAreaInsetTop=\"true\">\r\n                </u-navbar>\r\n            </view>\r\n            <u-tabs :list=\"list1\" @click=\"clicktab\"></u-tabs>\r\n            <view class=\"calendar-wrapper\">\r\n                <view class=\"flex alignCenter justifyBetween\">\r\n                    <view class=\"title\">\r\n                        往日签到\r\n                    </view>\r\n                    <view class=\"header\">\r\n                        <view class=\"pre\" @click=\"changeMonth('pre')\"></view>\r\n                        <view>{{ y + '年' + formatNum(m) + '月' }}</view>\r\n                        <view class=\"next\" @click=\"changeMonth('next')\"></view>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"week\">\r\n                    <view class=\"week-day\" v-for=\"(item, index) in weekDay\" :key=\"index\">{{ item }}</view>\r\n                </view>\r\n                <view :class=\"{ hide: !monthOpen }\" class=\"content\" :style=\"{ height: height }\">\r\n                    <view :style=\"{ top: positionTop + 'rpx' }\" class=\"days\">\r\n                        <view class=\"item\" v-for=\"(item, index) in dates\" :key=\"index\">\r\n                            <view class=\"day\" @click=\"selectOne(item, $event)\" :class=\"{\r\n                                choose: isMarkDay(item.year, item.month, item.date) && item.isCurM,\r\n                                nolm: !item.isCurM,\r\n                                today: isToday(item.year, item.month, item.date),\r\n                                isWorkDay: isWorkDay(item.year, item.month, item.date)\r\n                            }\">\r\n                                {{ Number(item.date) }}\r\n                            </view>\r\n                            <!-- <view class=\"markDay\" v-if=\"isMarkDay(item.year, item.month, item.date)&&item.isCurM\"></view> -->\r\n                            <!-- <view class=\"today-text\" v-if=\"isToday(item.year, item.month, item.date)\">今</view> -->\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <image src=\"https://i.loli.net/2020/07/16/2MmZsucVTlRjSwK.png\" mode=\"scaleToFill\" v-if=\"collapsible\"\r\n                    @click=\"toggle\" class=\"weektoggle\" :class=\"{ down: monthOpen }\"></image>\r\n            </view>\r\n        </template>\r\n    </themeWrap>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/common/api'\r\n\r\nexport default {\r\n    name: 'ren-calendar',\r\n    data() {\r\n        return {\r\n            list1: [{\r\n                name: '每日签到',\r\n            }, {\r\n                name: '团课签到',\r\n            }],\r\n            markDays: [],\r\n            weekstart: 0,\r\n            disabledAfter: false,\r\n            collapsible: true,\r\n            open: true,\r\n            weektext: ['日', '一', '二', '三', '四', '五', '六'],\r\n            y: new Date().getFullYear(), // 年\r\n            m: new Date().getMonth() + 1, // 月\r\n            dates: [{}], // 当前月的日期数据\r\n            positionTop: 0,\r\n            monthOpen: true,\r\n            choose: ''\r\n        };\r\n    },\r\n    async created() {\r\n        this.dates = this.monthDay(this.y, this.m);\r\n        console.log(this.monthDay(this.y, this.m))\r\n        this.getdata(1)\r\n        !this.open && this.toggle();\r\n    },\r\n    mounted() {\r\n        this.choose = this.getToday().date;\r\n\r\n    },\r\n    computed: {\r\n        // 顶部星期栏\r\n        weekDay() {\r\n            return this.weektext.slice(this.weekstart).concat(this.weektext.slice(0, this.weekstart));\r\n        },\r\n        height() {\r\n            return (this.dates.length / 7) * 80 + 'rpx';\r\n        }\r\n    },\r\n    methods: {\r\n        async getdata(type) {\r\n            let mySignIn = await api.mySignIn({\r\n                data: {\r\n                    year: this.y,\r\n                    month: this.m,\r\n                    type: type,\r\n                    shopId: uni.getStorageSync(\"nowShopId\"),\r\n                },\r\n            })\r\n            console.log(mySignIn)\r\n            this.markDays = mySignIn.data\r\n\r\n        },\r\n        clicktab(e) {\r\n            console.log(e)\r\n            if (e.name == '团课签到') {\r\n                this.getdata(2)\r\n            } else {\r\n                this.getdata(1)\r\n            }\r\n        },\r\n        formatNum(num) {\r\n            let res = Number(num);\r\n            return res < 10 ? '0' + res : res;\r\n        },\r\n        getToday() {\r\n            let date = new Date();\r\n            let y = date.getFullYear();\r\n            let m = date.getMonth();\r\n            let d = date.getDate();\r\n            let week = new Date().getDay();\r\n            let weekText = ['日', '一', '二', '三', '四', '五', '六'];\r\n            let formatWeek = '星期' + weekText[week];\r\n            let today = {\r\n                date: y + '-' + this.formatNum(m + 1) + '-' + this.formatNum(d),\r\n                week: formatWeek\r\n            };\r\n            return today;\r\n        },\r\n        // 获取当前月份数据\r\n        monthDay(y, month) {\r\n            let dates = [];\r\n            let m = Number(month);\r\n            let firstDayOfMonth = new Date(y, m - 1, 1).getDay(); // 当月第一天星期几\r\n            let lastDateOfMonth = new Date(y, m, 0).getDate(); // 当月最后一天\r\n            let lastDayOfLastMonth = new Date(y, m - 2, 0).getDate(); // 上一月的最后一天\r\n            let weekstart = this.weekstart == 7 ? 0 : this.weekstart;\r\n            let startDay = (() => {\r\n                // 周初有几天是上个月的\r\n                if (firstDayOfMonth == weekstart) {\r\n                    return 0;\r\n                } else if (firstDayOfMonth > weekstart) {\r\n                    return firstDayOfMonth - weekstart;\r\n                } else {\r\n                    return 7 - weekstart + firstDayOfMonth;\r\n                }\r\n            })();\r\n            let endDay = 7 - ((startDay + lastDateOfMonth) % 7); // 结束还有几天是下个月的\r\n            for (let i = 1; i <= startDay; i++) {\r\n                dates.push({\r\n                    date: this.formatNum(lastDayOfLastMonth - startDay + i),\r\n                    day: weekstart + i - 1 || 7,\r\n                    month: m - 1 >= 0 ? this.formatNum(m - 1) : 12,\r\n                    year: m - 1 >= 0 ? y : y - 1\r\n                });\r\n            }\r\n            for (let j = 1; j <= lastDateOfMonth; j++) {\r\n                dates.push({\r\n                    date: this.formatNum(j),\r\n                    day: (j % 7) + firstDayOfMonth - 1 || 7,\r\n                    month: this.formatNum(m),\r\n                    year: y,\r\n                    isCurM: true //是否当前月份\r\n                });\r\n            }\r\n            for (let k = 1; k <= endDay; k++) {\r\n                dates.push({\r\n                    date: this.formatNum(k),\r\n                    day: (lastDateOfMonth + startDay + weekstart + k - 1) % 7 || 7,\r\n                    month: m + 1 <= 11 ? this.formatNum(m + 1) : 0,\r\n                    year: m + 1 <= 11 ? y : y + 1\r\n                });\r\n            }\r\n            return dates;\r\n        },\r\n        isWorkDay(y, m, d) {\r\n            //是否工作日\r\n            // let ymd = `${y}/${m}/${d}`;\r\n            // let formatDY = new Date(ymd.replace(/-/g, '/'));\r\n            // let week = formatDY.getDay();\r\n            // if (week == 0 || week == 6) {\r\n            //     return false;\r\n            // } else {\r\n            //     return true;\r\n            // }\r\n            return true\r\n        },\r\n        isFutureDay(y, m, d) {\r\n            //是否未来日期\r\n            let ymd = `${y}/${m}/${d}`;\r\n            let formatDY = new Date(ymd.replace(/-/g, '/'));\r\n            let showTime = formatDY.getTime();\r\n            let curTime = new Date().getTime();\r\n            if (showTime > curTime) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        },\r\n        // 标记日期\r\n        isMarkDay(y, m, d) {\r\n            let flag = false;\r\n            for (let i = 0; i < this.markDays.length; i++) {\r\n                let dy = `${y}-${m}-${d}`;\r\n                if (this.markDays[i].signInTime == dy) {\r\n                    flag = true;\r\n                    break;\r\n                }\r\n            }\r\n            return flag;\r\n        },\r\n        isToday(y, m, d) {\r\n            let checkD = y + '-' + m + '-' + d;\r\n            let today = this.getToday().date;\r\n            if (checkD == today) {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        },\r\n        // 展开收起\r\n        toggle() {\r\n            this.monthOpen = !this.monthOpen;\r\n            if (this.monthOpen) {\r\n                this.positionTop = 0;\r\n            } else {\r\n                let index = -1;\r\n                this.dates.forEach((i, x) => {\r\n                    this.isToday(i.year, i.month, i.date) && (index = x);\r\n                });\r\n                this.positionTop = -((Math.ceil((index + 1) / 7) || 1) - 1) * 80;\r\n            }\r\n        },\r\n        // 点击回调\r\n        selectOne(i, event) {\r\n            return\r\n            let date = `${i.year}-${i.month}-${i.date}`;\r\n            let selectD = new Date(date).getTime();\r\n            let curTime = new Date().getTime();\r\n            let week = new Date(date).getDay();\r\n            let weekText = ['日', '一', '二', '三', '四', '五', '六'];\r\n            let formatWeek = '星期' + weekText[week];\r\n            let response = {\r\n                date: date,\r\n                week: formatWeek\r\n            };\r\n            if (!i.isCurM) {\r\n                // console.log('不在当前月范围内');\r\n                return false;\r\n            }\r\n            if (selectD > curTime) {\r\n                if (this.disabledAfter) {\r\n                    return false;\r\n                } else {\r\n                    this.choose = date;\r\n                    this.$emit('onDayClick', response);\r\n                }\r\n            } else {\r\n                this.choose = date;\r\n                this.$emit('onDayClick', response);\r\n            }\r\n            console.log(response);\r\n        },\r\n        //改变年月\r\n        changYearMonth(y, m) {\r\n            this.dates = this.monthDay(y, m);\r\n            this.y = y;\r\n            this.m = m;\r\n        },\r\n        changeMonth(type) {\r\n            if (type == 'pre') {\r\n                if (this.m + 1 == 2) {\r\n                    this.m = 12;\r\n                    this.y = this.y - 1;\r\n                } else {\r\n                    this.m = this.m - 1;\r\n                }\r\n            } else {\r\n                if (this.m + 1 == 13) {\r\n                    this.m = 1;\r\n                    this.y = this.y + 1;\r\n                } else {\r\n                    this.m = this.m + 1;\r\n                }\r\n            }\r\n            this.dates = this.monthDay(this.y, this.m);\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.flex {\r\n    display: flex;\r\n}\r\n\r\n.alignCenter {\r\n    align-items: center;\r\n}\r\n\r\n.justifyBetween {\r\n    justify-content: space-between;\r\n}\r\n\r\n.calendar-wrapper {\r\n    color: #bbb7b7;\r\n    font-size: 28rpx;\r\n    text-align: center;\r\n    background-color: #fff;\r\n    border-radius: 29rpx;\r\n    padding: 19rpx 38rpx 10rpx 38rpx;\r\n\r\n    .title {}\r\n\r\n    .header {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        height: 88rpx;\r\n        color: #42464A;\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        border-bottom: 2rpx solid #f2f2f2;\r\n\r\n        .pre,\r\n        .next {\r\n            color: rgba(51, 51, 51, 0.2);\r\n            font-size: 28rpx;\r\n            font-weight: normal;\r\n            width: 34rpx;\r\n            height: 34rpx;\r\n            // border-radius: 10rpx;\r\n            border: 2rpx solid #dcdfe6;\r\n            background: rgba(51, 51, 51, 0.1);\r\n            border-radius: 50%;\r\n\r\n        }\r\n\r\n        .pre {\r\n            margin-right: 30rpx;\r\n        }\r\n\r\n        .next {\r\n            margin-left: 30rpx;\r\n        }\r\n    }\r\n\r\n    .week {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 80rpx;\r\n        line-height: 80rpx;\r\n        border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\r\n        view {\r\n            flex: 1;\r\n        }\r\n    }\r\n\r\n    .content {\r\n        position: relative;\r\n        overflow: hidden;\r\n        transition: height 0.4s ease;\r\n\r\n        .days {\r\n            transition: top 0.3s;\r\n            display: flex;\r\n            align-items: center;\r\n            flex-wrap: wrap;\r\n            position: relative;\r\n\r\n            .item {\r\n                position: relative;\r\n                display: block;\r\n                height: 80rpx;\r\n                line-height: 80rpx;\r\n                width: calc(100% / 7);\r\n\r\n                .day {\r\n                    font-style: normal;\r\n                    display: inline-block;\r\n                    vertical-align: middle;\r\n                    width: 60rpx;\r\n                    height: 60rpx;\r\n                    line-height: 60rpx;\r\n                    overflow: hidden;\r\n                    border-radius: 60rpx;\r\n\r\n                    &.choose {\r\n                        background-color: #FC6767;\r\n                        color: #fff;\r\n                    }\r\n\r\n                    &.nolm {\r\n                        color: #fff;\r\n                        opacity: 0.3;\r\n                    }\r\n                }\r\n\r\n                .isWorkDay {\r\n                    color: #42464a;\r\n                }\r\n\r\n                .notSigned {\r\n                    font-style: normal;\r\n                    width: 8rpx;\r\n                    height: 8rpx;\r\n                    background: #fa7268;\r\n                    border-radius: 10rpx;\r\n                    position: absolute;\r\n                    left: 50%;\r\n                    bottom: 0;\r\n                    pointer-events: none;\r\n                }\r\n\r\n                .today {\r\n                    border-radius: 0rpx 0rpx 0rpx 0rpx;\r\n                    opacity: 1;\r\n                    border: 2rpx solid #FC6767;\r\n                    color: #FC6767;\r\n                    border-radius: 50%;\r\n                }\r\n\r\n                .workDay {\r\n                    font-style: normal;\r\n                    width: 8rpx;\r\n                    height: 8rpx;\r\n                    background: #4d7df9;\r\n                    border-radius: 10rpx;\r\n                    position: absolute;\r\n                    left: 50%;\r\n                    bottom: 0;\r\n                    pointer-events: none;\r\n                }\r\n\r\n                .markDay {\r\n                    font-style: normal;\r\n                    width: 8rpx;\r\n                    height: 8rpx;\r\n                    background: #fc7a64;\r\n                    border-radius: 10rpx;\r\n                    position: absolute;\r\n                    left: 50%;\r\n                    bottom: 0;\r\n                    pointer-events: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .hide {\r\n        height: 80rpx !important;\r\n    }\r\n\r\n    .weektoggle {\r\n        width: 85rpx;\r\n        height: 32rpx;\r\n        position: relative;\r\n        bottom: -42rpx;\r\n\r\n        &.down {\r\n            transform: rotate(180deg);\r\n            bottom: 0;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/signin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./signin.vue?vue&type=template&id=74f9f77d&scoped=true&\"\nvar renderjs\nimport script from \"./signin.vue?vue&type=script&lang=js&\"\nexport * from \"./signin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./signin.vue?vue&type=style&index=0&id=74f9f77d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"74f9f77d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/signin.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=style&index=0&id=74f9f77d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=style&index=0&id=74f9f77d&lang=scss&scoped=true&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=template&id=74f9f77d&scoped=true&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "_regeneratorRuntime", "name", "data", "list1", "markDays", "weekstart", "disabledAfter", "collapsible", "open", "weektext", "y", "Date", "getFullYear", "m", "getMonth", "dates", "positionTop", "monthOpen", "choose", "created", "_this", "_asyncToGenerator", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "monthDay", "console", "log", "getdata", "toggle", "stop", "mounted", "get<PERSON><PERSON>y", "date", "computed", "weekDay", "slice", "concat", "height", "length", "methods", "type", "_this2", "_callee2", "mySignIn", "_callee2$", "_context2", "api", "year", "month", "shopId", "uni", "getStorageSync", "sent", "clicktab", "formatNum", "num", "res", "Number", "d", "getDate", "week", "getDay", "weekText", "formatWeek", "today", "firstDayOfMonth", "lastDateOfMonth", "lastDayOfLastMonth", "startDay", "endDay", "i", "push", "day", "j", "isCurM", "k", "isWorkDay", "isFutureDay", "ymd", "formatDY", "replace", "showTime", "getTime", "curTime", "isMarkDay", "flag", "dy", "signInTime", "isToday", "checkD", "_this3", "index", "for<PERSON>ach", "x", "Math", "ceil", "selectOne", "event", "selectD", "response", "$emit", "changYearMonth", "changeMonth", "_vue", "_signin", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}