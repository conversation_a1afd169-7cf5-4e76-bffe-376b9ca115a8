<theme-wrap scoped-slots-compiler="augmented" vue-id="e1195a4c-1" class="data-v-89f4fa22" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-89f4fa22" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('e1195a4c-2')+','+('e1195a4c-1')}}" title="新增课程" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-89f4fa22" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-89f4fa22"><view class="formView data-v-89f4fa22"><view class="formTextarea border-8 data-v-89f4fa22"><view class="textareaTitle u-flex data-v-89f4fa22"><view class="u-flex-1 data-v-89f4fa22">课程图标:</view><u-icon vue-id="{{('e1195a4c-3')+','+('e1195a4c-1')}}" name="photo" color="#000" size="28" data-event-opts="{{[['^click',[['choseLogo']]]]}}" bind:click="__e" class="data-v-89f4fa22" bind:__l="__l"></u-icon></view><view class="formLogo data-v-89f4fa22"><u--image vue-id="{{('e1195a4c-4')+','+('e1195a4c-1')}}" showLoading="{{true}}" src="{{$root.f0}}" width="240rpx" height="240rpx" radius="4" data-event-opts="{{[['^click',[['clickLogo']]]]}}" bind:click="__e" class="data-v-89f4fa22" bind:__l="__l"></u--image></view></view><view class="formList data-v-89f4fa22"><view class="data-v-89f4fa22">课程名称:</view><u--input bind:input="__e" vue-id="{{('e1195a4c-5')+','+('e1195a4c-1')}}" border="{{false}}" value="{{formList.courseName}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseName','$event',[]],['formList']]]]]}}" class="data-v-89f4fa22" bind:__l="__l"></u--input></view><view class="formList data-v-89f4fa22"><view class="data-v-89f4fa22">课程时长（分）:</view><u--input bind:input="__e" vue-id="{{('e1195a4c-6')+','+('e1195a4c-1')}}" border="{{false}}" value="{{formList.courseDuration}}" data-event-opts="{{[['^input',[['__set_model',['$0','courseDuration','$event',[]],['formList']]]]]}}" class="data-v-89f4fa22" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="formList data-v-89f4fa22" bindtap="__e"><view class="data-v-89f4fa22">付费方式:</view><u--input bind:input="__e" vue-id="{{('e1195a4c-7')+','+('e1195a4c-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择付费方式" value="{{cardTypeName}}" data-event-opts="{{[['^input',[['__set_model',['','cardTypeName','$event',[]]]]]]}}" class="data-v-89f4fa22" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="formList data-v-89f4fa22" bindtap="__e"><view class="data-v-89f4fa22">课程类型:</view><u--input bind:input="__e" vue-id="{{('e1195a4c-8')+','+('e1195a4c-1')}}" border="{{false}}" disabled="{{true}}" placeholder="请选择课程类型" value="{{courseTypeName}}" data-event-opts="{{[['^input',[['__set_model',['','courseTypeName','$event',[]]]]]]}}" class="data-v-89f4fa22" bind:__l="__l"></u--input></view></view></view><u-picker vue-id="{{('e1195a4c-9')+','+('e1195a4c-1')}}" show="{{showType}}" columns="{{actions}}" keyName="dictLabel" data-event-opts="{{[['^confirm',[['typeSelect']]],['^cancel',[['e3']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-89f4fa22" bind:__l="__l"></u-picker><u-picker vue-id="{{('e1195a4c-10')+','+('e1195a4c-1')}}" show="{{showLX}}" columns="{{actionsLX}}" keyName="courseTypeName" data-event-opts="{{[['^confirm',[['courseSelect']]],['^cancel',[['e4']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-89f4fa22" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-89f4fa22"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-89f4fa22" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增课程</view></view></view></theme-wrap>