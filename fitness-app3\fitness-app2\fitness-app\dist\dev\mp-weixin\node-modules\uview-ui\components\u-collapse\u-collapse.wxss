/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node_modules/uview-ui/components/u-collapse/u-collapse.vue?vue&type=style&index=0&id=e7ce28f2&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-e7ce28f2, .theme-color.u-content-color.data-v-e7ce28f2 {
  color: var(--base-color);
}
.theme-bg-color.data-v-e7ce28f2, .bgc.data-v-e7ce28f2 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-e7ce28f2, .nbc.data-v-e7ce28f2 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-e7ce28f2, .bc.data-v-e7ce28f2 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-e7ce28f2, .lbc.data-v-e7ce28f2 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-e7ce28f2, .btc.data-v-e7ce28f2 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-e7ce28f2, .ltc.data-v-e7ce28f2 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-e7ce28f2 {
  background: var(--scroll-item-bg-color);
}
view.data-v-e7ce28f2, scroll-view.data-v-e7ce28f2, swiper-item.data-v-e7ce28f2 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
