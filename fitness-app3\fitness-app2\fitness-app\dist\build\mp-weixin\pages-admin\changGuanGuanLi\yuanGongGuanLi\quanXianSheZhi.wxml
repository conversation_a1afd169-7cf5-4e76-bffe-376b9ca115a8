<theme-wrap scoped-slots-compiler="augmented" vue-id="76eebf4d-1" class="data-v-69a8ee3f" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-69a8ee3f" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('76eebf4d-2')+','+('76eebf4d-1')}}" title="员工权限设置" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-69a8ee3f" bind:__l="__l"></u-navbar><view class="u-m-t-30 u-m-l-30 u-font-32 bold data-v-69a8ee3f">权限人员</view><view class="container u-p-t-40 u-p-b-40 data-v-69a8ee3f"><view class="contView u-p-b-20 w-100 border-16 bg-fff data-v-69a8ee3f"><block wx:for="{{7}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="user_avatar data-v-69a8ee3f"><u-avatar vue-id="{{('76eebf4d-3-'+__i0__)+','+('76eebf4d-1')}}" size="80rpx" src="{{src}}" shape="circle" class="data-v-69a8ee3f" bind:__l="__l"></u-avatar><view class="w-100 u-font-24 data-v-69a8ee3f">晴天雨绵</view></view></block><view class="user_avatar data-v-69a8ee3f"><u-icon vue-id="{{('76eebf4d-4')+','+('76eebf4d-1')}}" size="80rpx" name="plus-circle" data-event-opts="{{[['^click',[['addMore']]]]}}" bind:click="__e" class="data-v-69a8ee3f" bind:__l="__l"></u-icon><view class="w-100 data-v-69a8ee3f"></view></view><view class="user_avatar data-v-69a8ee3f"><u-icon vue-id="{{('76eebf4d-5')+','+('76eebf4d-1')}}" size="80rpx" name="minus-circle" data-event-opts="{{[['^click',[['removeMore']]]]}}" bind:click="__e" class="data-v-69a8ee3f" bind:__l="__l"></u-icon><view class="w-100 data-v-69a8ee3f"></view></view></view></view><view class="u-m-t-30 u-m-l-30 u-font-32 bold data-v-69a8ee3f">会员管理权限</view><view class="container u-p-t-40 u-p-b-40 data-v-69a8ee3f"><view class="contView u-p-r-30 u-p-l-30 w-100 border-16 bg-fff data-v-69a8ee3f"><u-checkbox-group vue-id="{{('76eebf4d-6')+','+('76eebf4d-1')}}" borderBottom="{{true}}" placement="column" iconPlacement="right" value="{{checkboxValue7}}" data-event-opts="{{[['^change',[['checkboxChange']]],['^input',[['__set_model',['','checkboxValue7','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-69a8ee3f" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{checkboxList7}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-checkbox vue-id="{{('76eebf4d-7-'+index)+','+('76eebf4d-6')}}" customStyle="{{({marginBottom:'16px'})}}" label="{{item.name}}" name="{{item.name}}" class="data-v-69a8ee3f" bind:__l="__l"></u-checkbox></block></u-checkbox-group></view></view></view></theme-wrap>