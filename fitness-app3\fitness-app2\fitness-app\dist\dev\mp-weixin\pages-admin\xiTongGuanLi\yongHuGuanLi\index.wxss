/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/layout/theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-7a7df696, .theme-color.u-content-color.data-v-7a7df696 {
  color: var(--base-color);
}
.theme-bg-color.data-v-7a7df696, .bgc.data-v-7a7df696 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-7a7df696, .nbc.data-v-7a7df696 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-7a7df696, .bc.data-v-7a7df696 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-7a7df696, .lbc.data-v-7a7df696 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-7a7df696, .btc.data-v-7a7df696 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-7a7df696, .ltc.data-v-7a7df696 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-7a7df696 {
  background: var(--scroll-item-bg-color);
}
.theme-wrap.data-v-7a7df696 {
  min-height: 100vh;
  width: 100vw;
  background: var(--base-bg-color);
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages-admin/xiTongGuanLi/yongHuGuanLi/index.vue?vue&type=style&index=0&id=69600ba2&lang=scss&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-69600ba2, .theme-color.u-content-color.data-v-69600ba2 {
  color: var(--base-color);
}
.theme-bg-color.data-v-69600ba2, .bgc.data-v-69600ba2 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-69600ba2, .nbc.data-v-69600ba2 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-69600ba2, .bc.data-v-69600ba2 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-69600ba2, .lbc.data-v-69600ba2 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-69600ba2, .btc.data-v-69600ba2 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-69600ba2, .ltc.data-v-69600ba2 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-69600ba2 {
  background: var(--scroll-item-bg-color);
}
.shopView.data-v-69600ba2 {
  width: 375rpx;
}
.title.data-v-69600ba2 {
  line-height: 44rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #000;
  margin-bottom: 20rpx;
}
.active.data-v-69600ba2 {
  background-color: #f9ae3d;
  color: white;
}
.suspension.data-v-69600ba2 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  left: 20rpx;
  z-index: 99;
  bottom: 100rpx;
}
.searchView.data-v-69600ba2 {
  height: 88rpx;
  padding: 5rpx 40rpx;
}
.scrollView.data-v-69600ba2 {
  padding: 0 40rpx;
}
.userList.data-v-69600ba2 {
  height: 100rpx;
  display: flex;
}
.userList .user_avatar.data-v-69600ba2 {
  height: 100rpx;
  width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.userList .user_textView.data-v-69600ba2 {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 4rpx solid #eee;
}
.userList .user_textView .user_tag.data-v-69600ba2 {
  display: flex;
  width: 100%;
}
.userList .user_textView .user_tag .tag.data-v-69600ba2 {
  color: #7f663f;
  background-color: #faf5e8;
  font-size: 20rpx;
  padding: 4rpx 6rpx;
  margin-right: 10rpx;
}
.userList .user_rightView.data-v-69600ba2 {
  display: flex;
  align-items: center;
  width: 100rpx;
  border-bottom: 4rpx solid #eee;
}
.con.data-v-69600ba2  .u-popup__content {
  border-radius: 16rpx;
  padding: 40rpx;
}
.con .saveBtn.data-v-69600ba2 {
  width: 100%;
  height: 80rpx;
  margin: 0 auto;
  text-align: center;
  line-height: 80rpx;
  border: 1px solid;
  border-radius: 40rpx;
}
