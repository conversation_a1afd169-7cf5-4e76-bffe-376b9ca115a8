<view class="main data-v-2a4bdcb8"><view data-event-opts="{{[['tap',[['showModal',['$event']]]]]}}" class="input data-v-2a4bdcb8" bindtap="__e"><input style="{{(disabled?'color:#c0c4cc':'')}}" placeholder="{{placeholder}}" placeholder-style="color: rgba(102, 102, 102, 0.25);" placeholder-class="zqs-select-placeholder-class" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['','_value','$event',[]]]]]]}}" value="{{_value}}" bindinput="__e" class="data-v-2a4bdcb8"/><block wx:if="{{showArrow&&!_value}}"><image class="selector-icon data-v-2a4bdcb8" src="/static/right_icon.png"></image></block></view><view data-event-opts="{{[['tap',[['hideModal',['$event']]]]]}}" class="{{['select-modal','data-v-2a4bdcb8',isShowModal?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="select-dialog data-v-2a4bdcb8" style="{{'background-color:'+(bgColor)+';'}}" catchtap="__e"><view class="title-main data-v-2a4bdcb8"><text class="title-detail data-v-2a4bdcb8">{{title}}</text></view><block wx:if="{{showSearch}}"><view class="search-box data-v-2a4bdcb8"><input class="search-input data-v-2a4bdcb8" confirm-type="search" placeholder="输入内容进行模糊查询" placeholder-style="color:rgba(102, 102, 102, 0.25);" data-event-opts="{{[['input',[['__set_model',['','searchInput','$event',[]]]]]]}}" value="{{searchInput}}" bindinput="__e"/><block wx:if="{{showSearchBtn}}"><text data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-text data-v-2a4bdcb8" bindtap="__e">搜索</text></block></view></block><view class="select-content data-v-2a4bdcb8"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',index]]]]]]]}}" class="select-item data-v-2a4bdcb8" style="{{(item.m0?'color:'+selectColor+';background-color:'+selectBgColor+';':'color:'+color+';')}}" bindtap="__e"><view class="title data-v-2a4bdcb8">{{item.m1}}</view><block wx:if="{{item.m2}}"><text class="selectIcon icongou data-v-2a4bdcb8"></text></block></view></block></view><view class="select-bar bg-white data-v-2a4bdcb8"><button class="mini-btn action data-v-2a4bdcb8" plain="true" type="default" size="default" data-event-opts="{{[['tap',[['empty',['$event']]]]]}}" bindtap="__e">{{''+emptyText+''}}</button><button class="mini-btn action data-v-2a4bdcb8" type="primary" size="default" data-event-opts="{{[['tap',[['confirmClick',['$event']]]]]}}" bindtap="__e">{{''+confirmText+''}}</button></view></view></view></view>