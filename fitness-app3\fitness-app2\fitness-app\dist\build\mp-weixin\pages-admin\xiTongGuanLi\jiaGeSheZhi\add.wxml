<theme-wrap scoped-slots-compiler="augmented" vue-id="d2840db8-1" class="data-v-84ef72d2" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-84ef72d2" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('d2840db8-2')+','+('d2840db8-1')}}" title="价格编辑" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-84ef72d2" bind:__l="__l"></u-navbar><view class="formView data-v-84ef72d2"><view class="formList data-v-84ef72d2"><view class="data-v-84ef72d2">续费名称:</view><u-input bind:input="__e" vue-id="{{('d2840db8-3')+','+('d2840db8-1')}}" border="{{false}}" value="{{formList.renewalName}}" data-event-opts="{{[['^input',[['__set_model',['$0','renewalName','$event',[]],['formList']]]]]}}" class="data-v-84ef72d2" bind:__l="__l"></u-input></view><view class="formList data-v-84ef72d2"><view class="data-v-84ef72d2">续费天数:</view><view class="u-flex-1 data-v-84ef72d2"><u-input bind:input="__e" vue-id="{{('d2840db8-4')+','+('d2840db8-1')}}" border="{{false}}" type="number" value="{{formList.renewalDays}}" data-event-opts="{{[['^input',[['__set_model',['$0','renewalDays','$event',[]],['formList']]]]]}}" class="data-v-84ef72d2" bind:__l="__l"></u-input></view></view><view class="formList data-v-84ef72d2"><view class="data-v-84ef72d2">续费价格:</view><u-input bind:input="__e" vue-id="{{('d2840db8-5')+','+('d2840db8-1')}}" border="{{false}}" type="number" value="{{formList.renewalPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','renewalPrice','$event',[]],['formList']]]]]}}" class="data-v-84ef72d2" bind:__l="__l"></u-input></view><view class="formList data-v-84ef72d2"><view class="data-v-84ef72d2">续费折扣:</view><u-input bind:input="__e" vue-id="{{('d2840db8-6')+','+('d2840db8-1')}}" border="{{false}}" type="number" value="{{formList.renewalDiscount}}" data-event-opts="{{[['^input',[['__set_model',['$0','renewalDiscount','$event',[]],['formList']]]]]}}" class="data-v-84ef72d2" bind:__l="__l"></u-input></view><view class="formList data-v-84ef72d2"><view class="data-v-84ef72d2">折扣价格:</view><u-input bind:input="__e" vue-id="{{('d2840db8-7')+','+('d2840db8-1')}}" border="{{false}}" type="number" value="{{formList.discountPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','discountPrice','$event',[]],['formList']]]]]}}" class="data-v-84ef72d2" bind:__l="__l"></u-input></view></view><view class="whiteView data-v-84ef72d2"></view><view class="bottonBtn u-flex data-v-84ef72d2"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-84ef72d2" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">确认修改</view></view></view></theme-wrap>