{"version": 3, "file": "pages/notFind.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAO,CAAC,8DAA0B;AAC7C;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCCAA;EACAA,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;EACAC,OAAA;AACA;;;;;;;;;;ACvBA;;;;;;;;;;;;;;;ACAAC,mBAAA;AAGA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,QAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAsC,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAHtC;AACAG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLoG;AACpH;AACA,CAA2D;AACL;AACtD,CAAoE;;;AAGpE;AACgI;AAChI,gBAAgB,4IAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBqe,CAAC,+DAAe,2dAAG,EAAC;;;;;;;;;;;;;;;;;ACA6b,CAAC,+DAAe,m4BAAG,EAAC", "sources": ["webpack:///./src/pages/notFind.vue?0d47", "uni-app:///src/pages/notFind.vue", "webpack:///./src/pages/notFind.vue?335d", "uni-app:///src/main.js", "webpack:///./src/pages/notFind.vue?82c1", "webpack:///./src/pages/notFind.vue?0585", "webpack:///./src/pages/notFind.vue?ab65", "webpack:///./src/pages/notFind.vue?98ab"], "sourcesContent": ["var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = require(\"@/static/images/logo.png\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <view class=\"content\">\n    <view class=\"notFind-index\">\n      <view class=\"logo-wrap\"><img src=\"@/static/images/logo.png\"/></view>\n      <view class=\"notFind-text\">\n        <view class=\"notFind-title\">\n          404~\n        </view>\n        <view class=\"notFind-dec\">\n          页面走丢了！\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {};\n  },\n  onLoad() {},\n  methods: {}\n};\n</script>\n\n<style lang=\"scss\">\n.notFind-index {\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  .logo-wrap {\n    display: inline-block;\n    line-height: 0;\n    padding-bottom: 180upx;\n    image {\n      width: 210upx;\n      height: 240upx;\n    }\n  }\n  .notFind-text {\n    padding: 0 80upx;\n    padding-bottom: 180upx;\n    text-align: center;\n    font-size: 60upx;\n    .notFind-title {\n      color: #4c4c4c;\n    }\n    .notFind-dec {\n      padding-top: 50upx;\n      color: #4c4c4c;\n    }\n  }\n}\n</style>\n", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/notFind.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./notFind.vue?vue&type=template&id=3756d67a&\"\nvar renderjs\nimport script from \"./notFind.vue?vue&type=script&lang=js&\"\nexport * from \"./notFind.vue?vue&type=script&lang=js&\"\nimport style0 from \"./notFind.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/notFind.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./notFind.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./notFind.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./notFind.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./notFind.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./notFind.vue?vue&type=template&id=3756d67a&\""], "names": ["data", "onLoad", "methods", "require", "_vue", "_interopRequireDefault", "_notFind", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}