/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/layout/theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-7a7df696, .theme-color.u-content-color.data-v-7a7df696 {
  color: var(--base-color);
}
.theme-bg-color.data-v-7a7df696, .bgc.data-v-7a7df696 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-7a7df696, .nbc.data-v-7a7df696 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-7a7df696, .bc.data-v-7a7df696 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-7a7df696, .lbc.data-v-7a7df696 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-7a7df696, .btc.data-v-7a7df696 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-7a7df696, .ltc.data-v-7a7df696 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-7a7df696 {
  background: var(--scroll-item-bg-color);
}
.theme-wrap.data-v-7a7df696 {
  min-height: 100vh;
  width: 100vw;
  background: var(--base-bg-color);
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages-admin/siJiaoGuanLi/siJiaoYuYueGuanLi/index.vue?vue&type=style&index=0&id=60f226a8&scoped=true&lang=scss& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.theme-color.data-v-60f226a8, .theme-color.u-content-color.data-v-60f226a8 {
  color: var(--base-color);
}
.theme-bg-color.data-v-60f226a8, .bgc.data-v-60f226a8 {
  background: var(--base-bg-color);
}
.theme-nav-bg-color.data-v-60f226a8, .nbc.data-v-60f226a8 {
  background: var(--navbar-color);
}
.theme-button-color.data-v-60f226a8, .bc.data-v-60f226a8 {
  background: var(--button-bg-color);
}
.theme-light-button-color.data-v-60f226a8, .lbc.data-v-60f226a8 {
  background: var(--button-light-bg-color) !important;
}
.theme-button-text-color.data-v-60f226a8, .btc.data-v-60f226a8 {
  color: var(--button-text-color);
}
.theme-light-text-color.data-v-60f226a8, .ltc.data-v-60f226a8 {
  color: var(--button-light-bg-color);
}
.scroll-item-wrap.data-v-60f226a8 {
  background: var(--scroll-item-bg-color);
}
.tabsView.data-v-60f226a8 .u-tabs .u-tabs__wrapper__nav__item {
  width: 50%;
}
.title.data-v-60f226a8 {
  margin: 20rpx 0;
  font-size: 26rpx;
  color: #7F7F80;
}
.reservation.data-v-60f226a8 {
  padding: 0 40rpx;
}
.reservation .reservationView.data-v-60f226a8 {
  background: #fff8f6;
  padding: 20rpx;
}
.reservation .reservationView .resTop.data-v-60f226a8 {
  margin-bottom: 10rpx;
}
.reservation .reservationView .resTop .resIcon.data-v-60f226a8 {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  text-align: center;
  line-height: 32rpx;
  font-size: 24rpx;
}
.reservation .reservationView .resCenter.data-v-60f226a8 {
  margin-bottom: 10rpx;
}
.reservation .reservationView .resCenter .value.data-v-60f226a8 {
  font-size: 32rpx;
  color: black;
}
.moreBtn.data-v-60f226a8,
.addHuiYuan.data-v-60f226a8 {
  width: 140rpx;
  height: 50rpx;
  margin: 0 auto;
  font-size: 26rpx;
  text-align: center;
  line-height: 50rpx;
  border: 1px solid;
  border-radius: 40rpx;
}
