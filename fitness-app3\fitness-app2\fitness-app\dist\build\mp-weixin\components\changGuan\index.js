(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["components/changGuan/index"],{11985:function(n,e,t){"use strict";t.r(e),t.d(e,{__esModule:function(){return i.__esModule},default:function(){return d}});var o,u={uNavbar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-navbar/u-navbar")]).then(t.bind(t,66372))}},c=function(){var n=this,e=n.$createElement,t=(n._self._c,n.__map(n.shopList,(function(e,t){var o=n.__get_orig(e),u=n._f("Img")(e.logo),c=(e.distance/1e3).toFixed(2);return{$orig:o,f0:u,g0:c}})));n.$mp.data=Object.assign({},{$root:{l0:t}})},a=[],i=t(54917),s=i["default"],r=t(71402),l=t.n(r),h=(l(),t(18535)),f=(0,h["default"])(s,c,a,!1,null,null,null,!1,u,o),d=f.exports},54917:function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;t(77020),o(t(68466));function o(n){return n&&n.__esModule?n:{default:n}}e["default"]={props:{shopList:{type:Array,default:[]}},data:function(){return{show:!1}},mounted:function(){},onShow:function(){},methods:{clickhide:function(){this.show=!1},clickshow:function(){this.show=!0},changeChangGuan:function(n){this.$emit("changeChangGuan",n)},onTouchStart:function(n){this.startY=n.changedTouches[0].clientY},onTouchMove:function(n){var e=n.changedTouches[0].clientY;this.startY-e>100&&(this.show=!1)}}}},71402:function(){}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["components/changGuan/index-create-component"],{},function(n){n("81715")["createComponent"](n(11985))}]);