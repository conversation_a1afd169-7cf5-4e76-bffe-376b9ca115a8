<theme-wrap scoped-slots-compiler="augmented" vue-id="8dd740cc-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><u-toast class="vue-ref" vue-id="{{('8dd740cc-2')+','+('8dd740cc-1')}}" data-ref="uToast" bind:__l="__l"></u-toast><view data-event-opts="{{[['touchstart',[['onTouchStart',['$event']]]],['touchmove',[['onTouchMove',['$event']]]],['touchend',[['onTouchEnd',['$event']]]],['touchcancel',[['onTouchEnd',['$event']]]]]}}" class="container" style="padding-bottom:130rpx;" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindtouchcancel="__e"><view class="nav-bar"><view class="left u-border font-bold border-32 u-p-r-20 u-p-l-20 u-p-t-4 u-p-b-4 u-flex u-row-center u-col-center"><block wx:if="{{!isAdmin}}"><view data-event-opts="{{[['tap',[['toQrcodePage',['$event']]]]]}}" class="u-flex u-p-t-4 u-p-b-4 u-row-center u-col-center w-100" bindtap="__e"><image style="width:26rpx;height:26rpx;" src="/static/images/icons/qrcode-white.png" mode="widthFix" lazy-load="{{true}}"></image><view class="fc-fff u-font-24 u-m-l-10">签到</view></view></block><block wx:else><u-icon vue-id="{{('8dd740cc-3')+','+('8dd740cc-1')}}" name="list" color="{{$root.m1['buttonTextColor']}}" labelColor="{{$root.m2['buttonTextColor']}}" size="21" labelPos="right" data-event-opts="{{[['^click',[['gotoAdmin']]]]}}" bind:click="__e" bind:__l="__l"></u-icon><u-line vue-id="{{('8dd740cc-4')+','+('8dd740cc-1')}}" direction="column" hairline="{{false}}" length="16" margin="0 8px" bind:__l="__l"></u-line><view data-event-opts="{{[['tap',[['toQrcodePage',['$event']]]]]}}" class="u-flex u-p-t-4 u-p-b-4 u-row-center u-col-center w-100" bindtap="__e"><image style="width:30rpx;height:30rpx;" src="/static/images/icons/qrcode-white.png" mode="widthFix" lazy-load="{{true}}"></image></view></block></view><view data-event-opts="{{[['tap',[['showChangGuanSelector',['$event']]]]]}}" class="title" bindtap="__e"><u-icon vue-id="{{('8dd740cc-5')+','+('8dd740cc-1')}}" name="arrow-down" size="24" color="#fff" bind:__l="__l"></u-icon></view><view class="right"></view></view><block alt="场馆信息"><view class="u-relative"><image style="height:560rpx;width:100%;filter:blur(3px);" src="{{infoImgList[0].src}}" lazy-load="{{true}}"></image><view class="w-100 u-m-b-20 u-absolute" style="height:560rpx;top:0;left:0;padding:40rpx;padding-top:150rpx;"><view class="u-flex-3"><view class="img-wrap flex-0 u-m-b-20" style="aspect-ratio:1;border-radius:50%;overflow:hidden;line-height:0;width:180rpx;margin:auto;"><image class="w-100 flex-0" style="height:160rpx;border-radius:50%;" src="{{$root.f0}}" mode="widthFix" lazy-load="{{true}}"></image></view></view><view class="u-font-38 font-bold u-line-1 u-text-center" style="color:white;">{{''+(detail.shopName||"")+''}}</view><view class="u-flex u-col-start w-100 u-p-t-30"><block wx:if="{{detail.shopIntroduce}}"><view class="desc-blk"><u-read-more vue-id="{{('8dd740cc-6')+','+('8dd740cc-1')}}" showHeight="60" toggle="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-font-28 u-tips-color" style="color:white;">{{''+(detail.shopIntroduce||"暂无简介")+''}}</view></u-read-more></view></block></view></view></view></block><view class="w-100 bg-fff u-p-30"><block wx:if="{{$root.g0}}"><block wx:if="{{$root.g1>1}}"><u-swiper vue-id="{{('8dd740cc-7')+','+('8dd740cc-1')}}" list="{{infoImgList}}" bgColor="transparent" previousMargin="80" nextMargin="80" circular="{{true}}" indicator="{{true}}" autoplay="{{true}}" radius="5" keyName="src" data-event-opts="{{[['^click',[['previewImage']]]]}}" bind:click="__e" bind:__l="__l"></u-swiper></block><block wx:else><view class="w-100"><image class="w-100" src="{{$root.f1}}" mode="widthFix" lazy-load="{{true}}"></image></view></block></block></view><view class="u-flex bg-fff u-p-30"><view data-event-opts="{{[['tap',[['openMap',['$event']]]]]}}" class="u-flex u-flex-1" bindtap="__e"><u-icon vue-id="{{('8dd740cc-8')+','+('8dd740cc-1')}}" name="map" color="{{$root.m3['buttonLightBgColor']}}" size="20" bind:__l="__l"></u-icon><view class="u-line-1 u-p-l-10">{{detail.address}}</view></view><view style="width:1px;height:16px;margin:auto;background-color:#666;margin-right:20rpx;"></view><view data-event-opts="{{[['tap',[['callPhone',['$event']]]]]}}" class="u-flex" style="width:40px;" bindtap="__e"><u-icon vue-id="{{('8dd740cc-9')+','+('8dd740cc-1')}}" name="phone" color="{{$root.m4['buttonLightBgColor']}}" size="32" bind:__l="__l"></u-icon></view></view><block alt="教练团队"><view class="u-m-t-20 u-p-t-40 u-p-b-40 u-flex w-100 u-row-between u-p-r-30 u-p-l-30 bg-fff"><text class="font-bold u-font-36">教练团队</text><navigator url="/pages/yu-yue/index" open-type="switchTab" hover-class="none"><u-icon vue-id="{{('8dd740cc-10')+','+('8dd740cc-1')}}" label="查看更多" name="arrow-right" labelPos="left" size="15" labelSize="15" color="#999" bind:__l="__l"></u-icon></navigator></view><view class="w-100 border-16 u-m-b-20 u-flex u-p-r-30 u-p-l-30 bg-fff u-p-b-30" style="overflow:scroll;"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toJiaoLianDetail',['$0'],[[['jiaoLianList','',index]]]]]]]}}" class="jiaolian-item flex-0 u-flex-col u-row-center u-col-center border-16 u-m-r-30" style="background-color:#f3f3f3;" bindtap="__e"><view style="width:160rpx;height:160rpx;border-radius:50%;overflow:hidden;"><block wx:if="{{item.$orig.coachAvatar}}"><image class="w-100" style="height:160rpx;" src="{{item.f2}}" lazy-load="{{true}}"></image></block><block wx:else><image class="w-100" style="height:160rpx;" src="/static/images/default/coach.jpg" lazy-load="{{true}}"></image></block></view><view class="u-p-t-20 u-p-b-10 u-font-34 font-bold u-line-1 u-text-center">{{''+item.$orig.nickName+''}}</view><view class="u-tips-color u-font-28 u-line-2 u-text-center" style="height:2.8rem;">{{''+(item.$orig.aphorism||'暂无格言')+''}}</view></view></block></view></block><block alt="会员卡"><block wx:for="{{$root.l2}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view><view class="u-p-t-40 u-p-b-40 u-flex w-100 u-row-between u-p-r-30 u-p-l-30 bg-fff"><text class="font-bold u-font-36">{{i.$orig.type_text}}</text><navigator url="{{'/pages/huiYuanKa/index?id='+shopId}}" open-type="navigate" hover-class="none"><u-icon vue-id="{{('8dd740cc-11-'+index)+','+('8dd740cc-1')}}" label="查看更多" name="arrow-right" labelPos="left" size="15" labelSize="15" color="#999" bind:__l="__l"></u-icon></navigator></view><view class="border-16 u-m-b-20 u-flex u-p-r-30 u-p-l-30 bg-fff u-p-b-30" style="overflow:scroll;"><block wx:for="{{i.l1}}" wx:for-item="lit" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['toHuiYuanKa',['$0'],[[['huiYuanKaList','',index],['list','',idx,'memberCardId']]]]]]]}}" class="huiyuan-item u-flex u-row-start u-col-start bg-fff u-p-b-30 u-p-t-30" bindtap="__e"><view class="u-flex-6 u-relative"><view class="u-relative overflow-hidden" style="line-height:0;"><image class="border-16" style="width:300rpx;height:160rpx;" src="{{lit.f3}}" mode="widthFix" lazy-load="{{true}}"></image><view class="u-font-28 w-100 font-bold fc-fff u-absolute u-text-center expired-blk">{{'有效期：'+(lit.$orig.validDays<=0?'无限期':lit.$orig.validDays+'天')+''}}</view></view></view></view></block></view></view></block></block></view><view class="huodonglist"><block wx:for="{{huodonglist}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['jump',['$0'],[[['huodonglist','',__i0__]]]]]]]}}" style="margin-top:30rpx;" bindtap="__e"><image class="shake" src="/static/images/icons/gift.jpg" mode="widthFix" lazy-load="{{true}}"></image></view></block></view><chang-guan class="vue-ref" vue-id="{{('8dd740cc-12')+','+('8dd740cc-1')}}" shopList="{{shopList}}" data-ref="changGuan" data-event-opts="{{[['^changeChangGuan',[['changeChangGuan']]]]}}" bind:changeChangGuan="__e" bind:__l="__l"></chang-guan><zw-tab-bar vue-id="{{('8dd740cc-13')+','+('8dd740cc-1')}}" selIdx="{{0}}" bigIdx="{{2}}" bind:__l="__l"></zw-tab-bar></view></theme-wrap>