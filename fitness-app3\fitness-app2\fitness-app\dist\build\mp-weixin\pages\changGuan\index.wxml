<theme-wrap scoped-slots-compiler="augmented" vue-id="ceae42c8-1" bind:__l="__l" vue-slots="{{['content']}}"><view class="chang-guan-list" slot="content" wx:if="{{$root.m0}}"><nav-bar vue-id="{{('ceae42c8-2')+','+('ceae42c8-1')}}" buttonBgColor="{{$root.m1['navBarColor']}}" buttonTextColor="{{$root.m2['buttonTextColor']}}" bind:__l="__l"></nav-bar><view class="u-p-40"><u-notice-bar vue-id="{{('ceae42c8-3')+','+('ceae42c8-1')}}" text="{{text1}}" bind:__l="__l"></u-notice-bar><view data-event-opts="{{[['tap',[['choseAdvice',['$event']]]]]}}" class="formList" bindtap="__e"><view>位置:</view><u--input bind:input="__e" vue-id="{{('ceae42c8-4')+','+('ceae42c8-1')}}" border="{{false}}" disabled="{{true}}" value="{{address}}" data-event-opts="{{[['^input',[['__set_model',['','address','$event',[]]]]]]}}" bind:__l="__l"></u--input></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeChangGuan',[index]]]]]}}" class="changguan-item-blk u-relative overflow-hidden u-m-b-40 border-24" bindtap="__e"><view class="w-100 placeholder overflow-hidden" style="aspect-ratio:2;line-height:0;"><image class="w-100" src="{{item.f0}}" mode="widthFix"></image></view><view class="u-absolute u-p-30 w-100" style="bottom:0;left:0;background-color:rgba(255, 255, 255, 0.6);"><view class="u-font-40 font-bold u-line u-m-b-20 w-100 u-text-center">{{''+item.$orig.shopName+''}}</view><view class="u-flex w-100 u-font-30" style="color:#555;"><view class="u-flex-5 u-p-r-20 u-line-1">{{''+item.$orig.address+''}}</view><view class="u-flex-1">{{''+item.g0+'km'}}</view></view></view><view class="u-absolute" style="top:20rpx;right:20rpx;"></view></view></block></view></view></theme-wrap>