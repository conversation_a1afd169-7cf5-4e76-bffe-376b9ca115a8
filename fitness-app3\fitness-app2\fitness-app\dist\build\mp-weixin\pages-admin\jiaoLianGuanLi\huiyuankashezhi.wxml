<theme-wrap scoped-slots-compiler="augmented" vue-id="9cbb4aee-1" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-7712b07e" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('9cbb4aee-2')+','+('9cbb4aee-1')}}" title="修改教练管理" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-7712b07e" bind:__l="__l"></u-navbar><view class="formView data-v-7712b07e"><view class="formTextarea border-8 data-v-7712b07e"><view class="textareaTitle u-flex data-v-7712b07e"><view class="u-flex-1 data-v-7712b07e">已有会员卡:</view></view><view class="formLogo data-v-7712b07e"><block wx:for="{{cardList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('9cbb4aee-3-'+index)+','+('9cbb4aee-1')}}" title="{{item.cardName}}" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon" class="data-v-7712b07e"><view data-event-opts="{{[['tap',[['confirm',['$0'],[[['cardList','',index,'memberCardId']]]]]]]}}" style="color:blue;font-size:24rpx;" bindtap="__e" class="data-v-7712b07e">课程设置</view><view data-event-opts="{{[['tap',[['removeCourse',['$0'],[[['cardList','',index,'memberCardId']]]]]]]}}" class="u-flex data-v-7712b07e" style="color:#dd4d51;font-size:24rpx;margin-top:10rpx;" bindtap="__e"><u-icon vue-id="{{('9cbb4aee-4-'+index)+','+('9cbb4aee-3-'+index)}}" name="close-circle" size="12" color="#dd4d51" class="data-v-7712b07e" bind:__l="__l"></u-icon><view class="data-v-7712b07e">移除会员卡</view></view></view></u-cell></block></view></view></view><view class="whiteView data-v-7712b07e"></view><view hidden="{{!(choseList!='')}}" class="bottom-blk bg-fff u-flex w-100 u-p-40 data-v-7712b07e"><view class="u-flex-1 u-m-r-10 data-v-7712b07e"><u-button vue-id="{{('9cbb4aee-5')+','+('9cbb4aee-1')}}" color="{{$root.m3['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['addCard']]]]}}" bind:click="__e" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['default']}}">添加会员卡</u-button></view></view><u-popup vue-id="{{('9cbb4aee-6')+','+('9cbb4aee-1')}}" show="{{showPopup}}" data-event-opts="{{[['^close',[['e1']]],['^open',[['openPopup']]]]}}" bind:close="__e" bind:open="__e" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['default']}}"><view class="formView data-v-7712b07e"><view class="formTextarea border-8 data-v-7712b07e"><view class="textareaTitle u-flex data-v-7712b07e"><view class="u-flex-1 data-v-7712b07e">会员卡课程设置:</view></view><view class="formLogo data-v-7712b07e"><block wx:for="{{allTabList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('9cbb4aee-7-'+index)+','+('9cbb4aee-6')}}" title="{{item.courseName}}" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon" class="data-v-7712b07e"><block wx:if="{{item.bind!=true}}"><view data-event-opts="{{[['tap',[['bindToCard',['$0'],[[['allTabList','',index,'courseId']]]]]]]}}" style="color:blue;font-size:24rpx;" bindtap="__e" class="data-v-7712b07e">添加课程</view></block><block wx:else><view data-event-opts="{{[['tap',[['bindCancelCard',['$0'],[[['allTabList','',index,'courseId']]]]]]]}}" style="color:#dd4d51;font-size:24rpx;" bindtap="__e" class="data-v-7712b07e">取消设置</view></block></view></u-cell></block></view></view></view></u-popup><u-popup vue-id="{{('9cbb4aee-8')+','+('9cbb4aee-1')}}" show="{{show}}" data-event-opts="{{[['^close',[['e2']]],['^open',[['open']]]]}}" bind:close="__e" bind:open="__e" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['default']}}"><view class="formView data-v-7712b07e"><view class="formTextarea border-8 data-v-7712b07e"><view class="textareaTitle u-flex data-v-7712b07e"><view class="u-flex-1 data-v-7712b07e">绑定会员卡:</view></view><view class="formLogo data-v-7712b07e"><block wx:for="{{notCardList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('9cbb4aee-9-'+index)+','+('9cbb4aee-8')}}" title="{{item.cardName}}" class="data-v-7712b07e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon" class="data-v-7712b07e"><view data-event-opts="{{[['tap',[['bindCard',['$0'],[[['notCardList','',index,'memberCardId']]]]]]]}}" style="color:blue;font-size:24rpx;" bindtap="__e" class="data-v-7712b07e">绑定会员卡</view></view></u-cell></block></view></view></view></u-popup></view></theme-wrap>