(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-loading-page/u-loading-page"],{2201:function(){},43287:function(n,e,u){"use strict";var o=u(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=t(u(10994));function t(n){return n&&n.__esModule?n:{default:n}}e["default"]={name:"u-loading-page",mixins:[o.$u.mpMixin,o.$u.mixin,i.default],data:function(){return{}},methods:{}}},51873:function(n,e,u){"use strict";u.r(e),u.d(e,{__esModule:function(){return l.__esModule},default:function(){return m}});var o,i={uTransition:function(){return Promise.all([u.e("common/vendor"),u.e("node-modules/uview-ui/components/u-transition/u-transition")]).then(u.bind(u,68270))},uLoadingIcon:function(){return Promise.all([u.e("common/vendor"),u.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(u.bind(u,94597))}},t=function(){var n=this,e=n.$createElement,u=(n._self._c,n.image?n.$u.addUnit(n.iconSize):null),o=n.image?n.$u.addUnit(n.iconSize):null,i=n.image?null:n.$u.addUnit(n.iconSize),t=n.$u.addUnit(n.fontSize);n.$mp.data=Object.assign({},{$root:{g0:u,g1:o,g2:i,g3:t}})},a=[],l=u(43287),d=l["default"],c=u(2201),s=u.n(c),r=(s(),u(18535)),f=(0,r["default"])(d,t,a,!1,null,"6e15fd16",null,!1,i,o),m=f.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-loading-page/u-loading-page-create-component"],{},function(n){n("81715")["createComponent"](n(51873))}]);