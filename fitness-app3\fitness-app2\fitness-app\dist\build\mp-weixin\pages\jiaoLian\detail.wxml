<theme-wrap scoped-slots-compiler="augmented" vue-id="71dead74-1" bind:__l="__l" vue-slots="{{['content']}}"><view slot="content" wx:if="{{$root.m0}}"><block wx:if="{{loading}}"><view style="width:100vw;position:fixed;height:100vh;z-index:1000;"><u-loading-page vue-id="{{('71dead74-2')+','+('71dead74-1')}}" bind:__l="__l"></u-loading-page></view></block><view><u-sticky vue-id="{{('71dead74-3')+','+('71dead74-1')}}" offset-top="0" zIndex="0" bind:__l="__l" vue-slots="{{['default']}}"><view class="w-100 overflow-hidden" style="max-height:calc(100vh - 200rpx);"><image class="w-100" style="height:300rpx;" src="{{$root.f0}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$0'],['detail.background']]]]]}}" bindtap="__e"></image></view></u-sticky><view data-event-opts="{{[['tap',[['back']]]]}}" class="backIcon" bindtap="__e"><u-icon vue-id="{{('71dead74-4')+','+('71dead74-1')}}" name="arrow-left" color="#2979ff" size="28" bind:__l="__l"></u-icon></view><view class="container u-p-t-60 u-p-b-40 bgc u-relative bottom-placeholder" style="z-index:100;margin-top:-40rpx;border-radius:40rpx 40rpx 0 0;"><block alt="教练信息"><view class="u-p-40 w-100 border-16 u-m-b-20 bg-fff"><view class="u-flex u-col-start w-100 u-p-t-30"><view class="u-flex-3"><view class="img-wrap flex-0 u-m-b-20" style="aspect-ratio:1;border-radius:50%;overflow:hidden;line-height:0;width:150rpx;"><image class="w-100" src="{{$root.f1}}" mode="widthFix"></image></view></view><view class="u-flex-5 u-p-l-30" style="height:150rpx;line-height:150rpx;"><view class="font-bold u-line-1" style="font-size:46rpx;">{{detail.nickName||""}}</view></view><view class="u-flex-2 u-flex" style="height:150rpx;"><u-icon vue-id="{{('71dead74-5')+','+('71dead74-1')}}" name="{{detail.sex=='0'?'man':'woman'}}" size="40" color="{{detail.sex=='0'?'#3c9cff':'#f56c6c'}}" bind:__l="__l"></u-icon></view></view><view class="u-m-t-10">{{detail.aphorism||""}}</view></view></block><view class="w-100 border-16 u-m-t-40" style="overflow:hidden;"><image class="w-100" src="../../static/images/bar.png" mode="widthFix"></image></view><block alt="教练特色"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between"><text class="font-bold u-font-36">教练特色</text></view><view class="w-100 border-16 u-p-20 u-flex"><block wx:for="{{specialty}}" wx:for-item="i" wx:for-index="__i0__"><view class="my-tag activeTag u-m-r-20">{{i}}</view></block></view></block><block alt="教练资质"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between"><text class="font-bold u-font-36">教练资质</text></view><view class="w-100 border-16 u-m-b-20 u-flex u-p-30 bg-fff" style="overflow:scroll;"><block wx:for="{{$root.l0}}" wx:for-item="i" wx:for-index="__i1__"><view class="jiaolian-item flex-0 u-flex-col u-row-center u-col-center border-16 u-m-r-30" style="background-color:#f3f3f3;"><view class="border-16" style="width:300rpx;height:200rpx;overflow:hidden;"><image class="w-100" mode="widthFix" src="{{i.f2}}" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({i:i.$orig})}}" bindtap="__e"></image></view></view></block></view></block><block alt="所授私课"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between"><text class="font-bold u-font-36">所授私课</text></view><view class="w-100 border-16 u-m-b-20 u-p-40 bg-fff"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0>0}}"><view class="course-type-item"><view style="width:140rpx;height:140rpx;border-radius:50%;overflow:hidden;"><block wx:if="{{item.$orig.cover}}"><image class="w-100" style="height:140rpx;" src="{{item.f3}}"></image></block><block wx:else><image class="w-100" style="height:140rpx;" src="/static/images/default/course.png"></image></block></view><view class="course-type-name">{{item.$orig.name}}</view><block wx:if="{{choseKeCheng!=index}}"><view data-event-opts="{{[['tap',[['changeBox',[index,'$0'],[[['siJiaoKeList','',index]]]]]]]}}" class="border-8 flex-0 u-text-center lbc btc font-bold u-font-24 btn-blk u-p-12" bindtap="__e">选择课程</view></block><block wx:else><view class="border-8 disabled flex-0 u-text-center lbc btc font-bold u-font-24 btn-blk u-p-12">已选择</view></block></view></block><block wx:else><u-empty vue-id="{{('71dead74-6-'+index)+','+('71dead74-1')}}" mode="list" text="暂无课程" bind:__l="__l"></u-empty></block></block></view></block><block alt="授课时间"><view class="u-m-t-40 u-m-b-40 u-flex w-100 u-row-between"><text class="font-bold u-font-36">授课时间</text></view><view class="w-100 bg-fff border-16 overflow-hidden" style="margin-bottom:180rpx;"><calendar bind:changeActive="__e" vue-id="{{('71dead74-7')+','+('71dead74-1')}}" data-event-opts="{{[['^changeActive',[['changeActive']]]]}}" bind:__l="__l"></calendar><block wx:if="{{$root.g1}}"><view class="u-border-top u-p-t-20 u-p-b-20 u-relative"><view data-event-opts="{{[['tap',[['handleOfficial',['$event']]]]]}}" class="u-flex u-tips-color u-font-26 u-m-l-10 u-m-t-10 u-m-b-20" bindtap="__e"></view><view hidden="{{!(loading)}}" class="u-absolute u-flex u-row-center u-col-center w-100 h-100" style="top:0;left:0;"><u-loading-icon vue-id="{{('71dead74-8')+','+('71dead74-1')}}" mode="circle" loading="{{true}}" bind:__l="__l"></u-loading-icon></view><view class="courses-wrap"><block wx:for="{{courses}}" wx:for-item="i" wx:for-index="index" wx:key="index"><view class="course-blk u-p-10"><view data-event-opts="{{[['tap',[['changeCurrentIndex',[index,'$0'],[[['courses','',index,'status']]]]]]]}}" class="{{['w-100','u-p-t-20','u-relative','u-p-b-20','u-text-center','course-item','border-16',(i.status!==1)?'disabled':'',(index==currentIndex)?'active':'',(i.status===2)?'expired':'',(i.status===3)?'full':'',(i.status===4)?'rest':'']}}" bindtap="__e">{{''+i.text+''}}</view></view></block></view></view></block><block wx:else><view class="w-100 u-p-t-80 u-border-top u-flex-col u-row-center u-col-center u-p-b-80"><image style="width:300rpx;height:300rpx;" src="/static/images/empty/order.png" mode="widthFix"></image><view class="u-fotn-30 u-tips-color">暂无排期</view></view></block></view></block></view><block wx:if="{{currentIndex!==null}}"><view class="bottom-blk bg-fff u-flex w-100 u-p-40"><block wx:if="{{!isCoach}}"><view class="u-flex-1 u-m-r-10"><u-button vue-id="{{('71dead74-9')+','+('71dead74-1')}}" color="{{$root.m1['buttonLightBgColor']}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['toBuyHuiYuanKa']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">购买会员卡</u-button></view></block><block wx:if="{{!isCoach&&currentIndex!==null}}"><view class="u-flex-1"><u-button vue-id="{{('71dead74-10')+','+('71dead74-1')}}" color="{{$root.m2['buttonLightBgColor']}}" loading="{{disabled}}" shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['appointment']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">预约课程</u-button></view></block><block wx:if="{{isCoach&&currentIndex!==null}}"><view class="u-flex-1"><u-button vue-id="{{('71dead74-11')+','+('71dead74-1')}}" color="{{$root.m3['buttonLightBgColor']}}" loading="{{disabled}}" loadingText="预约中..." shape="circle" customStyle="{{({fontWeight:'bold'})}}" data-event-opts="{{[['^click',[['appointmentHelp']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">帮学员预约</u-button></view></block></view></block></view><u-popup vue-id="{{('71dead74-12')+','+('71dead74-1')}}" show="{{showUserSelect}}" mode="center" safeAreaInsetBottom="{{false}}" round="{{20}}" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="user-select"><u-search vue-id="{{('71dead74-13')+','+('71dead74-12')}}" placeholder="学员手机号" showAction="{{true}}" value="{{phoneKey}}" data-event-opts="{{[['^search',[['getUserList']]],['^custom',[['getUserList']]],['^input',[['__set_model',['','phoneKey','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" bind:__l="__l"></u-search><u-list vue-id="{{('71dead74-14')+','+('71dead74-12')}}" height="200" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{userList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-list-item vue-id="{{('71dead74-15-'+index)+','+('71dead74-14')}}" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('71dead74-16-'+index)+','+('71dead74-15-'+index)}}" title="{{item.nickName}}" data-event-opts="{{[['^click',[['handleUserItem',[index]]]]]}}" bind:click="__e" bind:__l="__l"></u-cell></u-list-item></block></u-list></view></u-popup><u-popup vue-id="{{('71dead74-17')+','+('71dead74-1')}}" show="{{showQrcode}}" mode="center" safeAreaInsetBottom="{{false}}" round="{{20}}" data-event-opts="{{[['^close',[['onOfficialClose']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><official-qrcode vue-id="{{('71dead74-18')+','+('71dead74-17')}}" bind:__l="__l"></official-qrcode></u-popup></view></theme-wrap>