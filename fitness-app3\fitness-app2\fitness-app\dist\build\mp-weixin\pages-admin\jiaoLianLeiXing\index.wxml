<theme-wrap scoped-slots-compiler="augmented" vue-id="bd29a8b2-1" class="data-v-34fc55f6" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-34fc55f6" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('bd29a8b2-2')+','+('bd29a8b2-1')}}" title="教练分类" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-34fc55f6" bind:__l="__l"></u-navbar><view class="container u-p-t-40 u-p-b-40 data-v-34fc55f6"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker u-m-b-40 data-v-34fc55f6" bindtap="__e">{{'当前场馆: '+nowVenue+''}}</view><block wx:if="{{$root.g0>0}}"><view class="u-p-t-20 u-p-b-20 u-p-r-40 u-p-l-40 w-100 border-16 u-m-b-20 bg-fff data-v-34fc55f6"><u-cell-group vue-id="{{('bd29a8b2-3')+','+('bd29a8b2-1')}}" border="{{false}}" class="data-v-34fc55f6" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="list" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('bd29a8b2-4-'+index)+','+('bd29a8b2-3')}}" title="{{list.$orig.coachTypeName}}" value="" label=" " border="{{index+1==list.g1?false:true}}" center="{{true}}" isLink="{{true}}" url="{{'/pages-admin/jiaoLianLeiXing/details?list='+list.g2}}" data-event-opts="{{[['^click',[['setValue']]]]}}" bind:click="__e" class="data-v-34fc55f6" bind:__l="__l"></u-cell></block></u-cell-group></view></block><block wx:else><u-empty vue-id="{{('bd29a8b2-5')+','+('bd29a8b2-1')}}" marginTop="150" mode="data" class="data-v-34fc55f6" bind:__l="__l"></u-empty></block></view><u-picker vue-id="{{('bd29a8b2-6')+','+('bd29a8b2-1')}}" show="{{showVenue}}" columns="{{columns}}" keyName="shopName" data-event-opts="{{[['^confirm',[['confirmVenue']]],['^cancel',[['cancelVenue']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-34fc55f6" bind:__l="__l"></u-picker><view class="bottonBtn u-flex data-v-34fc55f6"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-34fc55f6" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增分类</view></view></view></theme-wrap>