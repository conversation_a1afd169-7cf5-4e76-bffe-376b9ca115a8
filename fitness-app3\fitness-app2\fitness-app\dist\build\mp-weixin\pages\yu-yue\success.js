"use strict";(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["pages/yu-yue/success"],{45128:function(n,e){Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;e["default"]={data:function(){return{list:[],loading:!1}},onLoad:function(){},onShow:function(){},methods:{loadData:function(){var n=this;api.getData().then((function(e){n.$nextTick((function(){n.loading=!1}))}))}}}},46047:function(n,e,t){var u=t(51372)["default"],a=t(81715)["createPage"];t(96910);c(t(923));var o=c(t(48903));function c(n){return n&&n.__esModule?n:{default:n}}u.__webpack_require_UNI_MP_PLUGIN__=t,a(o.default)},48903:function(n,e,t){var u;t.r(e),t.d(e,{__esModule:function(){return i.__esModule},default:function(){return d}});var a,o=function(){var n=this,e=n.$createElement;n._self._c},c=[],i=t(45128),l=i["default"],r=t(18535),f=(0,r["default"])(l,o,c,!1,null,null,null,!1,u,a),d=f.exports}},function(n){var e=function(e){return n(n.s=e)};n.O(0,["common/vendor"],(function(){return e(46047)}));n.O()}]);