(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-status-bar/u-status-bar"],{37723:function(){},51401:function(t,e,u){"use strict";var n;u.r(e),u.d(e,{__esModule:function(){return l.__esModule},default:function(){return b}});var s,a=function(){var t=this,e=t.$createElement,u=(t._self._c,t.__get_style([t.style]));t.$mp.data=Object.assign({},{$root:{s0:u}})},o=[],l=u(97987),r=l["default"],i=u(37723),c=u.n(i),d=(c(),u(18535)),f=(0,d["default"])(r,a,o,!1,null,"5062d177",null,!1,n,s),b=f.exports},97987:function(t,e,u){"use strict";var n=u(81715)["default"];Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var s=a(u(35944));function a(t){return t&&t.__esModule?t:{default:t}}e["default"]={name:"u-status-bar",mixins:[n.$u.mpMixin,n.$u.mixin,s.default],data:function(){return{}},computed:{style:function(){var t={};return t.height=n.$u.addUnit(n.$u.sys().statusBarHeight,"px"),t.backgroundColor=this.bgColor,n.$u.deepMerge(t,n.$u.addStyle(this.customStyle))}}}}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-status-bar/u-status-bar-create-component"],{},function(t){t("81715")["createComponent"](t(51401))}]);