(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons"],{6979:function(){},18036:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=void 0;var i=e(31488),o=function(n){var t=/^[0-9]*$/g;return"number"===typeof n||t.test(n)?n+"px":n};t["default"]={name:"UniIcons",emits:["click"],props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customPrefix:{type:String,default:""},fontFamily:{type:String,default:""}},data:function(){return{icons:i.fontData}},computed:{unicode:function(){var n=this,t=this.icons.find((function(t){return t.font_class===n.type}));return t?t.unicode:""},iconSize:function(){return o(this.size)},styleObj:function(){return""!==this.fontFamily?"color: ".concat(this.color,"; font-size: ").concat(this.iconSize,"; font-family: ").concat(this.fontFamily,";"):"color: ".concat(this.color,"; font-size: ").concat(this.iconSize,";")}},methods:{_onClick:function(){this.$emit("click")}}}},63003:function(n,t,e){"use strict";var i;e.r(t),e.d(t,{__esModule:function(){return s.__esModule},default:function(){return p}});var o,c=function(){var n=this,t=n.$createElement;n._self._c},u=[],s=e(18036),l=s["default"],r=e(6979),a=e.n(r),f=(a(),e(18535)),d=(0,f["default"])(l,c,u,!1,null,null,null,!1,i,o),p=d.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons-create-component"],{},function(n){n("81715")["createComponent"](n(63003))}]);