<theme-wrap scoped-slots-compiler="augmented" vue-id="632906d8-1" class="data-v-6409b317" bind:__l="__l" vue-slots="{{['content']}}"><view class="data-v-6409b317" slot="content" wx:if="{{$root.m0}}"><u-navbar vue-id="{{('632906d8-2')+','+('632906d8-1')}}" title="场馆列表" titleStyle="{{$root.a0}}" bgColor="{{$root.m1['navBarColor']}}" leftIconColor="{{$root.m2['navBarTextColor']}}" autoBack="{{true}}" placeholder="{{true}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^rightClick',[['e0']]]]}}" bind:rightClick="__e" class="data-v-6409b317" bind:__l="__l"></u-navbar><block wx:if="{{$root.g0>0}}"><view class="u-p-l-20 u-p-r-20 data-v-6409b317"><block wx:for="{{shopList}}" wx:for-item="list" wx:for-index="index" wx:key="index"><view class="u-p-t-20 u-p-b-20 u-border-bottom u-flex data-v-6409b317"><view class="u-flex-1 data-v-6409b317"><view class="title data-v-6409b317">{{list.shopName}}</view><view class="value u-flex u-m-t-20 data-v-6409b317"><view class="u-flex-1 data-v-6409b317">{{"地址："+list.address}}</view></view></view><view data-event-opts="{{[['tap',[['gotoDetails',['$0'],[[['shopList','',index]]]]]]]}}" class="u-m-l-20 data-v-6409b317" bindtap="__e"><u-icon vue-id="{{('632906d8-3-'+index)+','+('632906d8-1')}}" name="arrow-right" class="data-v-6409b317" bind:__l="__l"></u-icon></view></view></block><view class="whiteView data-v-6409b317"></view></view></block><block wx:else><u-empty vue-id="{{('632906d8-4')+','+('632906d8-1')}}" marginTop="150" mode="list" class="data-v-6409b317" bind:__l="__l"></u-empty></block><view class="bottonBtn u-flex data-v-6409b317"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirmBtn data-v-6409b317" style="{{'background:'+($root.m3['buttonLightBgColor'])+';'+('color:'+($root.m4['buttonTextColor'])+';')+('border-color:'+($root.m5['buttonLightBgColor'])+';')}}" bindtap="__e">新增场馆</view></view></view></theme-wrap>