{"version": 3, "file": "pages/user/xunLianJianYi.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCCsDA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,UAAA;MACA;MACAC,IAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACAC,MAAA;MACAC,gBAAA;MACAC,aAAA,GACA;QACAC,KAAA,EACA;QACAC,EAAA;MACA,GACA;QACAD,KAAA;QACAC,EAAA;MACA,GACA;QACAD,KAAA;QACAC,EAAA;MACA,GACA;QACAD,KAAA;QACAC,EAAA;MACA,GACA;QACAD,KAAA;QACAC,EAAA;MACA,EACA;MACAC,YAAA;MACAC,OAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IACA,KAAAN,gBAAA,QAAAC,aAAA,MAAAG,YAAA,EAAAF,KAAA;IACA;EACA;EACAK,aAAA,WAAAA,cAAA;IACA,SAAAX,WAAA,QAAAC,UAAA;MACA,KAAAD,WAAA;MACA,KAAAY,QAAA;IACA;MACAC,GAAA,CAAAC,SAAA;QACAC,KAAA;QACAC,IAAA;MACA;IACA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;EACAC,OAAA;IACAC,eAAA,WAAAA,gBAAAC,CAAA;MACA,KAAAZ,YAAA,GAAAY,CAAA,CAAAC,MAAA,CAAAC,KAAA;MACA,KAAAlB,gBAAA,QAAAC,aAAA,CAAAe,CAAA,CAAAC,MAAA,CAAAC,KAAA,EAAAhB,KAAA;IACA;IACAM,QAAA,WAAAA,SAAA;MAAA,IAAAW,KAAA;MACAC,GAAA,CAAAC,OAAA,GAAAC,IAAA,WAAAC,GAAA;QACAJ,KAAA,CAAAK,SAAA;UACAL,KAAA,CAAAd,OAAA;QACA;MACA;IACA;EACA;AACA;;;;;;;;;;;;;;ACvPAoB,mBAAA;AAGA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAiD,SAAAE,uBAAAX,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAa,UAAA,GAAAb,CAAA,KAAAc,OAAA,EAAAd,CAAA;AAHjD;AACAe,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC;;;;;;;;;;;;;;;;;ACL0G;AAC1H;AACA,CAAiE;AACL;;;AAG5D;AACA,CAAmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;ACtB0f,CAAC,+DAAe,ieAAG,EAAC", "sources": ["webpack:///./src/pages/user/xunLianJianYi.vue?9950", "uni-app:///src/pages/user/xunLianJianYi.vue", "uni-app:///src/main.js", "webpack:///./src/pages/user/xunLianJianYi.vue?77c2", "webpack:///./src/pages/user/xunLianJianYi.vue?d366", "webpack:///./src/pages/user/xunLianJianYi.vue?f808"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uReadMore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-read-more/u-read-more\" */ \"uview-ui/components/u-read-more/u-read-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"3861e38c-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"3861e38c-1\", \"content\")[\"buttonTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"3861e38c-1\", \"content\") : null\n  var g0 = m0 ? _vm.list.length : null\n  var g1 = m0 && !g0 ? !_vm.list.length && !_vm.loading : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <themeWrap>\n    <template #content=\"{ navBarColor, buttonTextColor }\">\n      <!-- 主页navbar -->\n      <u-navbar\n        :titleStyle=\"{ color: buttonTextColor }\"\n        :bgColor=\"navBarColor\"\n        :placeholder=\"true\"\n        title=\"训练建议\"\n        :autoBack=\"true\"\n        :border=\"false\"\n        :safeAreaInsetTop=\"true\"\n      >\n      </u-navbar>\n      <view class=\"container u-p-t-40 u-p-b-40\">\n        <picker\n          class=\"u-m-b-40\"\n          mode=\"selector\"\n          :range=\"changGuanList\"\n          range-key=\"label\"\n          :value=\"currentIndex\"\n          @change=\"changeChangGuan\"\n        >\n          <view class=\"w-100 u-flex u-row-between u-col-center\">\n            <view\n              class=\"u-font-36 font-bold u-line-1\"\n              :class=\"{ 'u-tips-color': !currentChangGuan }\"\n              >{{ currentChangGuan || \"请选择场馆\" }}</view\n            >\n            <u-icon name=\"arrow-right\" size=\"21\" color=\"#999999\" />\n          </view>\n        </picker>\n        <view class=\"u-p-t-40 u-border-top\">\n          <view>\n            <template v-if=\"list.length\">\n              <view\n                class=\"u-tips-color u-flex u-row-end font-bold u-font-24 u-m-b-30\"\n              >\n                共\n                <text style=\"color: #000\" class=\"u-m-l-10 u-m-r-10 u-font-30\">{{\n                  amount\n                }}</text>\n                记录\n              </view>\n              <view\n                class=\"u-m-b-30 item-blk u-flex u-row-satrt u-col-start\"\n                v-for=\"(item, index) in list\"\n                :key=\"index\"\n              >\n                <view class=\"u-flex-1 u-p-r-20\">\n                  <image\n                    :src=\"item.avatar\"\n                    mode=\"widthFix\"\n                    class=\"w-100 flex-0\"\n                    style=\"height: 60rpx; border-radius: 50%\"\n                  />\n                  <view class=\"u-m-t-10 u-text-center u-font-28 font-bold u-line-1\">\n                    {{ item.nickname }}\n                  </view>\n                  <view class=\"u-m-t-10 u-tips-color u-font-26\">\n                    {{ item.created_at }}\n                  </view>\n                </view>\n                <view\n                  class=\"u-p-20 border-16 u-m-t-40 bg-fff u-flex-3 u-relative\"\n                >\n                  <view\n                    class=\"w-100 flex-0 u-relative u-border-bottom u-m-b-20 u-p-b-20 u-flex u-row-between\"\n                  >\n                    <view\n                      class=\"u-font-32 font-bold overflow-hidden u-line-1 u-p-r-10\"\n                      style=\"max-width: 65%\"\n                      >{{ item.courseName }}{{ item.courseName\n                      }}{{ item.courseName }}</view\n                    >\n                    <view\n                      class=\"u-tips-color u-font-28 u-text-end overflow-hidden u-line-1\"\n                      style=\"max-width: 35%\"\n                      >{{ item.courseTypeText }}{{ item.courseTypeText\n                      }}{{ item.courseTypeText }}</view\n                    >\n                  </view>\n                  <view>\n                    <u-read-more showHeight=\"200\" :toggle=\"true\">\n                      <view class=\"u-font-28\">{{item.content}}</view>\n                    </u-read-more>\n                  </view>\n                  <view class=\"u-absolute\" style=\"left: -17rpx; top: 16rpx\">\n                    <u-icon\n                      name=\"arrow-down-fill\"\n                      size=\"18\"\n                      color=\"#fff\"\n                    ></u-icon>\n                  </view>\n                </view>\n              </view>\n            </template>\n            <template v-else-if=\"!list.length && !loading\">\n              <view\n                class=\"w-100 u-p-t-80 u-flex-col u-row-center u-col-center u-p-b-80\"\n              >\n                <image\n                  src=\"@/static/images/empty/history.png\"\n                  mode=\"widthFix\"\n                  style=\"width: 360rpx; height: 360rpx\"\n                />\n                <view class=\"u-fotn-30 u-tips-color\">暂无记录</view>\n              </view>\n            </template>\n            <view v-show=\"loading\"> </view>\n          </view>\n        </view>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      currentPage: 1,\n      totalPages: 1,\n      // list: [],\n      list: [\n        // {\n        //   avatar: require(\"@/static/images/test/jiaolian1.jpg\"),\n        //   created_at: \"2020-10-10\",\n        //   nickname: \"教练1\",\n        //   courseName: \"精品减脂课1\",\n        //   courseTypeText: \"精品团课\",\n        //   // 训练建议\n        //   content: `\n        //   1. 请在训练前做好热身准备，避免运动损伤。\n        //   2. 请在训练时注意保护自己，避免运动损伤。\n        //   3. 请在训练后做好拉伸放松，避免运动损伤。\n        //   4. 请在训练后做好拉伸放松，避免运动损伤。\n        //   5. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。         2. 请在训练时注意保护自己，避免运动损伤。\n        //   3. 请在训练后做好拉伸放松，避免运动损伤。\n        //   4. 请在训练后做好拉伸放松，避免运动损伤。\n        //   5. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   `\n        // },\n        // {\n        //   avatar: require(\"@/static/images/test/jiaolian1.jpg\"),\n        //   created_at: \"2020-10-10\",\n        //   nickname: \"教练2\",\n        //   courseName: \"精品减脂课2\",\n        //   courseTypeText: \"私教\",\n        //             content: `\n        //   1. 请在训练前做好热身准备，避免运动损伤。\n        //   2. 请在训练时注意保护自己，避免运动损伤。\n        //   3. 请在训练后做好拉伸放松，避免运动损伤。\n        //   4. 请在训练后做好拉伸放松，避免运动损伤。\n        //   5. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   `\n        // },\n        // {\n        //   avatar: require(\"@/static/images/test/jiaolian1.jpg\"),\n        //   created_at: \"2020-10-10\",\n        //   nickname: \"教练3\",\n        //   courseName: \"精品减脂课3\",\n        //   courseTypeText: \"私教\",\n        //             content: `\n        //   1. 请在训练前做好热身准备，避免运动损伤。\n        //   2. 请在训练时注意保护自己，避免运动损伤。\n        //   3. 请在训练后做好拉伸放松，避免运动损伤。\n        //   4. 请在训练后做好拉伸放松，避免运动损伤。\n        //   5. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   6. 请在训练后做好拉伸放松，避免运动损伤。\n        //   `\n        // },\n      ],\n      amount: 1000,\n      currentChangGuan: \"\",\n      changGuanList: [\n        {\n          label:\n            \"场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1场馆1\",\n          id: 1,\n        },\n        {\n          label: \"场馆2\",\n          id: 1,\n        },\n        {\n          label: \"场馆3\",\n          id: 1,\n        },\n        {\n          label: \"场馆4\",\n          id: 1,\n        },\n        {\n          label: \"场馆5\",\n          id: 1,\n        },\n      ],\n      currentIndex: 0,\n      loading: false,\n    };\n  },\n  onLoad() {\n    this.currentChangGuan = this.changGuanList[this.currentIndex].label;\n    // this.loadData()\n  },\n  onReachBottom() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n      this.loadData();\n    } else {\n      uni.showToast({\n        title: \"没有更多了\",\n        icon: \"none\",\n      });\n    }\n    // this.loadData();\n  },\n  onShow() {},\n  methods: {\n    changeChangGuan(e) {\n      this.currentIndex = e.detail.value;\n      this.currentChangGuan = this.changGuanList[e.detail.value].label;\n    },\n    loadData() {\n      api.getData().then((res) => {\n        this.$nextTick(() => {\n          this.loading = false;\n        });\n      });\n    },\n  },\n};\n</script>\n<style lang=\"scss\"></style>\n", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/xunLianJianYi.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./xunLianJianYi.vue?vue&type=template&id=335e0768&\"\nvar renderjs\nimport script from \"./xunLianJianYi.vue?vue&type=script&lang=js&\"\nexport * from \"./xunLianJianYi.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/xunLianJianYi.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xunLianJianYi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xunLianJianYi.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xunLianJianYi.vue?vue&type=template&id=335e0768&\""], "names": ["data", "currentPage", "totalPages", "list", "amount", "currentChang<PERSON>uan", "changGuanList", "label", "id", "currentIndex", "loading", "onLoad", "onReachBottom", "loadData", "uni", "showToast", "title", "icon", "onShow", "methods", "changeChangGuan", "e", "detail", "value", "_this", "api", "getData", "then", "res", "$nextTick", "require", "_vue", "_interopRequireDefault", "_xunLianJian<PERSON>i", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}