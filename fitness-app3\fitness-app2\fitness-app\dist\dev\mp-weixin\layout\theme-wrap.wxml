<view class="theme-wrap u-relative data-v-7a7df696" style="{{'--base-bg-color:'+(themeConfig.baseBgColor)+';'+('--base-color:'+(themeConfig.baseTextColor)+';')+('--button-bg-color:'+(themeConfig.buttonBgColor)+';')+('--button-text-color:'+(themeConfig.buttonTextColor)+';')+('--button-light-bg-color:'+(themeConfig.buttonLightBgColor)+';')+('--scroll-item-bg-color:'+(themeConfig.scrollItemBgColor)+';')+('padding-bottom:'+(isTab?'180rpx':'0')+';')+('--navbar-color:'+(themeConfig.navBarColor)+';')}}"><slot name="content"></slot><scoped-slots-content logo="{{themeConfig.logo}}" bgColor="{{themeConfig.baseBgColor}}" color="{{themeConfig.baseColor}}" buttonBgColor="{{themeConfig.buttonBgColor}}" buttonTextColor="{{themeConfig.buttonTextColor}}" buttonLightBgColor="{{themeConfig.buttonLightBgColor}}" navBarColor="{{themeConfig.navBarColor}}" navBarTextColor="{{themeConfig.navBarTextColor}}" couponColor="{{themeConfig.couponColor}}" class="scoped-ref" bind:__l="__l"></scoped-slots-content></view>