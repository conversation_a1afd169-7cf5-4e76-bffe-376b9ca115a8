(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-parse/u-parse"],{433:function(e,n,t){"use strict";var o=t(81715)["default"];Object.defineProperty(n,"__esModule",{value:!0}),n["default"]=void 0;var i=s(t(72185));function s(e){return e&&e.__esModule?e:{default:e}}var r=[],c=t(48100),u=function(){t.e("node-modules/uview-ui/components/u-parse/node/node").then(function(){return resolve(t(98216))}.bind(null,t))["catch"](t.oe)};n["default"]={name:"mp-html",data:function(){return{nodes:[]}},mixins:[i.default],components:{node:u},watch:{content:function(e){this.setContent(e)}},created:function(){this.plugins=[];for(var e=r.length;e--;)this.plugins.push(new r[e](this))},mounted:function(){this.content&&!this.nodes.length&&this.setContent(this.content)},beforeDestroy:function(){this._hook("onDetached"),clearInterval(this._timer)},methods:{in:function(e,n,t){e&&n&&t&&(this._in={page:e,selector:n,scrollTop:t})},navigateTo:function(e,n){var t=this;return new Promise((function(i,s){if(!t.useAnchor)return s("Anchor is disabled");n=n||parseInt(t.useAnchor)||0;var r=" ";r=">>>";var c=o.createSelectorQuery().in(t._in?t._in.page:t).select((t._in?t._in.selector:"._root")+(e?"".concat(r,"#").concat(e):"")).boundingClientRect();t._in?c.select(t._in.selector).scrollOffset().select(t._in.selector).boundingClientRect():c.selectViewport().scrollOffset(),c.exec((function(e){if(!e[0])return s("Label not found");var r=e[1].scrollTop+e[0].top-(e[2]?e[2].top:0)+n;t._in?t._in.page[t._in.scrollTop]=r:o.pageScrollTo({scrollTop:r,duration:300}),i()}))}))},getText:function(){var e="";return function n(t){for(var o=0;o<t.length;o++){var i=t[o];if("text"==i.type)e+=i.text.replace(/&amp;/g,"&");else if("br"==i.name)e+="\n";else{var s="p"==i.name||"div"==i.name||"tr"==i.name||"li"==i.name||"h"==i.name[0]&&i.name[1]>"0"&&i.name[1]<"7";s&&e&&"\n"!=e[e.length-1]&&(e+="\n"),i.children&&n(i.children),s&&"\n"!=e[e.length-1]?e+="\n":"td"!=i.name&&"th"!=i.name||(e+="\t")}}}(this.nodes),e},getRect:function(){var e=this;return new Promise((function(n,t){o.createSelectorQuery().in(e).select("#_root").boundingClientRect().exec((function(e){return e[0]?n(e[0]):t("Root label not found")}))}))},setContent:function(e,n){var t=this;n&&this.imgList||(this.imgList=[]);var o,i=new c(this).parse(e);this.$set(this,"nodes",n?(this.nodes||[]).concat(i):i),this._videos=[],this.$nextTick((function(){t._hook("onLoad"),t.$emit("load")})),clearInterval(this._timer),this._timer=setInterval((function(){t.getRect().then((function(e){e.height==o&&(t.$emit("ready",e),clearInterval(t._timer)),o=e.height})).catch((function(){}))}),350)},_hook:function(e){for(var n=r.length;n--;)this.plugins[n][e]&&this.plugins[n][e]()}}}},62121:function(){},69833:function(e,n,t){"use strict";var o;t.r(n),t.d(n,{__esModule:function(){return c.__esModule},default:function(){return d}});var i,s=function(){var e=this,n=e.$createElement;e._self._c},r=[],c=t(433),u=c["default"],l=t(62121),a=t.n(l),h=(a(),t(18535)),f=(0,h["default"])(u,s,r,!1,null,null,null,!1,o,i),d=f.exports}}]),(global["webpackChunkfitness_xcx"]=global["webpackChunkfitness_xcx"]||[]).push([["node-modules/uview-ui/components/u-parse/u-parse-create-component"],{},function(e){e("81715")["createComponent"](e(69833))}]);