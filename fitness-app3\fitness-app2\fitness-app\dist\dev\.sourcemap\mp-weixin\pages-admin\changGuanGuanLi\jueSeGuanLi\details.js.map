{"version": 3, "file": "pages-admin/changGuanGuanLi/jueSeGuanLi/details.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACvBA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uaAEN;AACP,KAAK;AACL;AACA,aAAa,uXAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACrDA,IAAAA,KAAA,GAAAC,mBAAA;AAAA,SAAAC,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAX,CAAA,GAAAS,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAP,CAAA,GAAAA,CAAA,CAAAY,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAP,CAAA,EAAAC,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAR,CAAA,YAAAQ,CAAA;AAAA,SAAAS,cAAAX,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAjB,CAAA,EAAAG,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAe,gBAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAkB,cAAA,CAAAlB,CAAA,MAAAD,CAAA,GAAAG,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,IAAAmB,KAAA,EAAAlB,CAAA,EAAAM,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAAtB,CAAA,CAAAC,CAAA,IAAAC,CAAA,EAAAF,CAAA;AAAA,SAAAmB,eAAAjB,CAAA,QAAAqB,CAAA,GAAAC,YAAA,CAAAtB,CAAA,gCAAAT,OAAA,CAAA8B,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAtB,CAAA,EAAAD,CAAA,oBAAAR,OAAA,CAAAS,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAF,CAAA,GAAAE,CAAA,CAAAP,MAAA,CAAA8B,WAAA,kBAAAzB,CAAA,QAAAuB,CAAA,GAAAvB,CAAA,CAAA0B,IAAA,CAAAxB,CAAA,EAAAD,CAAA,gCAAAR,OAAA,CAAA8B,CAAA,UAAAA,CAAA,YAAAI,SAAA,yEAAA1B,CAAA,GAAA2B,MAAA,GAAAC,MAAA,EAAA3B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACA4B,QAAA,EAAAnB,aAAA,KACA,IAAAoB,gBAAA,mBACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,MAAAC,WAAA;EACA;AACA;;;;;;;;;;;;;;;;;;ACeA,IAAAC,IAAA,GAAAC,sBAAA,CAAAlD,mBAAA;AACA,IAAAmD,UAAA,GAAAD,sBAAA,CAAAlD,mBAAA;AAAA,SAAAkD,uBAAA1C,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAA4C,UAAA,GAAA5C,CAAA,KAAAoC,OAAA,EAAApC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCACA;EACA6C,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,cAAA;MACA;MACAC,aAAA;QACAC,IAAA;QACAC,QAAA;MACA,GACA;QACAD,IAAA;QACAC,QAAA;MACA,GACA;QACAD,IAAA;QACAC,QAAA;MACA,GACA;QACAD,IAAA;QACAC,QAAA;MACA;IAEA;EACA;EACAC,MAAA,WAAAA,OAAAC,GAAA;IACAnB,OAAA,CAAAC,GAAA,CAAAkB,GAAA;IACA,KAAAX,QAAA,GAAAY,IAAA,CAAAC,KAAA,CAAAF,GAAA,CAAAG,IAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACAC,GAAA,CAAAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,YAAA,CAAAC,cAAA;QACArB,MAAA,OAAAF,QAAA,CAAAE,MAAA;QACAsB,MAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAR,GAAA,CAAAS,WAAA;QACA,IAAAD,GAAA,CAAAE,IAAA;UACAX,KAAA,CAAAY,WAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAb,GAAA,CAAAS,WAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MACAX,GAAA,CAAAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,YAAA,CAAAO,WAAA;QACA3B,MAAA,OAAAF,QAAA,CAAAE,MAAA;QACAsB,MAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAR,GAAA,CAAAS,WAAA;QACA,IAAAD,GAAA,CAAAE,IAAA,UAEA;MACA,GAAAE,KAAA,WAAAC,GAAA;QACAb,GAAA,CAAAS,WAAA;MACA;IACA;IACAK,OAAA,WAAAA,QAAA;MACAV,YAAA,CAAAW,WAAA;IACA;IACAC,OAAA,WAAAA,QAAAhF,CAAA;MACA,KAAAiF,EAAA,CAAAC,KAAA;IACA;IACAC,UAAA,WAAAA,WAAAnF,CAAA;MACA,KAAAiF,EAAA,CAAAC,KAAA;IACA;IACAE,cAAA,WAAAA,eAAA,GAEA;IACA;IACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACAtB,GAAA,CAAAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,YAAA,CAAAW,WAAA;QACAlC,IAAA,OAAAC,QAAA;QACAwB,MAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAR,GAAA,CAAAS,WAAA;QACA,IAAAD,GAAA,CAAAE,IAAA;UACAY,MAAA,CAAAL,EAAA,CAAAC,KAAA;UACAK,UAAA;YACAvB,GAAA,CAAAwB,YAAA;UACA;QACA;MACA,GAAAZ,KAAA,WAAAC,GAAA;QACAb,GAAA,CAAAS,WAAA;MACA;IACA;IACA;IACAgB,UAAA,WAAAA,WAAA;MACA,IAAAC,IAAA;MACA1B,GAAA,CAAA2B,SAAA;QACAxB,KAAA;QACAyB,OAAA;QACAC,OAAA,WAAAA,QAAArB,GAAA;UACA,IAAAA,GAAA,CAAAa,OAAA;YACAjB,YAAA,CAAAqB,UAAA;cACAK,WAAA,EAAAJ,IAAA,CAAA5C,QAAA,CAAAE,MAAA;cACAsB,MAAA;YACA,GAAAC,IAAA,WAAAC,GAAA;cACAR,GAAA,CAAAS,WAAA;cACA,IAAAD,GAAA,CAAAE,IAAA;gBACAgB,IAAA,CAAAT,EAAA,CAAAC,KAAA;gBACAK,UAAA;kBACAvB,GAAA,CAAAwB,YAAA;gBACA;cACA;YACA,GAAAZ,KAAA,WAAAC,GAAA;cACAb,GAAA,CAAAS,WAAA;cACAiB,IAAA,CAAAT,EAAA,CAAAC,KAAA;YACA;UACA,WAAAV,GAAA,CAAAuB,MAAA,GAEA;QACA;MACA;IACA;EACA;AACA;;;;;;;;;;AClMA;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;ACAmI;AACnI;AACA,CAA8D;AACL;AACzD,CAA+F;;;AAG/F;AACgI;AAChI,gBAAgB,4IAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBwe,CAAC,+DAAe,8dAAG,EAAC;;;;;;;;;;;;;;;;;ACAqd,CAAC,+DAAe,85BAAG,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAp/BvG,mBAAA;AAGA,IAAAwG,IAAA,GAAAtD,sBAAA,CAAAlD,mBAAA;AACA,IAAAyG,QAAA,GAAAvD,sBAAA,CAAAlD,mBAAA;AAAwE,SAAAkD,uBAAA1C,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAA4C,UAAA,GAAA5C,CAAA,KAAAoC,OAAA,EAAApC,CAAA;AAHxE;AACAkG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLgH;AAChI;AACA,CAA2D;AACL;AACtD,CAA4F;;;AAG5F;AACsI;AACtI,gBAAgB,4IAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBmgB,CAAC,+DAAe,2dAAG,EAAC;;;;;;;;;;;;;;;;;ACA6e,CAAC,+DAAe,25BAAG,EAAC", "sources": ["webpack:///./src/layout/theme-wrap.vue?8473", "webpack:///./src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue?69c0", "uni-app:///src/layout/theme-wrap.vue", "uni-app:///src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue", "webpack:///./src/layout/theme-wrap.vue?ddc8", "webpack:///./src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue?6893", "webpack:///./src/layout/theme-wrap.vue?e3fa", "webpack:///./src/layout/theme-wrap.vue?8af5", "webpack:///./src/layout/theme-wrap.vue?afdb", "uni-app:///src/main.js", "webpack:///./src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue?b1c9", "webpack:///./src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue?deef", "webpack:///./src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue?42da", "webpack:///./src/pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue?d1ea"], "sourcesContent": ["var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"content\", {\n      logo: _vm.themeConfig.logo,\n      bgColor: _vm.themeConfig.baseBgColor,\n      color: _vm.themeConfig.baseColor,\n      buttonBgColor: _vm.themeConfig.buttonBgColor,\n      buttonTextColor: _vm.themeConfig.buttonTextColor,\n      buttonLightBgColor: _vm.themeConfig.buttonLightBgColor,\n      navBarColor: _vm.themeConfig.navBarColor,\n      navBarTextColor: _vm.themeConfig.navBarTextColor,\n      couponColor: _vm.themeConfig.couponColor,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"3618ab06-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"3618ab06-1\", \"content\")[\"navBarTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  var m2 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  var m3 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  var m4 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  var m5 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  var m6 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  var m7 = m0 ? _vm.$getSSP(\"3618ab06-1\", \"content\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.uni.navigateBack()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n  <view\n    class=\"theme-wrap u-relative\"\n    :style=\"{\n      '--base-bg-color': themeConfig.baseBgColor,\n      '--base-color': themeConfig.baseTextColor,\n      '--button-bg-color': themeConfig.buttonBgColor,\n      '--button-text-color': themeConfig.buttonTextColor,\n      '--button-light-bg-color': themeConfig.buttonLightBgColor,\n      '--scroll-item-bg-color': themeConfig.scrollItemBgColor,\n      'padding-bottom': isTab?'180rpx':'0',\n      '--navbar-color': themeConfig.navBarColor\n    }\"\n  >\n    <slot\n      name=\"content\"\n      :logo=\"themeConfig.logo\"\n      :bgColor=\"themeConfig.baseBgColor\"\n      :color=\"themeConfig.baseColor\"\n      :buttonBgColor=\"themeConfig.buttonBgColor\"\n      :buttonTextColor=\"themeConfig.buttonTextColor\"\n      :buttonLightBgColor=\"themeConfig.buttonLightBgColor\"\n      :navBarColor=\"themeConfig.navBarColor\"\n      :navBarTextColor=\"themeConfig.navBarTextColor\"\n      :couponColor=\"themeConfig.couponColor\"\n    ></slot>\n  </view>\n</template>\n<script>\nimport { mapGetters } from \"vuex\";\nexport default {\n  computed: {\n    ...mapGetters([\"themeConfig\"]),\n  },\n  props: {\n    isTab:{\n      type: Boolean,\n      default: false\n    }\n  },\n  mounted() {\n    console.log(this.themeConfig);\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.theme-wrap {\n  min-height: 100vh;\n  width: 100vw;\n  background: var(--base-bg-color);\n}\n</style>\n", "<template>\n  <themeWrap>\n    <template #content=\"{navBarColor,navBarTextColor,buttonLightBgColor,buttonTextColor}\">\n      <view>\n        <!-- 顶部菜单栏 -->\n        <u-navbar title=\"员工权限设置\" @rightClick=\"uni.navigateBack()\" :titleStyle=\"{ color: navBarTextColor }\"\n          :bgColor=\"navBarColor\" :leftIconColor=\"navBarTextColor\" :autoBack=\"true\" :placeholder=\"true\"\n          :safeAreaInsetTop=\"true\">\n        </u-navbar>\n        <!-- 人员列表 -->\n        <view class=\"u-m-t-30 u-m-l-30 u-font-32 bold\">权限人员</view>\n        <view class=\"container u-p-t-40 u-p-b-40\">\n          <view class=\"formView\">\n            <view class=\"formList\">\n              <view>角色名称:</view>\n              <u--input :border=\"false\" v-model=\"formList.roleName\"></u--input>\n            </view>\n            <!-- <view class=\"user_avatar\" v-for=\"n in 7\" :key=\"n\">\n              <u-avatar size=\"80rpx\" :src=\"src\" shape=\"circle\"></u-avatar>\n              <view class=\"w-100 u-font-24\">晴天雨绵</view>\n            </view>\n            <view class=\"user_avatar\">\n              <u-icon size=\"80rpx\" name=\"plus-circle\" @click=\"addMore\"></u-icon>\n              <view class=\"w-100\"></view>\n            </view>\n            <view class=\"user_avatar\">\n              <u-icon size=\"80rpx\" name=\"minus-circle\" @click=\"removeMore\"></u-icon>\n              <view class=\"w-100\"></view>\n            </view> -->\n          </view>\n        </view>\n        <!-- 权限列表 -->\n        <view class=\"u-m-t-30 u-m-l-30 u-font-32 bold\">会员管理权限</view>\n        <view class=\"container u-p-t-40 u-p-b-40\">\n          <view class=\"contView u-p-r-30 u-p-l-30 w-100 border-16 bg-fff\">\n\n            <u-checkbox-group v-model=\"checkboxValue7\" @change=\"checkboxChange\" :borderBottom=\"true\" placement=\"column\"\n              iconPlacement=\"right\">\n              <u-checkbox :customStyle=\"{marginBottom: '16px'}\" v-for=\"(item, index) in checkboxList7\" :key=\"index\"\n                :label=\"item.name\" :name=\"item.name\">\n              </u-checkbox>\n            </u-checkbox-group>\n          </view>\n        </view>\n        <!-- 底部按钮 -->\n        <view class=\"bottonBtn u-flex\">\n          <view class=\"moreBtn\" @click=\"deleteRole()\"\n            :style=\"{color: buttonLightBgColor, 'border-color': buttonLightBgColor}\">删除角色</view>\n          <view class=\"addHuiYuan\" @click=\"confirm()\"\n            :style=\"{'background': buttonLightBgColor, color: buttonTextColor, 'border-color': buttonLightBgColor}\">\n            修改角色</view>\n        </view>\n      </view>\n    </template>\n  </themeWrap>\n</template>\n\n<script>\n  import api from \"@/common/api\";\n  import themeWrap from '../../../layout/theme-wrap.vue';\n  export default {\n    data() {\n      return {\n        formList: {\n          companyId: 1,\n          roleId: '',\n          roleKey: '',\n          roleName: '',\n          roleSort: '',\n        },\n        checkboxValue7: false,\n        //横向两端排列形式\n        checkboxList7: [{\n            name: '汽车',\n            disabled: false\n          },\n          {\n            name: '蒸汽机',\n            disabled: false\n          },\n          {\n            name: '猪肉',\n            disabled: false\n          },\n          {\n            name: '抄手',\n            disabled: false\n          }\n        ],\n      }\n    },\n    onLoad(obj) {\n      console.log(obj);\n      this.formList = JSON.parse(obj.list);\n      this.getRoleDetail();\n    },\n    methods: {\n      getRoleDetail() {\n        uni.showLoading({\n          mask: true,\n          title: '数据加载中，请稍后……'\n        })\n        api.getRoleDetails({\n          roleId: this.formList.roleId,\n          method: 'GET'\n        }).then((res) => {\n          uni.hideLoading();\n          if (res.code == 200) {\n            this.getRoleTree();\n          }\n        }).catch((err) => {\n          uni.hideLoading();\n        })\n      },\n      getRoleTree() {\n        uni.showLoading({\n          mask: true,\n          title: '数据加载中，请稍后……'\n        })\n        api.getRoleTree({\n          roleId: this.formList.roleId,\n          method: 'GET'\n        }).then((res) => {\n          uni.hideLoading();\n          if (res.code == 200) {\n\n          }\n        }).catch((err) => {\n          uni.hideLoading();\n        })\n      },\n      putRole() {\n        api.putShopRole\n      },\n      addMore(e) {\n        this.$u.toast('增加')\n      },\n      removeMore(e) {\n        this.$u.toast('删除')\n      },\n      checkboxChange() {\n\n      },\n      // 修改角色\n      confirm() {\n        uni.showLoading({\n          mask: true,\n          title: '修改角色中，请稍后……'\n        })\n        api.putShopRole({\n          data: this.formList,\n          method: 'PUT'\n        }).then((res) => {\n          uni.hideLoading();\n          if (res.code == 200) {\n            this.$u.toast(\"新增成功！\")\n            setTimeout(() => {\n              uni.navigateBack()\n            }, 2000)\n          }\n        }).catch((err) => {\n          uni.hideLoading();\n        })\n      },\n      // 删除角色\n      deleteRole() {\n        let that = this\n        uni.showModal({\n          title: '提示：',\n          content: '请确认是否要删除?',\n          success: function(res) {\n            if (res.confirm) {\n              api.deleteRole({\n                shopRoleIds: that.formList.roleId,\n                method: 'DELETE'\n              }).then((res) => {\n                uni.hideLoading();\n                if (res.code == 200) {\n                  that.$u.toast(\"删除成功！\")\n                  setTimeout(() => {\n                    uni.navigateBack()\n                  }, 2000)\n                }\n              }).catch((err) => {\n                uni.hideLoading();\n                that.$u.toast(\"删除失败！请稍后再试\");\n              })\n            } else if (res.cancel) {\n\n            }\n          }\n        });\n      }\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .contView {\n    display: flex;\n    flex-wrap: wrap;\n\n    .user_avatar {\n      width: 20%;\n      height: 120rpx;\n      margin-top: 30rpx;\n      text-align: center;\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      justify-content: center;\n\n      .w-100 {\n        overflow: hidden;\n      }\n    }\n\n    ::v-deep .u-checkbox-group--column {\n      width: 100%;\n    }\n\n    ::v-deep .u-radio {\n      width: 100%;\n    }\n  }\n\n  .formView {\n\n    .formList {\n      background-color: #fafafa;\n      display: flex;\n      align-items: center;\n      flex-direction: row;\n      box-sizing: border-box;\n      padding: 10rpx 30rpx;\n      font-size: 30rpx;\n      color: #303133;\n      align-items: center;\n      border: 2rpx solid #d6d7d9;\n      margin-bottom: 20rpx;\n    }\n  }\n\n  .bottonBtn {\n    height: 160rpx;\n    width: 750rpx;\n    position: fixed;\n    bottom: 0;\n    border-top: 1px solid black;\n\n    .moreBtn,\n    .addHuiYuan {\n      width: 300rpx;\n      height: 80rpx;\n      margin: 0 auto;\n      text-align: center;\n      line-height: 80rpx;\n      border: 1px solid;\n      border-radius: 40rpx;\n    }\n  }\n</style>\n", "// extracted by mini-css-extract-plugin", "// extracted by mini-css-extract-plugin", "import { render, staticRenderFns, recyclableRender, components } from \"./theme-wrap.vue?vue&type=template&id=7a7df696&scoped=true&\"\nvar renderjs\nimport script from \"./theme-wrap.vue?vue&type=script&lang=js&\"\nexport * from \"./theme-wrap.vue?vue&type=script&lang=js&\"\nimport style0 from \"./theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a7df696\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"layout/theme-wrap.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./theme-wrap.vue?vue&type=style&index=0&id=7a7df696&lang=scss&scoped=true&\"", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./details.vue?vue&type=template&id=545368ae&scoped=true&\"\nvar renderjs\nimport script from \"./details.vue?vue&type=script&lang=js&\"\nexport * from \"./details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./details.vue?vue&type=style&index=0&id=545368ae&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"545368ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages-admin/changGuanGuanLi/jueSeGuanLi/details.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./details.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./details.vue?vue&type=style&index=0&id=545368ae&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./details.vue?vue&type=style&index=0&id=545368ae&scoped=true&lang=scss&\"", "export * from \"-!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./details.vue?vue&type=template&id=545368ae&scoped=true&\""], "names": ["_vuex", "require", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "computed", "mapGetters", "props", "isTab", "type", "Boolean", "default", "mounted", "console", "log", "themeConfig", "_api", "_interopRequireDefault", "_themeWrap", "__esModule", "data", "formList", "companyId", "roleId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort", "checkboxValue7", "checkboxList7", "name", "disabled", "onLoad", "obj", "JSON", "parse", "list", "getRoleDetail", "methods", "_this", "uni", "showLoading", "mask", "title", "api", "getRoleDetails", "method", "then", "res", "hideLoading", "code", "getRoleTree", "catch", "err", "putRole", "putShopRole", "addMore", "$u", "toast", "removeMore", "checkboxChange", "confirm", "_this2", "setTimeout", "navigateBack", "deleteRole", "that", "showModal", "content", "success", "shopRoleIds", "cancel", "_vue", "_details", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}