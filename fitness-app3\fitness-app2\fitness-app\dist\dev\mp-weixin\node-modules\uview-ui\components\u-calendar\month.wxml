<view data-ref="u-calendar-month-wrapper" class="u-calendar-month-wrapper data-v-b6727082 vue-ref"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['data-v-b6727082','vue-ref-in-for','u-calendar-month-'+index]}}" id="{{'month-'+index}}" data-ref="{{'u-calendar-month-'+index}}"><block wx:if="{{index!==0}}"><text class="u-calendar-month__title data-v-b6727082">{{item.$orig.year+"年"+item.$orig.month+"月"}}</text></block><view class="u-calendar-month__days data-v-b6727082"><block wx:if="{{showMark}}"><view class="u-calendar-month__days__month-mark-wrapper data-v-b6727082"><text class="u-calendar-month__days__month-mark-wrapper__text data-v-b6727082">{{item.$orig.month}}</text></view></block><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['clickHandler',[index,index1,'$0'],[[['months','',index],['date','',index1]]]]]]]}}" class="{{['u-calendar-month__days__day','data-v-b6727082',item1.$orig.selected&&'u-calendar-month__days__day__select--selected']}}" style="{{item1.s0}}" bindtap="__e"><view class="u-calendar-month__days__day__select data-v-b6727082" style="{{item1.s1}}"><text class="{{['u-calendar-month__days__day__select__info','data-v-b6727082',item1.$orig.disabled&&'u-calendar-month__days__day__select__info--disabled']}}" style="{{item1.s2}}">{{item1.$orig.day}}</text><block wx:if="{{item1.m0}}"><text class="{{['u-calendar-month__days__day__select__buttom-info','data-v-b6727082',item1.$orig.disabled&&'u-calendar-month__days__day__select__buttom-info--disabled']}}" style="{{item1.s3}}">{{item1.m1}}</text></block><block wx:if="{{item1.$orig.dot}}"><text class="u-calendar-month__days__day__select__dot data-v-b6727082"></text></block></view></view></block></view></view></block></view>