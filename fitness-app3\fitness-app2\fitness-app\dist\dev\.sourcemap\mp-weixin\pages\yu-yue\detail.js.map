{"version": 3, "file": "pages/yu-yue/detail.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uZAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,+VAEN;AACP,KAAK;AACL;AACA,aAAa,uWAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,aAAa,+XAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACNA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AAAA,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAGA;EACAG,UAAA;IACAC,QAAA,EAAAA,QAAA;IACAC,cAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MACAC,QAAA;MACAC,YAAA;MACA;MACAC,OAAA;QACAC,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,GACA;QACAF,IAAA;QACAC,EAAA;QACAC,MAAA;MACA,EACA;MACAC,SAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,MAAA;QACAC,UAAA;MACA;MACAC,QAAA;MAAA;MACAD,UAAA;MAAA;MACAE,QAAA;MAAA;MACAC,UAAA;MAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;;MAEAC,UAAA;MAAA;MACAC,OAAA;MACAC,QAAA;MAAA;MACAC,QAAA;MACAC,cAAA;MACAC,MAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IAAA,IAAAC,KAAA;IACA,KAAAT,MAAA,GAAAU,GAAA,CAAAC,cAAA;IACA,KAAAV,QAAA,GAAAS,GAAA,CAAAC,cAAA;IACA,IAAAC,KAAA,GAAAF,GAAA,CAAAC,cAAA;IACA,IAAAC,KAAA,CAAAC,QAAA;MACA,KAAAV,OAAA;IACA;MACA,KAAAA,OAAA;IACA;IACA,IAAAW,YAAA,QAAAC,qBAAA;IAEAD,YAAA,CAAAE,EAAA,2BAAAlC,IAAA;MACAmC,OAAA,CAAAC,GAAA,UAAApC,IAAA;MACA2B,KAAA,CAAAjB,UAAA,GAAAV,IAAA,CAAAqC,IAAA;MACAV,KAAA,CAAAZ,QAAA,GAAAf,IAAA,CAAAe,QAAA;MACAY,KAAA,CAAAb,UAAA,GAAAd,IAAA,CAAAc,UAAA;MACAa,KAAA,CAAAX,QAAA,GAAAhB,IAAA,CAAAsC,OAAA;MACAX,KAAA,CAAAV,UAAA,GAAAjB,IAAA,CAAAuC,SAAA;MACAZ,KAAA,CAAAa,QAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACAC,YAAA,CAAAD,WAAA;QACAE,KAAA,OAAAtB;MACA,GAAAuB,IAAA,WAAAC,GAAA;QACAZ,OAAA,CAAAC,GAAA,CAAAW,GAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAd,OAAA,CAAAC,GAAA,CAAAa,GAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACAxB,GAAA,CAAAyB,SAAA;QACAC,KAAA;QACAC,OAAA,uBAAAC,MAAA,MAAAlC,QAAA,CAAA6B,KAAA,EAAAM,QAAA;QACAC,OAAA,WAAAA,QAAAX,GAAA;UACA,IAAAA,GAAA,CAAAY,OAAA;YACAP,MAAA,CAAAQ,WAAA,CAAAR,MAAA,CAAA9B,QAAA,CAAA6B,KAAA,EAAAnC,QAAA;UACA;QACA;MACA;IACA;IACA6C,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,GAAAnC,GAAA,CAAAC,cAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,UAAAd,QAAA;QACA,KAAAiD,EAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAlD,QAAA;QACA,KAAAiD,EAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAvD,UAAA;QACA,KAAAsD,EAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAC,QAAA;MACA,IAAA7B,IAAA,QAAA3B,UAAA,cAAAL,OAAA,MAAAD,YAAA,EAAAE,IAAA;MACAsC,YAAA,CAAAuB,kBAAA;QACAnE,IAAA;UACAoE,WAAA,EAAA/B,IAAA;UACAtB,QAAA,OAAAA,QAAA;UACAD,UAAA,OAAAA,UAAA;UAAA;UACAwB,OAAA,OAAAtB,QAAA;UAAA;UACAuB,SAAA,OAAAtB,UAAA;UAAA;UACAC,MAAA,OAAAA,MAAA;UAAA;UACAC,QAAA,OAAAA,QAAA;QACA;QACAkD,MAAA;MACA,GAAAvB,IAAA,WAAAC,GAAA;QACAe,MAAA,CAAAI,QAAA;QACA,IAAAnB,GAAA,CAAAuB,IAAA;UACAR,MAAA,CAAAE,EAAA,CAAAC,KAAA;UACAM,UAAA;YACA3C,GAAA,CAAA4C,YAAA;UACA;QACA,QAEA;QAAA;MACA,GAAAxB,KAAA,WAAAC,GAAA;QACAa,MAAA,CAAAI,QAAA;MACA;IACA;IACA;IACAO,eAAA,WAAAA,gBAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,UAAA1D,QAAA;QACA,KAAAiD,EAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAlD,QAAA;QACA,KAAAiD,EAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAvD,UAAA;QACA,KAAAsD,EAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAzC,cAAA;IACA;IACA;IACAoC,WAAA,WAAAA,YAAAnC,MAAA;MAAA,IAAAiD,MAAA;MACA,KAAAR,QAAA;MACA,IAAA7B,IAAA,QAAA3B,UAAA,cAAAL,OAAA,MAAAD,YAAA,EAAAE,IAAA;MACAsC,YAAA,CAAAgB,WAAA;QACA5D,IAAA;UACAoE,WAAA,EAAA/B,IAAA;UACAtB,QAAA,OAAAA,QAAA;UACAD,UAAA,OAAAA,UAAA;UAAA;UACAwB,OAAA,OAAAtB,QAAA;UAAA;UACAuB,SAAA,OAAAtB,UAAA;UAAA;UACAC,MAAA,OAAAA,MAAA;UAAA;UACAC,QAAA,OAAAA,QAAA;UAAA;UACAH,QAAA,EAAAS,MAAA;QACA;QACA4C,MAAA;MACA,GAAAvB,IAAA,WAAAC,GAAA;QACA2B,MAAA,CAAAR,QAAA;QACA,IAAAnB,GAAA,CAAAuB,IAAA;UACAI,MAAA,CAAAV,EAAA,CAAAC,KAAA;UACAM,UAAA;YACA3C,GAAA,CAAA4C,YAAA;UACA;QACA,QAEA;QAAA;MACA,GAAAxB,KAAA,WAAAC,GAAA;QACAyB,MAAA,CAAAR,QAAA;MACA;IACA;IACAS,YAAA,WAAAA,aAAAC,IAAA;MACA,KAAAlE,UAAA,GAAAkE,IAAA;MACA,KAAAjE,WAAA;MACA,KAAAC,UAAA;MACAuB,OAAA,CAAAC,GAAA,MAAA1B,UAAA;MACA,KAAA8B,QAAA;IACA;IACAqC,kBAAA,WAAAA,mBAAA1B,KAAA,EAAA3C,MAAA;MACA,IAAAA,MAAA;QACA,KAAAJ,YAAA,GAAA+C,KAAA;MACA;IACA;IACA2B,cAAA,WAAAA,eAAA;MACAlD,GAAA,CAAAmD,UAAA;QACAC,GAAA,sCAAA9D;MACA;IACA;IACAsB,QAAA,WAAAA,SAAA;MAAA,IAAAyC,MAAA;MACArD,GAAA,CAAAsD,WAAA;QACAC,IAAA;QACA7B,KAAA;MACA;MACAV,YAAA,CAAAwC,iBAAA;QACArE,QAAA,OAAAA,QAAA;QACAsD,MAAA;MACA,GAAAvB,IAAA,WAAAC,GAAA;QACAnB,GAAA,CAAAyD,WAAA;QACA,IAAAtC,GAAA,CAAAuB,IAAA;UACAW,MAAA,CAAApE,MAAA,GAAAkC,GAAA,CAAA/C,IAAA;UACA;UACA4C,YAAA,CAAA0C,eAAA;YACAtF,IAAA;cACAgB,QAAA,EAAAiE,MAAA,CAAAjE,QAAA;cACAE,MAAA,EAAA+D,MAAA,CAAA/D;YACA;UACA,GAAA4B,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAuB,IAAA;cACAW,MAAA,CAAAM,cAAA,CAAAxC,GAAA,CAAA/C,IAAA;YACA,QAEA;YAAA;UACA;QACA;MACA,GAAAgD,KAAA,WAAAC,GAAA;QACArB,GAAA,CAAAyD,WAAA;MACA;IACA;IACAE,cAAA,WAAAA,eAAAtF,IAAA;MACA;MACA,IAAAuF,aAAA,GAAAvF,IAAA,CAAAuF,aAAA;MACA,IAAAC,WAAA,GAAAxF,IAAA,CAAAwF,WAAA;MACA,IAAAC,QAAA;MACA,IAAAC,MAAA;MACA,IAAAC,WAAA,OAAAC,IAAA;MACA,IAAAC,KAAA,GAAAF,WAAA,CAAAG,QAAA;MACA,IAAAC,OAAA,GAAAJ,WAAA,CAAAK,UAAA;MACA,IAAAC,GAAA,GAAAJ,KAAA;MACA3D,OAAA,CAAAC,GAAA,CAAAoD,aAAA;MACArD,OAAA,CAAAC,GAAA,CAAAqD,WAAA;MACA,SAAAU,CAAA,MAAAA,CAAA,QAAA9F,OAAA,CAAA+F,MAAA,EAAAD,CAAA;QACA;QACA,IAAAlG,IAAA,CAAAoG,OAAA;UACA,KAAAhG,OAAA,CAAA8F,CAAA,EAAA3F,MAAA;QACA;UACA,IAAAgF,aAAA,QAAAnF,OAAA,CAAA8F,CAAA,EAAA7F,IAAA;YACA6B,OAAA,CAAAC,GAAA;YACA,KAAA/B,OAAA,CAAA8F,CAAA,EAAA3F,MAAA;UACA,WAAAiF,WAAA,QAAApF,OAAA,CAAA8F,CAAA,EAAA7F,IAAA;YACA,KAAAD,OAAA,CAAA8F,CAAA,EAAA3F,MAAA;UACA;QACA;MACA;IACA;IACA;IACA8F,cAAA,WAAAA,eAAA;MACA,KAAAlF,UAAA;IACA;IACA;IACAmF,eAAA,WAAAA,gBAAA;MACA,KAAAnF,UAAA;MACA;MACAwB,YAAA,CACA4D,OAAA;QACAxG,IAAA;UACAyG,SAAA;QACA;QACApC,MAAA;MACA,GACAvB,IAAA,WAAA4D,GAAA;QACA,IAAAA,GAAA,CAAApC,IAAA;UACA1C,GAAA,CAAA+E,cAAA,eAAAD,GAAA,CAAAE,MAAA;QACA;MACA;IACA;EACA;AACA;;;;;;;;;;AChnBA;;;;;;;;;;;;;;;ACAAnH,mBAAA;AAGA,IAAAoH,IAAA,GAAArH,sBAAA,CAAAC,mBAAA;AACA,IAAAqH,OAAA,GAAAtH,sBAAA,CAAAC,mBAAA;AAA4C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH5C;AACAqH,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC;;;;;;;;;;;;;;;;;;;ACLmG;AACnH;AACA,CAA0D;AACL;AACrD,CAAmE;;;AAGnE;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACA,+DAAe;;;;;;;;;;;;;;;;ACvBmf,CAAC,+DAAe,0dAAG,EAAC;;;;;;;;;;;;;;;;;ACAyc,CAAC,+DAAe,k4BAAG,EAAC", "sources": ["webpack:///./src/pages/yu-yue/detail.vue?9fe6", "uni-app:///src/pages/yu-yue/detail.vue", "webpack:///./src/pages/yu-yue/detail.vue?cd4f", "uni-app:///src/main.js", "webpack:///./src/pages/yu-yue/detail.vue?2ffd", "webpack:///./src/pages/yu-yue/detail.vue?9ab4", "webpack:///./src/pages/yu-yue/detail.vue?09f5", "webpack:///./src/pages/yu-yue/detail.vue?549b"], "sourcesContent": ["var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uList: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-list/u-list\" */ \"uview-ui/components/u-list/u-list.vue\"\n      )\n    },\n    uListItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-list-item/u-list-item\" */ \"uview-ui/components/u-list-item/u-list-item.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-cell/u-cell\" */ \"uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"563484e7-1\")\n  var a0 = m0\n    ? {\n        color: _vm.$getSSP(\"563484e7-1\", \"content\")[\"buttonTextColor\"],\n      }\n    : null\n  var m1 = m0 ? _vm.$getSSP(\"563484e7-1\", \"content\") : null\n  var g0 = m0 ? _vm.courses.length : null\n  var m2 =\n    m0 && _vm.currentIndex !== null && !_vm.isCoach\n      ? _vm.$getSSP(\"563484e7-1\", \"content\")\n      : null\n  var m3 =\n    m0 && _vm.currentIndex !== null && !_vm.isCoach && _vm.currentIndex !== null\n      ? _vm.$getSSP(\"563484e7-1\", \"content\")\n      : null\n  var m4 =\n    m0 && _vm.currentIndex !== null && _vm.isCoach && _vm.currentIndex !== null\n      ? _vm.$getSSP(\"563484e7-1\", \"content\")\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showUserSelect = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\n\t<themeWrap>\n\t\t<template #content=\"{ navBarColor, buttonTextColor, buttonLightBgColor }\">\n\t\t\t<!-- 主页navbar -->\n\t\t\t<u-navbar :titleStyle=\"{ color: buttonTextColor }\" :bgColor=\"navBarColor\" :placeholder=\"true\"\n\t\t\t\t:title=\"nickname\" :autoBack=\"true\" :border=\"false\" :safeAreaInsetTop=\"true\">\n\t\t\t</u-navbar>\n\t\t\t<view class=\"container u-p-t-40 bottom-placeholder\">\n\t\t\t\t<view class=\"u-flex u-row-between u-col-center\">\n\t\t\t\t\t<view class=\"u-font-40 font-bold\">{{ detail.courseName }}</view>\n\t\t\t\t\t<!-- <view class=\"u-flex ltc u-font-32\">\n            ￥<text\n              style=\"font-size: 44rpx; margin: 0 8rpx\"\n              class=\"font-bold\"\n              >{{ detail.coursePrice }}</text\n            >元\n          </view> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"w-100 bg-fff u-m-t-40 border-16 overflow-hidden\">\n\t\t\t\t\t<calendar @changeActive=\"changeActive\"></calendar>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-flex u-tips-color u-font-26 u-m-l-10 u-m-t-20\" @click=\"handleOfficial\">\n\t\t\t\t\t<!-- <u-icon name=\"error-circle\" :color=\"buttonLightBgColor\"></u-icon>\n\t\t\t\t\t<text class=\"u-m-l-10 ltc\">\n\t\t\t\t\t\t关注公众号，教练确认后会发送公众号消息通知会员\n\t\t\t\t\t</text> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"w-100 bg-fff border-16 u-p-40 u-m-t-20 overflow-hidden\">\n\t\t\t\t\t<view class=\"u-flex u-row-between u-col-center u-p-b-20\">\n\t\t\t\t\t\t<view class=\"u-font-32 font-bold\"> 选择排期 </view>\n\t\t\t\t\t\t<view class=\"u-font-28 u-tips-color\">\n\t\t\t\t\t\t\t课程时长：{{ detail.courseDuration }}分钟\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-border-top u-p-t-20 u-p-b-20 u-relative\" v-if=\"courses.length\">\n\t\t\t\t\t\t<view class=\"u-absolute u-flex u-row-center u-col-center w-100 h-100\" style=\"top: 0; left: 0\"\n\t\t\t\t\t\t\tv-show=\"loading\">\n\t\t\t\t\t\t\t<u-loading-icon mode=\"circle\" :loading=\"true\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"courses-wrap\">\n\t\t\t\t\t\t\t<view class=\"course-blk u-p-10\" v-for=\"(i, index) in courses\" :key=\"index\">\n\t\t\t\t\t\t\t\t<view @click=\"changeCurrentIndex(index, i.status)\" :class=\"{\n                    disabled: i.status !== 1,\n                    active: index == currentIndex,\n                    expired: i.status === 2,\n                    full: i.status === 3,\n                    reset: i.status === 4\n                  }\" class=\"w-100 u-p-t-20 u-relative u-p-b-20 u-text-center course-item border-16\">\n\t\t\t\t\t\t\t\t\t{{ i.text }}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"w-100 u-p-t-80 u-border-top u-flex-col u-row-center u-col-center u-p-b-80\">\n\t\t\t\t\t\t<image src=\"@/static/images/empty/order.png\" mode=\"widthFix\"\n\t\t\t\t\t\t\tstyle=\"width: 300rpx; height: 300rpx\" />\n\t\t\t\t\t\t<view class=\"u-fotn-30 u-tips-color\">暂无排期</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bottom-blk bg-fff u-flex w-100 u-p-40\" v-if=\"currentIndex!==null\">\n\t\t\t\t\t<view class=\"u-flex-1 u-m-r-10\" v-if=\"!isCoach\">\n\t\t\t\t\t\t<u-button :color=\"buttonLightBgColor\" @click=\"toBuyHuiYuanKa\" shape=\"circle\"\n\t\t\t\t\t\t\t:customStyle=\"{ fontWeight: 'bold' }\">\n\t\t\t\t\t\t\t购买会员卡\n\t\t\t\t\t\t</u-button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-flex-1\" v-if=\"!isCoach&&currentIndex!==null\">\n\t\t\t\t\t\t<u-button :color=\"buttonLightBgColor\" :loading=\"disabled\" loadingText=\"预约中...\" shape=\"circle\"\n\t\t\t\t\t\t\t@click=\"appointment\" :customStyle=\"{ fontWeight: 'bold' }\">\n\t\t\t\t\t\t\t预约课程\n\t\t\t\t\t\t</u-button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-flex-1\" v-if=\"isCoach&&currentIndex!==null\">\n\t\t\t\t\t\t<u-button :color=\"buttonLightBgColor\" :loading=\"disabled\" loadingText=\"预约中...\" shape=\"circle\"\n\t\t\t\t\t\t\t@click=\"appointmentHelp\" :customStyle=\"{ fontWeight: 'bold' }\">\n\t\t\t\t\t\t\t帮学员预约\n\t\t\t\t\t\t</u-button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<u-popup :show=\"showUserSelect\" mode=\"center\" :safeAreaInsetBottom=\"false\" :round=\"20\" @close=\"showUserSelect=false\">\n\t\t\t\t<view class=\"user-select\">\n\t\t\t\t\t<u-search placeholder=\"学员手机号\" v-model=\"phoneKey\" :showAction=\"true\" @search=\"getUserList\"\n\t\t\t\t\t\t@custom=\"getUserList\"></u-search>\n\t\t\t\t\t<u-list height=\"200\">\n\t\t\t\t\t\t<u-list-item v-for=\"(item, index) in userList\" :key=\"index\">\n\t\t\t\t\t\t\t<u-cell :title=\"item.nickName\" @click=\"handleUserItem(index)\">\n\t\t\t\t\t\t\t</u-cell>\n\t\t\t\t\t\t</u-list-item>\n\t\t\t\t\t</u-list>\n\t\t\t\t</view>\n\t\t\t</u-popup>\n\t\t\t<!-- 公众号二维码 -->\n\t\t\t<u-popup :show=\"showQrcode\" mode=\"center\" :safeAreaInsetBottom=\"false\" :round=\"20\" @close=\"onOfficialClose\">\n\t\t\t\t<officialQrcode></officialQrcode>\n\t\t\t</u-popup>\n\t\t</template>\n\t</themeWrap>\n</template>\n\n<script>\n\timport api from \"@/common/api\";\n\timport calendar from \"@/components/calendar\";\n\timport officialQrcode from \"@/components/official-qrcode\";\n\texport default {\n\t\tcomponents: {\n\t\t\tcalendar,\n\t\t\tofficialQrcode\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlist: [],\n\t\t\t\tloading: false,\n\t\t\t\tnickname: \"\",\n\t\t\t\tcurrentIndex: null,\n\t\t\t\t// 00:00 ~ 23:30,\n\t\t\t\tcourses: [{\n\t\t\t\t\t\ttext: \"00:00\",\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tstatus: 2,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"00:30\",\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\tstatus: 3,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"01:00\",\n\t\t\t\t\t\tid: 3,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"01:30\",\n\t\t\t\t\t\tid: 4,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"02:00\",\n\t\t\t\t\t\tid: 5,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"02:30\",\n\t\t\t\t\t\tid: 6,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"03:00\",\n\t\t\t\t\t\tid: 7,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"03:30\",\n\t\t\t\t\t\tid: 8,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"04:00\",\n\t\t\t\t\t\tid: 9,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"04:30\",\n\t\t\t\t\t\tid: 10,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"05:00\",\n\t\t\t\t\t\tid: 11,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"05:30\",\n\t\t\t\t\t\tid: 12,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"06:00\",\n\t\t\t\t\t\tid: 13,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"06:30\",\n\t\t\t\t\t\tid: 14,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"07:00\",\n\t\t\t\t\t\tid: 15,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"07:30\",\n\t\t\t\t\t\tid: 16,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"08:00\",\n\t\t\t\t\t\tid: 17,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"08:30\",\n\t\t\t\t\t\tid: 18,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"09:00\",\n\t\t\t\t\t\tid: 19,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"09:30\",\n\t\t\t\t\t\tid: 20,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"10:00\",\n\t\t\t\t\t\tid: 21,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"10:30\",\n\t\t\t\t\t\tid: 22,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"11:00\",\n\t\t\t\t\t\tid: 23,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"11:30\",\n\t\t\t\t\t\tid: 24,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"12:00\",\n\t\t\t\t\t\tid: 25,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"12:30\",\n\t\t\t\t\t\tid: 26,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"13:00\",\n\t\t\t\t\t\tid: 27,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"13:30\",\n\t\t\t\t\t\tid: 28,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"14:00\",\n\t\t\t\t\t\tid: 29,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"14:30\",\n\t\t\t\t\t\tid: 30,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"15:00\",\n\t\t\t\t\t\tid: 31,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"15:30\",\n\t\t\t\t\t\tid: 32,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"16:00\",\n\t\t\t\t\t\tid: 33,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"16:30\",\n\t\t\t\t\t\tid: 34,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"17:00\",\n\t\t\t\t\t\tid: 35,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"17:30\",\n\t\t\t\t\t\tid: 36,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"18:00\",\n\t\t\t\t\t\tid: 37,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"18:30\",\n\t\t\t\t\t\tid: 38,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"19:00\",\n\t\t\t\t\t\tid: 39,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"19:30\",\n\t\t\t\t\t\tid: 40,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"20:00\",\n\t\t\t\t\t\tid: 41,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"20:30\",\n\t\t\t\t\t\tid: 42,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"21:00\",\n\t\t\t\t\t\tid: 43,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"21:30\",\n\t\t\t\t\t\tid: 44,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"22:00\",\n\t\t\t\t\t\tid: 45,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"22:30\",\n\t\t\t\t\t\tid: 46,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"23:00\",\n\t\t\t\t\t\tid: 47,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttext: \"23:30\",\n\t\t\t\t\t\tid: 48,\n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tcourse_id: \"\",\n\t\t\t\tactiveDate: \"\",\n\t\t\t\tcurrentPage: \"\",\n\t\t\t\ttotalPages: \"\",\n\t\t\t\tdetail: {\n\t\t\t\t\tcourseName: '',\n\t\t\t\t},\n\t\t\t\tcourseId: \"\", // 课程id\n\t\t\t\tcourseName: \"\", // 课程名\n\t\t\t\tmemberId: \"\", // 教练id\n\t\t\t\tmemberName: \"\", // 教练名\n\t\t\t\tshopId: \"\", // 场馆id\n\t\t\t\tshopName: \"\", // 场馆名\n\n\t\t\t\tshowQrcode: false, //显示公众号二维码\n\t\t\t\tisCoach: false,\n\t\t\t\tuserList: [], //学员列表\n\t\t\t\tphoneKey: '',\n\t\t\t\tshowUserSelect: false,\n\t\t\t\tuserId: '', //学员id\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.shopId = uni.getStorageSync('nowShopId');\n\t\t\tthis.shopName = uni.getStorageSync('nowShopName');\n\t\t\tlet roles = uni.getStorageSync(\"userRoles\");\n\t\t\tif (roles.includes(\"shop_1_coach\") == true) {\n\t\t\t\tthis.isCoach = true;\n\t\t\t} else {\n\t\t\t\tthis.isCoach = false;\n\t\t\t}\n\t\t\tconst eventChannel = this.getOpenerEventChannel();\n\n\t\t\teventChannel.on(\"jiaoLianInfo\", (data) => {\n\t\t\t\tconsole.log('教练信息：', data);\n\t\t\t\tthis.activeDate = data.time;\n\t\t\t\tthis.courseId = data.courseId;\n\t\t\t\tthis.courseName = data.courseName;\n\t\t\t\tthis.memberId = data.coachId;\n\t\t\t\tthis.memberName = data.coachName;\n\t\t\t\tthis.loadData();\n\t\t\t});\n\t\t},\n\t\tonShow() {},\n\t\tmethods: {\n\t\t\t// 获取用户列表\n\t\t\tgetUserList() {\n\t\t\t\tapi.getUserList({\n\t\t\t\t\tphone: this.phoneKey\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t// this.userList = []\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.log(err);\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 点击学员,帮他预约\n\t\t\thandleUserItem(index) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: \"确认\",\n\t\t\t\t\tcontent: `确认给${this.userList[index].nickName}预约`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.bookingHelp(this.userList[index].memberId)\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tappointment() {\n\t\t\t\tconst wxUserInfo = uni.getStorageSync('wxUserInfo')\n\t\t\t\t// if (wxUserInfo.subscribeFlag != 1) {\n\t\t\t\t// \twx.showToast({\n\t\t\t\t// \t\ttitle: \" 您还未关注公众号，如果您已经关注请取消后重新关注\",\n\t\t\t\t// \t\ticon: \"none\",\n\t\t\t\t// \t})\n\t\t\t\t// \tthis.showQrcode = true\n\t\t\t\t// \treturn\n\t\t\t\t// }\n\t\t\t\tif (!this.courseId) {\n\t\t\t\t\tthis.$u.toast('请先选择预约课程！');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.courseId) {\n\t\t\t\t\tthis.$u.toast('请先选择预约课程！');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.activeDate) {\n\t\t\t\t\tthis.$u.toast('请先选择预约日期！');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.disabled = true;\n\t\t\t\tlet time = this.activeDate + ' ' + this.courses[this.currentIndex].text;\n\t\t\t\tapi.postTrainerBooking({\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tbookingTime: time,\n\t\t\t\t\t\tcourseId: this.courseId,\n\t\t\t\t\t\tcourseName: this.courseName, //课程名\n\t\t\t\t\t\tcoachId: this.memberId, // 教练id\n\t\t\t\t\t\tcoachName: this.memberName, //教练名\n\t\t\t\t\t\tshopId: this.shopId, //场馆id\n\t\t\t\t\t\tshopName: this.shopName, //场馆名\n\t\t\t\t\t},\n\t\t\t\t\tmethod: 'POST'\n\t\t\t\t}).then((res) => {\n\t\t\t\t\tthis.disabled = false;\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tthis.$u.toast(\"预约成功！\")\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t}, 2000)\n\t\t\t\t\t} else {\n\n\t\t\t\t\t};\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tthis.disabled = false;\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 帮学员预约\n\t\t\tappointmentHelp() {\n\t\t\t\t// const wxUserInfo = uni.getStorageSync('wxUserInfo')\n\t\t\t\t// if (wxUserInfo.subscribeFlag != 1) {\n\t\t\t\t// \twx.showToast({\n\t\t\t\t// \t\ttitle: \" 您还未关注公众号，如果您已经关注请取消后重新关注\",\n\t\t\t\t// \t\ticon: \"none\",\n\t\t\t\t// \t})\n\t\t\t\t// \tthis.showQrcode = true\n\t\t\t\t// \treturn\n\t\t\t\t// }\n\t\t\t\tif (!this.courseId) {\n\t\t\t\t\tthis.$u.toast('请先选择预约课程！');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.courseId) {\n\t\t\t\t\tthis.$u.toast('请先选择预约课程！');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.activeDate) {\n\t\t\t\t\tthis.$u.toast('请先选择预约日期！');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.showUserSelect = true\n\t\t\t},\n\t\t\t// 帮学员预约\n\t\t\tbookingHelp(userId) {\n\t\t\t\tthis.disabled = true;\n\t\t\t\tlet time = this.activeDate + ' ' + this.courses[this.currentIndex].text;\n\t\t\t\tapi.bookingHelp({\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tbookingTime: time,\n\t\t\t\t\t\tcourseId: this.courseId,\n\t\t\t\t\t\tcourseName: this.courseName, //课程名\n\t\t\t\t\t\tcoachId: this.memberId, // 教练id\n\t\t\t\t\t\tcoachName: this.memberName, //教练名\n\t\t\t\t\t\tshopId: this.shopId, //场馆id\n\t\t\t\t\t\tshopName: this.shopName, //场馆名\n\t\t\t\t\t\tmemberId: userId, //会员id\n\t\t\t\t\t},\n\t\t\t\t\tmethod: 'POST'\n\t\t\t\t}).then((res) => {\n\t\t\t\t\tthis.disabled = false;\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tthis.$u.toast(\"预约成功！\")\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t}, 2000)\n\t\t\t\t\t} else {\n\n\t\t\t\t\t};\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tthis.disabled = false;\n\t\t\t\t})\n\t\t\t},\n\t\t\tchangeActive(date) {\n\t\t\t\tthis.activeDate = date;\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.totalPages = 1;\n\t\t\t\tconsole.log(this.activeDate);\n\t\t\t\tthis.loadData();\n\t\t\t},\n\t\t\tchangeCurrentIndex(index, status) {\n\t\t\t\tif (status === 1) {\n\t\t\t\t\tthis.currentIndex = index;\n\t\t\t\t}\n\t\t\t},\n\t\t\ttoBuyHuiYuanKa() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/pages/huiYuanKa/index?id=\" + this.shopId,\n\t\t\t\t});\n\t\t\t},\n\t\t\tloadData() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\tmask: true,\n\t\t\t\t\ttitle: '数据加载中，请稍后……'\n\t\t\t\t})\n\t\t\t\tapi.getTrainerDetails({\n\t\t\t\t\tcourseId: this.courseId,\n\t\t\t\t\tmethod: 'GET'\n\t\t\t\t}).then((res) => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tthis.detail = res.data\n\t\t\t\t\t\t// 获取教练详情\n\t\t\t\t\t\tapi.getCoachDetails({\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tmemberId: this.memberId,\n\t\t\t\t\t\t\t\tshopId: this.shopId\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}).then((res) => {\n\t\t\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\t\t\tthis.dataProcessing(res.data);\n\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t})\n\t\t\t},\n\t\t\tdataProcessing(list) {\n\t\t\t\t// 等于1能点，等于2超时，等于3预约满了，等于4休息\n\t\t\t\tlet workStartTime = list.workStartTime;\n\t\t\t\tlet workEndTime = list.workEndTime;\n\t\t\t\tlet startIdx = 44;\n\t\t\t\tlet endIdx = 0;\n\t\t\t\tlet currentTime = new Date();\n\t\t\t\tlet hours = currentTime.getHours();\n\t\t\t\tlet minutes = currentTime.getMinutes();\n\t\t\t\tlet now = hours + ':00';\n\t\t\t\tconsole.log(workStartTime);\n\t\t\t\tconsole.log(workEndTime);\n\t\t\t\tfor (var i = 0; i < this.courses.length; i++) {\n\t\t\t\t\t// 如果教练休息，那么所有时间都是休息\n\t\t\t\t\tif (list.isReset == 'Y') {\n\t\t\t\t\t\tthis.courses[i].status = 4\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (workStartTime > this.courses[i].text) {\n\t\t\t\t\t\t\tconsole.log(777);\n\t\t\t\t\t\t\tthis.courses[i].status = 4\n\t\t\t\t\t\t} else if (workEndTime < this.courses[i].text) {\n\t\t\t\t\t\t\tthis.courses[i].status = 4\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 点击公众号\n\t\t\thandleOfficial() {\n\t\t\t\tthis.showQrcode = true\n\t\t\t},\n\t\t\t// 公众号弹窗关闭,重新获取用户信息,判断是否关注\n\t\t\tonOfficialClose() {\n\t\t\t\tthis.showQrcode = false\n\t\t\t\t// 获取用户信息\n\t\t\t\tapi\n\t\t\t\t\t.getInfo({\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tcompanyId: 1,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tmethod: \"GET\",\n\t\t\t\t\t})\n\t\t\t\t\t.then((ret) => {\n\t\t\t\t\t\tif (ret.code == 200) {\n\t\t\t\t\t\t\tuni.setStorageSync(\"wxUserInfo\", ret.wxUser);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t}\n\t\t},\n\t};\n</script>\n<style lang=\"scss\">\n\t.courses-wrap {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(4, 1fr);\n\t}\n\n\t.course-item {\n\t\toverflow: hidden;\n\t\tbackground: var(--base-bg-color);\n\t}\n\n\t.course-item.disabled {\n\t\tbackground: #d1d1d1 !important;\n\t\tcolor: #666 !important;\n\t}\n\n\t.course-item.active {\n\t\tbackground: var(--button-light-bg-color);\n\t\tcolor: var(--button-text-color);\n\t}\n\n\t.course-item.expired.disabled {\n\t\t&::before {\n\t\t\tcontent: \"过\";\n\t\t\tposition: absolute;\n\t\t\ttext-align: end;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tfont-size: 24rpx;\n\t\t\twidth: 50%;\n\t\t\theight: 70%;\n\t\t\tclip-path: polygon(0 0, 100% 0, 100% 100%);\n\t\t\tbackground: #333;\n\t\t\tcolor: var(--button-text-color);\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t.course-item.full.disabled {\n\t\t&::before {\n\t\t\tcontent: \"满\";\n\t\t\tposition: absolute;\n\t\t\ttext-align: end;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tfont-size: 24rpx;\n\t\t\twidth: 50%;\n\t\t\theight: 70%;\n\t\t\tclip-path: polygon(0 0, 100% 0, 100% 100%);\n\t\t\tbackground: var(--button-light-bg-color);\n\t\t\tcolor: var(--button-text-color);\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t.course-item.reset {\n\t\t&::before {\n\t\t\tcontent: \"休\";\n\t\t\tposition: absolute;\n\t\t\ttext-align: end;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tfont-size: 24rpx;\n\t\t\twidth: 50%;\n\t\t\theight: 70%;\n\t\t\tclip-path: polygon(0 0, 100% 0, 100% 100%);\n\t\t\tbackground: #55aaff;\n\t\t\tcolor: var(--button-text-color);\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t.user-select {\n\t\twidth: 600rpx;\n\t\tbox-sizing: border-box;\n\t\tpadding: 40rpx;\n\t}\n</style>", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/yu-yue/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=5d890802&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/yu-yue/detail.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=5d890802&\""], "names": ["_api", "_interopRequireDefault", "require", "e", "__esModule", "default", "components", "calendar", "officialQrcode", "data", "list", "loading", "nickname", "currentIndex", "courses", "text", "id", "status", "course_id", "activeDate", "currentPage", "totalPages", "detail", "courseName", "courseId", "memberId", "memberName", "shopId", "shopName", "showQrcode", "isCoach", "userList", "phoneKey", "showUserSelect", "userId", "onLoad", "_this", "uni", "getStorageSync", "roles", "includes", "eventChannel", "getOpenerEventChannel", "on", "console", "log", "time", "coachId", "<PERSON><PERSON><PERSON>", "loadData", "onShow", "methods", "getUserList", "api", "phone", "then", "res", "catch", "err", "handleUserItem", "index", "_this2", "showModal", "title", "content", "concat", "nick<PERSON><PERSON>", "success", "confirm", "bookingHelp", "appointment", "_this3", "wxUserInfo", "$u", "toast", "disabled", "postTrainerBooking", "bookingTime", "method", "code", "setTimeout", "navigateBack", "appointmentHelp", "_this4", "changeActive", "date", "changeCurrentIndex", "toBuyHuiYuanKa", "navigateTo", "url", "_this5", "showLoading", "mask", "getTrainerDetails", "hideLoading", "getCoachDetails", "dataProcessing", "workStartTime", "workEndTime", "startIdx", "endIdx", "currentTime", "Date", "hours", "getHours", "minutes", "getMinutes", "now", "i", "length", "isReset", "handleOfficial", "onOfficialClose", "getInfo", "companyId", "ret", "setStorageSync", "wxUser", "_vue", "_detail", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "sourceRoot": ""}